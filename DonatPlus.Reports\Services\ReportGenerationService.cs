#if NET48
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
#endif
using DonatPlus.Core.Models;
using DonatPlus.Core.Services;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace DonatPlus.Reports.Services
{
    /// <summary>
    /// Rapor oluşturma servisi
    /// </summary>
    public class ReportGenerationService
    {
        private readonly ILogger<ReportGenerationService> _logger;
        private readonly GeometryCalculationService _geometryService;
        
        public ReportGenerationService(ILogger<ReportGenerationService> logger, GeometryCalculationService geometryService)
        {
            _logger = logger;
            _geometryService = geometryService;
            
            // EPPlus lisans ayarı
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }
        
        /// <summary>
        /// Yeşil defter raporu oluşturur
        /// </summary>
        public string GenerateGreenBookReport(RebarCalculationResult calculationResult, string outputPath)
        {
            try
            {
                var fileName = $"YesilDefter_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                var fullPath = Path.Combine(outputPath, fileName);
                
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Yeşil Defter");
                    
                    // Başlık
                    CreateHeader(worksheet, "YEŞİL DEFTER", calculationResult);
                    
                    // Tablo başlıkları
                    int row = 8;
                    var headers = new[] { "Sıra", "Element Tipi", "Çap (mm)", "Uzunluk (m)", "Adet", "Toplam Uzunluk (m)", "Birim Ağırlık (kg/m)", "Toplam Ağırlık (kg)", "Açıklama" };
                    
                    for (int col = 1; col <= headers.Length; col++)
                    {
                        worksheet.Cells[row, col].Value = headers[col - 1];
                        worksheet.Cells[row, col].Style.Font.Bold = true;
                        worksheet.Cells[row, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, col].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                        worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                    
                    // Veri satırları
                    row++;
                    int siraNo = 1;
                    
                    foreach (var rebar in calculationResult.RebarElements.OrderBy(r => r.ElementType).ThenBy(r => r.Diameter))
                    {
                        worksheet.Cells[row, 1].Value = siraNo++;
                        worksheet.Cells[row, 2].Value = rebar.ElementType;
                        worksheet.Cells[row, 3].Value = rebar.Diameter;
                        worksheet.Cells[row, 4].Value = rebar.Length;
                        worksheet.Cells[row, 5].Value = rebar.Quantity;
                        worksheet.Cells[row, 6].Value = rebar.TotalLength;
                        worksheet.Cells[row, 7].Value = rebar.Weight;
                        worksheet.Cells[row, 8].Value = rebar.TotalWeight;
                        worksheet.Cells[row, 9].Value = rebar.Description;
                        
                        // Satır kenarlıkları
                        for (int col = 1; col <= headers.Length; col++)
                        {
                            worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        }
                        
                        row++;
                    }
                    
                    // Toplam satırı
                    worksheet.Cells[row, 1].Value = "TOPLAM";
                    worksheet.Cells[row, 6].Value = calculationResult.TotalLength;
                    worksheet.Cells[row, 8].Value = calculationResult.TotalWeight;
                    
                    for (int col = 1; col <= headers.Length; col++)
                    {
                        worksheet.Cells[row, col].Style.Font.Bold = true;
                        worksheet.Cells[row, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, col].Style.Fill.BackgroundColor.SetColor(Color.Yellow);
                        worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                    
                    // Çaplara göre özet
                    CreateDiameterSummary(worksheet, calculationResult, row + 3);
                    
                    // Sütun genişliklerini ayarla
                    worksheet.Column(1).Width = 8;
                    worksheet.Column(2).Width = 15;
                    worksheet.Column(3).Width = 12;
                    worksheet.Column(4).Width = 12;
                    worksheet.Column(5).Width = 8;
                    worksheet.Column(6).Width = 15;
                    worksheet.Column(7).Width = 15;
                    worksheet.Column(8).Width = 15;
                    worksheet.Column(9).Width = 30;
                    
                    package.SaveAs(new FileInfo(fullPath));
                }
                
                _logger.LogInformation("Yeşil defter raporu oluşturuldu: {FilePath}", fullPath);
                return fullPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yeşil defter raporu oluşturma hatası");
                throw;
            }
        }
        
        /// <summary>
        /// Keşif özeti raporu oluşturur
        /// </summary>
        public string GenerateDiscoveryReport(RebarCalculationResult calculationResult, string outputPath)
        {
            try
            {
                var fileName = $"KesifOzeti_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                var fullPath = Path.Combine(outputPath, fileName);
                
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Keşif Özeti");
                    
                    // Başlık
                    CreateHeader(worksheet, "KEŞİF ÖZETİ", calculationResult);
                    
                    // Çaplara göre özet tablo
                    int row = 8;
                    var headers = new[] { "Çap (mm)", "Toplam Uzunluk (m)", "Toplam Ağırlık (kg)", "Birim Fiyat (TL/kg)", "Toplam Tutar (TL)" };
                    
                    for (int col = 1; col <= headers.Length; col++)
                    {
                        worksheet.Cells[row, col].Value = headers[col - 1];
                        worksheet.Cells[row, col].Style.Font.Bold = true;
                        worksheet.Cells[row, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, col].Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
                        worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                    
                    row++;
                    double totalCost = 0;
                    
                    foreach (var diameterGroup in calculationResult.WeightByDiameter.OrderBy(kvp => kvp.Key))
                    {
                        var diameter = diameterGroup.Key;
                        var weight = diameterGroup.Value;
                        var length = calculationResult.RebarElements
                            .Where(r => r.Diameter == diameter)
                            .Sum(r => r.TotalLength);
                        
                        // Basit fiyat hesabı (gerçek uygulamada fiyat listesinden alınmalı)
                        var unitPrice = GetUnitPrice(diameter);
                        var cost = weight * unitPrice;
                        totalCost += cost;
                        
                        worksheet.Cells[row, 1].Value = diameter;
                        worksheet.Cells[row, 2].Value = length;
                        worksheet.Cells[row, 3].Value = weight;
                        worksheet.Cells[row, 4].Value = unitPrice;
                        worksheet.Cells[row, 5].Value = cost;
                        
                        for (int col = 1; col <= headers.Length; col++)
                        {
                            worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        }
                        
                        row++;
                    }
                    
                    // Toplam satırı
                    worksheet.Cells[row, 1].Value = "TOPLAM";
                    worksheet.Cells[row, 2].Value = calculationResult.TotalLength;
                    worksheet.Cells[row, 3].Value = calculationResult.TotalWeight;
                    worksheet.Cells[row, 5].Value = totalCost;
                    
                    for (int col = 1; col <= headers.Length; col++)
                    {
                        worksheet.Cells[row, col].Style.Font.Bold = true;
                        worksheet.Cells[row, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, col].Style.Fill.BackgroundColor.SetColor(Color.Yellow);
                        worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                    
                    // Element tiplerine göre dağılım
                    CreateElementTypeSummary(worksheet, calculationResult, row + 3);
                    
                    // Sütun genişliklerini ayarla
                    for (int col = 1; col <= headers.Length; col++)
                    {
                        worksheet.Column(col).Width = 18;
                    }
                    
                    package.SaveAs(new FileInfo(fullPath));
                }
                
                _logger.LogInformation("Keşif özeti raporu oluşturuldu: {FilePath}", fullPath);
                return fullPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Keşif özeti raporu oluşturma hatası");
                throw;
            }
        }
        
        /// <summary>
        /// Kesim listesi raporu oluşturur
        /// </summary>
        public string GenerateCuttingListReport(RebarCalculationResult calculationResult, string outputPath)
        {
            try
            {
                var fileName = $"KesimListesi_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                var fullPath = Path.Combine(outputPath, fileName);
                
                // Kesim optimizasyonu yap
                var cuttingPlans = _geometryService.OptimizeCutting(calculationResult.RebarElements);
                
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Kesim Listesi");
                    
                    // Başlık
                    CreateHeader(worksheet, "KESİM LİSTESİ", calculationResult);
                    
                    int row = 8;
                    
                    foreach (var plan in cuttingPlans.OrderBy(p => p.Diameter))
                    {
                        // Çap başlığı
                        worksheet.Cells[row, 1].Value = $"ø{plan.Diameter}mm - Standart Boy: {plan.StandardLength}m";
                        worksheet.Cells[row, 1, row, 6].Merge = true;
                        worksheet.Cells[row, 1].Style.Font.Bold = true;
                        worksheet.Cells[row, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                        row++;
                        
                        // Tablo başlıkları
                        var headers = new[] { "Çubuk No", "Parça 1", "Parça 2", "Parça 3", "Parça 4", "Fire (m)" };
                        for (int col = 1; col <= headers.Length; col++)
                        {
                            worksheet.Cells[row, col].Value = headers[col - 1];
                            worksheet.Cells[row, col].Style.Font.Bold = true;
                            worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        }
                        row++;
                        
                        // Kesim detayları
                        for (int i = 0; i < plan.Cuts.Count; i++)
                        {
                            var cut = plan.Cuts[i];
                            worksheet.Cells[row, 1].Value = i + 1;
                            
                            for (int j = 0; j < Math.Min(cut.Pieces.Count, 4); j++)
                            {
                                worksheet.Cells[row, j + 2].Value = cut.Pieces[j];
                            }
                            
                            worksheet.Cells[row, 6].Value = cut.WasteLength;
                            
                            for (int col = 1; col <= headers.Length; col++)
                            {
                                worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                            }
                            row++;
                        }
                        
                        // Özet bilgiler
                        worksheet.Cells[row, 1].Value = "Toplam Çubuk Sayısı:";
                        worksheet.Cells[row, 2].Value = plan.Cuts.Count;
                        worksheet.Cells[row, 3].Value = "Toplam Fire:";
                        worksheet.Cells[row, 4].Value = plan.TotalWaste;
                        worksheet.Cells[row, 5].Value = "Fire Oranı:";
                        worksheet.Cells[row, 6].Value = $"%{plan.WastePercentage:F1}";
                        
                        for (int col = 1; col <= headers.Length; col++)
                        {
                            worksheet.Cells[row, col].Style.Font.Bold = true;
                        }
                        
                        row += 3; // Boşluk bırak
                    }
                    
                    // Sütun genişliklerini ayarla
                    for (int col = 1; col <= 6; col++)
                    {
                        worksheet.Column(col).Width = 15;
                    }
                    
                    package.SaveAs(new FileInfo(fullPath));
                }
                
                _logger.LogInformation("Kesim listesi raporu oluşturuldu: {FilePath}", fullPath);
                return fullPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Kesim listesi raporu oluşturma hatası");
                throw;
            }
        }
        
        /// <summary>
        /// Sipariş listesi raporu oluşturur
        /// </summary>
        public string GenerateOrderListReport(RebarCalculationResult calculationResult, string outputPath)
        {
            try
            {
                var fileName = $"SiparisListesi_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                var fullPath = Path.Combine(outputPath, fileName);
                
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sipariş Listesi");
                    
                    // Başlık
                    CreateHeader(worksheet, "SİPARİŞ LİSTESİ", calculationResult);
                    
                    // Tablo başlıkları
                    int row = 8;
                    var headers = new[] { "Malzeme", "Çap (mm)", "Boy (m)", "Adet", "Toplam Ağırlık (kg)", "Birim", "Açıklama" };
                    
                    for (int col = 1; col <= headers.Length; col++)
                    {
                        worksheet.Cells[row, col].Value = headers[col - 1];
                        worksheet.Cells[row, col].Style.Font.Bold = true;
                        worksheet.Cells[row, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, col].Style.Fill.BackgroundColor.SetColor(Color.LightGreen);
                        worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                    
                    row++;
                    
                    // Çaplara göre sipariş miktarları
                    foreach (var diameterGroup in calculationResult.WeightByDiameter.OrderBy(kvp => kvp.Key))
                    {
                        var diameter = diameterGroup.Key;
                        var totalWeight = diameterGroup.Value;
                        var standardLength = 12.0; // Standart çubuk boyu
                        var requiredBars = Math.Ceiling(totalWeight / GetStandardBarWeight(diameter, standardLength));
                        
                        worksheet.Cells[row, 1].Value = "Nervürlü Çelik";
                        worksheet.Cells[row, 2].Value = diameter;
                        worksheet.Cells[row, 3].Value = standardLength;
                        worksheet.Cells[row, 4].Value = requiredBars;
                        worksheet.Cells[row, 5].Value = totalWeight;
                        worksheet.Cells[row, 6].Value = "kg";
                        worksheet.Cells[row, 7].Value = $"S420 Sınıfı - ø{diameter}mm";
                        
                        for (int col = 1; col <= headers.Length; col++)
                        {
                            worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        }
                        
                        row++;
                    }
                    
                    // Hasır siparişleri
                    var wireMeshElements = calculationResult.RebarElements.Where(r => r.IsWireMesh).ToList();
                    if (wireMeshElements.Any())
                    {
                        row++; // Boşluk
                        
                        worksheet.Cells[row, 1].Value = "ÇELİK HASIR";
                        worksheet.Cells[row, 1, row, headers.Length].Merge = true;
                        worksheet.Cells[row, 1].Style.Font.Bold = true;
                        worksheet.Cells[row, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[row, 1].Style.Fill.BackgroundColor.SetColor(Color.Orange);
                        row++;
                        
                        foreach (var mesh in wireMeshElements)
                        {
                            worksheet.Cells[row, 1].Value = "Çelik Hasır";
                            worksheet.Cells[row, 2].Value = mesh.Diameter;
                            worksheet.Cells[row, 3].Value = $"{mesh.MeshWidth:F2}x{mesh.MeshHeight:F2}";
                            worksheet.Cells[row, 4].Value = mesh.Quantity;
                            worksheet.Cells[row, 5].Value = mesh.TotalWeight;
                            worksheet.Cells[row, 6].Value = "m²";
                            worksheet.Cells[row, 7].Value = $"Aralık: {mesh.MeshSpacingX:F0}x{mesh.MeshSpacingY:F0}cm";
                            
                            for (int col = 1; col <= headers.Length; col++)
                            {
                                worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                            }
                            
                            row++;
                        }
                    }
                    
                    // Sütun genişliklerini ayarla
                    worksheet.Column(1).Width = 15;
                    worksheet.Column(2).Width = 12;
                    worksheet.Column(3).Width = 12;
                    worksheet.Column(4).Width = 10;
                    worksheet.Column(5).Width = 15;
                    worksheet.Column(6).Width = 8;
                    worksheet.Column(7).Width = 30;
                    
                    package.SaveAs(new FileInfo(fullPath));
                }
                
                _logger.LogInformation("Sipariş listesi raporu oluşturuldu: {FilePath}", fullPath);
                return fullPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Sipariş listesi raporu oluşturma hatası");
                throw;
            }
        }
        
        private void CreateHeader(ExcelWorksheet worksheet, string title, RebarCalculationResult calculationResult)
        {
            // Ana başlık
            worksheet.Cells[1, 1].Value = title;
            worksheet.Cells[1, 1, 1, 6].Merge = true;
            worksheet.Cells[1, 1].Style.Font.Size = 16;
            worksheet.Cells[1, 1].Style.Font.Bold = true;
            worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            
            // Proje bilgileri
            worksheet.Cells[3, 1].Value = "Proje:";
            worksheet.Cells[3, 2].Value = calculationResult.ProjectName;
            worksheet.Cells[4, 1].Value = "Hesaplayan:";
            worksheet.Cells[4, 2].Value = calculationResult.CalculatedBy;
            worksheet.Cells[5, 1].Value = "Tarih:";
            worksheet.Cells[5, 2].Value = calculationResult.CalculationDate.ToString("dd.MM.yyyy HH:mm");
            
            // Özet bilgiler
            worksheet.Cells[3, 4].Value = "Toplam Ağırlık:";
            worksheet.Cells[3, 5].Value = $"{calculationResult.TotalWeight:F2} kg";
            worksheet.Cells[4, 4].Value = "Toplam Uzunluk:";
            worksheet.Cells[4, 5].Value = $"{calculationResult.TotalLength:F2} m";
            worksheet.Cells[5, 4].Value = "Element Sayısı:";
            worksheet.Cells[5, 5].Value = calculationResult.RebarElements.Count;
        }
        
        private void CreateDiameterSummary(ExcelWorksheet worksheet, RebarCalculationResult calculationResult, int startRow)
        {
            worksheet.Cells[startRow, 1].Value = "ÇAPLARA GÖRE ÖZET";
            worksheet.Cells[startRow, 1, startRow, 4].Merge = true;
            worksheet.Cells[startRow, 1].Style.Font.Bold = true;
            
            startRow += 2;
            var headers = new[] { "Çap (mm)", "Toplam Uzunluk (m)", "Toplam Ağırlık (kg)", "Oran (%)" };
            
            for (int col = 1; col <= headers.Length; col++)
            {
                worksheet.Cells[startRow, col].Value = headers[col - 1];
                worksheet.Cells[startRow, col].Style.Font.Bold = true;
                worksheet.Cells[startRow, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }
            
            startRow++;
            
            foreach (var diameterGroup in calculationResult.WeightByDiameter.OrderBy(kvp => kvp.Key))
            {
                var percentage = (diameterGroup.Value / calculationResult.TotalWeight) * 100;
                
                worksheet.Cells[startRow, 1].Value = diameterGroup.Key;
                worksheet.Cells[startRow, 2].Value = calculationResult.RebarElements
                    .Where(r => r.Diameter == diameterGroup.Key)
                    .Sum(r => r.TotalLength);
                worksheet.Cells[startRow, 3].Value = diameterGroup.Value;
                worksheet.Cells[startRow, 4].Value = percentage;
                
                for (int col = 1; col <= headers.Length; col++)
                {
                    worksheet.Cells[startRow, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }
                
                startRow++;
            }
        }
        
        private void CreateElementTypeSummary(ExcelWorksheet worksheet, RebarCalculationResult calculationResult, int startRow)
        {
            worksheet.Cells[startRow, 1].Value = "ELEMENT TİPLERİNE GÖRE DAĞILIM";
            worksheet.Cells[startRow, 1, startRow, 3].Merge = true;
            worksheet.Cells[startRow, 1].Style.Font.Bold = true;
            
            startRow += 2;
            var headers = new[] { "Element Tipi", "Toplam Ağırlık (kg)", "Oran (%)" };
            
            for (int col = 1; col <= headers.Length; col++)
            {
                worksheet.Cells[startRow, col].Value = headers[col - 1];
                worksheet.Cells[startRow, col].Style.Font.Bold = true;
                worksheet.Cells[startRow, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }
            
            startRow++;
            
            foreach (var typeGroup in calculationResult.WeightByElementType.OrderByDescending(kvp => kvp.Value))
            {
                var percentage = (typeGroup.Value / calculationResult.TotalWeight) * 100;
                
                worksheet.Cells[startRow, 1].Value = typeGroup.Key;
                worksheet.Cells[startRow, 2].Value = typeGroup.Value;
                worksheet.Cells[startRow, 3].Value = percentage;
                
                for (int col = 1; col <= headers.Length; col++)
                {
                    worksheet.Cells[startRow, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }
                
                startRow++;
            }
        }
        
        private double GetUnitPrice(double diameter)
        {
            // Basit fiyat tablosu (gerçek uygulamada veritabanından alınmalı)
            return diameter switch
            {
                <= 10 => 25.0,
                <= 16 => 24.0,
                <= 20 => 23.5,
                <= 25 => 23.0,
                _ => 22.5
            };
        }
        
        private double GetStandardBarWeight(double diameter, double length)
        {
            // Standart çubuk ağırlığı hesapla
            var weightPerMeter = Math.PI * Math.Pow(diameter / 2000, 2) * 7850; // kg/m
            return weightPerMeter * length;
        }
    }
}
