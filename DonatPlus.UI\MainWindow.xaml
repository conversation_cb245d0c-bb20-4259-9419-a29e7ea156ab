<Window x:Class="DonatPlus.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="DonatPlus - Betonarme Donatı Metraj Hesaplama" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <Style x:Key="MenuButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Üst Menü -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
            <DockPanel>
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                    <materialDesign:PackIcon Kind="Calculator" Width="32" Height="32" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="DonatPlus" FontSize="20" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock Text="v1.0" FontSize="12" VerticalAlignment="Bottom" Margin="5,0,0,2" Opacity="0.7"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button x:Name="BtnMinimize" Style="{StaticResource MaterialDesignToolButton}" 
                            Click="BtnMinimize_Click" ToolTip="Küçült">
                        <materialDesign:PackIcon Kind="WindowMinimize"/>
                    </Button>
                    <Button x:Name="BtnMaximize" Style="{StaticResource MaterialDesignToolButton}" 
                            Click="BtnMaximize_Click" ToolTip="Büyüt">
                        <materialDesign:PackIcon Kind="WindowMaximize"/>
                    </Button>
                    <Button x:Name="BtnClose" Style="{StaticResource MaterialDesignToolButton}" 
                            Click="BtnClose_Click" ToolTip="Kapat">
                        <materialDesign:PackIcon Kind="Close"/>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- Ana İçerik -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sol Menü -->
            <materialDesign:ColorZone Grid.Column="0" Mode="Light" Padding="10">
                <StackPanel>
                    <TextBlock Text="Ana Menü" FontSize="16" FontWeight="Bold" Margin="10,0,0,20"/>
                    
                    <Button x:Name="BtnNewProject" Style="{StaticResource MenuButtonStyle}" 
                            Click="BtnNewProject_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FolderPlus" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="Yeni Proje"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="BtnOpenProject" Style="{StaticResource MenuButtonStyle}" 
                            Click="BtnOpenProject_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FolderOpen" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="Proje Aç"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="BtnAnalyzeDrawing" Style="{StaticResource MenuButtonStyle}" 
                            Click="BtnAnalyzeDrawing_Click" IsEnabled="False">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileAnalyze" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="Çizim Analizi"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="BtnCalculations" Style="{StaticResource MenuButtonStyle}" 
                            Click="BtnCalculations_Click" IsEnabled="False">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Calculator" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="Hesaplamalar"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="BtnReports" Style="{StaticResource MenuButtonStyle}" 
                            Click="BtnReports_Click" IsEnabled="False">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="Raporlar"/>
                        </StackPanel>
                    </Button>
                    
                    <Separator Margin="10,20"/>
                    
                    <Button x:Name="BtnSettings" Style="{StaticResource MenuButtonStyle}" 
                            Click="BtnSettings_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Settings" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="Ayarlar"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="BtnHelp" Style="{StaticResource MenuButtonStyle}" 
                            Click="BtnHelp_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Help" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="Yardım"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </materialDesign:ColorZone>

            <!-- Ana İçerik Alanı -->
            <Grid Grid.Column="1" Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Proje Bilgileri -->
                <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock x:Name="TxtProjectName" Text="Proje seçilmedi" FontSize="18" FontWeight="Bold"/>
                            <TextBlock x:Name="TxtProjectInfo" Text="Yeni bir proje oluşturun veya mevcut bir projeyi açın" 
                                     FontSize="12" Opacity="0.7" Margin="0,5,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button x:Name="BtnQuickAnalyze" Style="{StaticResource MaterialDesignRaisedAccentButton}" 
                                    Content="Hızlı Analiz" Margin="10,0" IsEnabled="False" 
                                    Click="BtnQuickAnalyze_Click"/>
                            <Button x:Name="BtnSaveProject" Style="{StaticResource MaterialDesignRaisedButton}" 
                                    Content="Kaydet" IsEnabled="False" 
                                    Click="BtnSaveProject_Click"/>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>

                <!-- İçerik Alanı -->
                <TabControl x:Name="MainTabControl" Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
                    
                    <!-- Hoş Geldiniz Sekmesi -->
                    <TabItem Header="Hoş Geldiniz" x:Name="TabWelcome">
                        <ScrollViewer>
                            <StackPanel Margin="20">
                                <TextBlock Text="DonatPlus'a Hoş Geldiniz!" FontSize="24" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,0,0,30"/>
                                
                                <materialDesign:Card Padding="20" Margin="0,0,0,20">
                                    <StackPanel>
                                        <TextBlock Text="Özellikler" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                                        
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0" Margin="0,0,20,0">
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                    <materialDesign:PackIcon Kind="Check" Foreground="Green" Margin="0,0,10,0"/>
                                                    <TextBlock Text="AutoCAD 2010-2025 desteği"/>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                    <materialDesign:PackIcon Kind="Check" Foreground="Green" Margin="0,0,10,0"/>
                                                    <TextBlock Text="Otomatik donatı algılama"/>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                    <materialDesign:PackIcon Kind="Check" Foreground="Green" Margin="0,0,10,0"/>
                                                    <TextBlock Text="Çelik hasır analizi"/>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                    <materialDesign:PackIcon Kind="Check" Foreground="Green" Margin="0,0,10,0"/>
                                                    <TextBlock Text="Geometrik hesaplamalar"/>
                                                </StackPanel>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="1">
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                    <materialDesign:PackIcon Kind="Check" Foreground="Green" Margin="0,0,10,0"/>
                                                    <TextBlock Text="Yeşil defter raporu"/>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                    <materialDesign:PackIcon Kind="Check" Foreground="Green" Margin="0,0,10,0"/>
                                                    <TextBlock Text="Keşif özeti"/>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                    <materialDesign:PackIcon Kind="Check" Foreground="Green" Margin="0,0,10,0"/>
                                                    <TextBlock Text="Kesim listesi"/>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                    <materialDesign:PackIcon Kind="Check" Foreground="Green" Margin="0,0,10,0"/>
                                                    <TextBlock Text="Sipariş listesi"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>
                                </materialDesign:Card>
                                
                                <materialDesign:Card Padding="20">
                                    <StackPanel>
                                        <TextBlock Text="Başlangıç" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                                        <TextBlock Text="1. Yeni bir proje oluşturun veya mevcut bir projeyi açın" Margin="0,0,0,5"/>
                                        <TextBlock Text="2. AutoCAD çizimlerinizi projeye ekleyin" Margin="0,0,0,5"/>
                                        <TextBlock Text="3. Çizim analizi yaparak donatı bilgilerini çıkarın" Margin="0,0,0,5"/>
                                        <TextBlock Text="4. Hesaplama sonuçlarını kontrol edin ve raporları oluşturun" Margin="0,0,0,5"/>
                                    </StackPanel>
                                </materialDesign:Card>
                            </StackPanel>
                        </ScrollViewer>
                    </TabItem>
                    
                </TabControl>
            </Grid>
        </Grid>

        <!-- Alt Durum Çubuğu -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="TxtStatus" Text="Hazır" VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="Veritabanı: " VerticalAlignment="Center"/>
                    <Ellipse x:Name="DbStatusIndicator" Width="10" Height="10" Fill="Green" Margin="5,0,15,0"/>
                    <TextBlock x:Name="TxtCurrentTime" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>
