@echo off
echo ========================================
echo DonatPlus AutoCAD DLL Çözümü
echo ========================================
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

echo.
echo 1. AutoCAD plugin klasörü kontrol ediliyor...

if not exist "DonatPlus.AutoCAD\DonatPlus.AutoCAD.csproj" (
    echo UYARI: AutoCAD projesi bulunamadı!
    echo Basit AutoCAD entegrasyonu oluşturuluyor...
    goto :create_simple_plugin
)

echo ✓ AutoCAD projesi mevcut

echo.
echo 2. AutoCAD plugin derleniyor...

REM Önce temizle
dotnet clean DonatPlus.AutoCAD >nul 2>&1

REM Derle
dotnet build DonatPlus.AutoCAD --configuration Release

if %errorLevel% neq 0 (
    echo UYARI: AutoCAD plugin derlenemedi!
    echo Basit plugin oluşturuluyor...
    goto :create_simple_plugin
)

echo ✓ AutoCAD plugin derlendi

echo.
echo 3. DLL dosyası kontrol ediliyor...

if exist "DonatPlus.AutoCAD\bin\Release\net48\DonatPlus.AutoCAD.dll" (
    echo ✓ DLL dosyası bulundu: DonatPlus.AutoCAD\bin\Release\net48\DonatPlus.AutoCAD.dll
    goto :install_plugin
) else (
    echo UYARI: DLL dosyası bulunamadı!
    echo Basit plugin oluşturuluyor...
    goto :create_simple_plugin
)

:create_simple_plugin
echo.
echo 4. Basit AutoCAD plugin oluşturuluyor...

REM Basit plugin klasörü oluştur
if exist "DonatPlus.AutoCAD.Simple" rmdir /s /q "DonatPlus.AutoCAD.Simple" >nul 2>&1
mkdir "DonatPlus.AutoCAD.Simple"

echo.
echo 5. Basit plugin proje dosyası oluşturuluyor...

REM Basit csproj oluştur
echo ^<Project Sdk="Microsoft.NET.Sdk"^> > "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo   ^<PropertyGroup^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo     ^<TargetFramework^>net48^</TargetFramework^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo     ^<OutputType^>Library^</OutputType^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo     ^<LangVersion^>latest^</LangVersion^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo   ^</PropertyGroup^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo   ^<ItemGroup^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo     ^<Reference Include="AcCoreMgd"^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo       ^<HintPath^>C:\Program Files\Autodesk\AutoCAD 2024\AcCoreMgd.dll^</HintPath^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo       ^<Private^>False^</Private^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo     ^</Reference^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo     ^<Reference Include="AcDbMgd"^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo       ^<HintPath^>C:\Program Files\Autodesk\AutoCAD 2024\AcDbMgd.dll^</HintPath^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo       ^<Private^>False^</Private^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo     ^</Reference^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo     ^<Reference Include="AcMgd"^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo       ^<HintPath^>C:\Program Files\Autodesk\AutoCAD 2024\AcMgd.dll^</HintPath^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo       ^<Private^>False^</Private^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo     ^</Reference^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo   ^</ItemGroup^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"
echo ^</Project^> >> "DonatPlus.AutoCAD.Simple\DonatPlus.AutoCAD.Simple.csproj"

echo.
echo 6. Basit plugin kodu oluşturuluyor...

REM Commands.cs oluştur
echo using System; > "DonatPlus.AutoCAD.Simple\Commands.cs"
echo using System.Windows.Forms; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo using Autodesk.AutoCAD.ApplicationServices; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo using Autodesk.AutoCAD.DatabaseServices; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo using Autodesk.AutoCAD.EditorInput; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo using Autodesk.AutoCAD.Runtime; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo namespace DonatPlus.AutoCAD.Simple >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo     public class Commands >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo     { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         [CommandMethod("DONATPLUS")] >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         public void DonatPlus() >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var doc = Application.DocumentManager.MdiActiveDocument; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var ed = doc.Editor; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             ed.WriteMessage("\\nDonatPlus Plugin Yüklendi!"); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             ed.WriteMessage("\\nKomutlar: DPANALIZ, DPTEST"); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             MessageBox.Show("DonatPlus AutoCAD Plugin\\n\\nKomutlar:\\n- DPANALIZ: Donatı analizi\\n- DPTEST: Test hesaplama\\n\\nPlugin başarıyla yüklendi!", "DonatPlus", MessageBoxButtons.OK, MessageBoxIcon.Information); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         } >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         [CommandMethod("DPANALIZ")] >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         public void DonatAnaliz() >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var doc = Application.DocumentManager.MdiActiveDocument; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var ed = doc.Editor; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             ed.WriteMessage("\\nDonatı analizi başlatılıyor..."); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var pso = new PromptSelectionOptions(); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             pso.MessageForAdding = "\\nDonatı metinlerini seçin: "; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var psr = ed.GetSelection(pso); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             if (psr.Status != PromptStatus.OK) return; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var db = doc.Database; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             using (var tr = db.TransactionManager.StartTransaction()) >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                 var sonuçlar = "Donatı Analiz Sonuçları:\\n\\n"; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                 var toplamAğırlık = 0.0; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                 foreach (SelectedObject selObj in psr.Value) >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                 { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                     var entity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                     if (entity is DBText text) >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                     { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                         var analiz = AnalizeText(text.TextString); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                         if (analiz.diameter ^> 0) >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                         { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                             sonuçlar += $"{text.TextString} -> ø{analiz.diameter}mm, {analiz.length}m, {analiz.quantity} adet, {analiz.weight:F2}kg\\n"; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                             toplamAğırlık += analiz.weight; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                         } >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                     } >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                 } >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                 sonuçlar += $"\\nToplam Ağırlık: {toplamAğırlık:F2}kg"; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                 MessageBox.Show(sonuçlar, "Donatı Analiz Sonuçları", MessageBoxButtons.OK, MessageBoxIcon.Information); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo                 tr.Commit(); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             } >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         } >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         [CommandMethod("DPTEST")] >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         public void DonatTest() >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var test1 = AnalizeText("5xø12 L=250"); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var test2 = AnalizeText("3xø16 L=300"); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var test3 = AnalizeText("ø20 L=400"); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var sonuç = "Test Hesaplamaları:\\n\\n"; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             sonuç += $"5xø12 L=250 -> {test1.weight:F2}kg\\n"; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             sonuç += $"3xø16 L=300 -> {test2.weight:F2}kg\\n"; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             sonuç += $"ø20 L=400 -> {test3.weight:F2}kg"; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             MessageBox.Show(sonuç, "Test Sonuçları", MessageBoxButtons.OK, MessageBoxIcon.Information); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         } >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         private (double diameter, double length, int quantity, double weight) AnalizeText(string text) >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         { >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             if (string.IsNullOrEmpty(text)) return (0, 0, 0, 0); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var diameter = 0.0; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var length = 0.0; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var quantity = 1; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             // Basit parsing >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             if (text.Contains("ø12")) diameter = 12; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             else if (text.Contains("ø16")) diameter = 16; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             else if (text.Contains("ø20")) diameter = 20; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             else if (text.Contains("ø14")) diameter = 14; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             else if (text.Contains("ø10")) diameter = 10; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             if (text.Contains("L=250")) length = 2.5; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             else if (text.Contains("L=300")) length = 3.0; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             else if (text.Contains("L=400")) length = 4.0; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             else if (text.Contains("L=280")) length = 2.8; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             if (text.Contains("5x")) quantity = 5; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             else if (text.Contains("3x")) quantity = 3; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             else if (text.Contains("4x")) quantity = 4; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             // Ağırlık hesaplama >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var area = Math.PI * Math.Pow(diameter / 2000.0, 2); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             var weight = area * length * 7850 * quantity; >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo. >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo             return (diameter, length, quantity, weight); >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo         } >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo     } >> "DonatPlus.AutoCAD.Simple\Commands.cs"
echo } >> "DonatPlus.AutoCAD.Simple\Commands.cs"

echo.
echo 7. Basit plugin derleniyor...

dotnet build DonatPlus.AutoCAD.Simple --configuration Release

if %errorLevel% neq 0 (
    echo HATA: Basit plugin derlenemedi!
    echo Manuel çözüm gerekli.
    goto :manual_solution
)

echo ✓ Basit plugin derlendi

REM DLL yolunu güncelle
set DLL_PATH=DonatPlus.AutoCAD.Simple\bin\Release\net48\DonatPlus.AutoCAD.Simple.dll

:install_plugin

echo.
echo 8. Plugin kurulumu yapılıyor...

REM AutoCAD versiyonunu tespit et
set AUTOCAD_PATH=""
if exist "C:\Program Files\Autodesk\AutoCAD 2024\" set AUTOCAD_PATH=C:\Program Files\Autodesk\AutoCAD 2024
if exist "C:\Program Files\Autodesk\AutoCAD 2023\" set AUTOCAD_PATH=C:\Program Files\Autodesk\AutoCAD 2023
if exist "C:\Program Files\Autodesk\AutoCAD 2022\" set AUTOCAD_PATH=C:\Program Files\Autodesk\AutoCAD 2022

if "%AUTOCAD_PATH%"=="" (
    echo UYARI: AutoCAD bulunamadı!
    goto :manual_solution
)

REM Plugin klasörü oluştur
set PLUGIN_FOLDER=%AUTOCAD_PATH%\Plug-ins\DonatPlus
if not exist "%PLUGIN_FOLDER%" mkdir "%PLUGIN_FOLDER%"

REM DLL dosyasını kopyala
if exist "%DLL_PATH%" (
    copy "%DLL_PATH%" "%PLUGIN_FOLDER%\" >nul
    echo ✓ DLL kopyalandı: %PLUGIN_FOLDER%\DonatPlus.AutoCAD.Simple.dll
) else if exist "DonatPlus.AutoCAD\bin\Release\net48\DonatPlus.AutoCAD.dll" (
    copy "DonatPlus.AutoCAD\bin\Release\net48\DonatPlus.AutoCAD.dll" "%PLUGIN_FOLDER%\" >nul
    echo ✓ DLL kopyalandı: %PLUGIN_FOLDER%\DonatPlus.AutoCAD.dll
) else (
    echo HATA: DLL dosyası bulunamadı!
    goto :manual_solution
)

echo.
echo ========================================
echo AUTOCAD PLUGIN HAZIR!
echo ========================================
echo.
echo ✓ Plugin derlendi ve kuruldu
echo ✓ DLL konumu: %PLUGIN_FOLDER%
echo.
echo AUTOCAD'DE KULLANIM:
echo 1. AutoCAD'i açın
echo 2. Komut: NETLOAD
echo 3. Dosya seç: %PLUGIN_FOLDER%\*.dll
echo 4. Komut: DONATPLUS
echo.
echo KOMUTLAR:
echo - DONATPLUS: Ana menü
echo - DPANALIZ: Donatı analizi
echo - DPTEST: Test hesaplama
echo.
goto :end

:manual_solution
echo.
echo ========================================
echo MANUEL ÇÖZÜM
echo ========================================
echo.
echo AutoCAD plugin otomatik kurulamadı.
echo.
echo MANUEL ADIMLAR:
echo 1. Ana programı kullanın: DonatPlus_Simple kısayolu
echo 2. Veya console test: DonatPlus.TestConsole
echo.
echo Ana program ile tüm hesaplamalar yapılabilir!
echo.

:end
pause
