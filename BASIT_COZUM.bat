@echo off
echo ========================================
echo DonatPlus Basit Ç<PERSON>ü<PERSON> (Launcher'sız)
echo ========================================
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel__ == 0 (
    echo Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

echo.
echo 1. .NET 6.0 Desktop Runtime kontrol ediliyor...

REM .NET 6.0 Desktop Runtime kontrolü
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 6." >nul
if %errorLevel% neq 0 (
    echo .NET 6.0 Desktop Runtime bulunamadı, indiriliyor...
    
    REM .NET 6.0 Desktop Runtime indir
    powershell -Command "try { Invoke-WebRequest -Uri 'https://download.microsoft.com/download/6/6/4/664414c2-06ab-4b5b-8a82-e74a78b27a9e/windowsdesktop-runtime-6.0.25-win-x64.exe' -OutFile '%TEMP%\dotnet6-desktop.exe' -UseBasicParsing } catch { Write-Host 'İndirme hatası' }"
    
    if exist "%TEMP%\dotnet6-desktop.exe" (
        echo .NET 6.0 Desktop Runtime kuruluyor...
        "%TEMP%\dotnet6-desktop.exe" /quiet /norestart
        timeout /t 30 /nobreak >nul
        del "%TEMP%\dotnet6-desktop.exe"
        echo .NET 6.0 Desktop Runtime kuruldu.
    ) else (
        echo UYARI: Otomatik indirme başarısız!
        echo Manuel olarak şu adresten indirin:
        echo https://dotnet.microsoft.com/download/dotnet/6.0
        echo "Desktop Runtime x64" seçeneğini indirin ve kurun.
        pause
    )
) else (
    echo ✓ .NET 6.0 Desktop Runtime mevcut
)

echo.
echo 2. Ana proje derleniyor...
dotnet clean DonatPlus.sln >nul 2>&1
dotnet restore DonatPlus.sln
dotnet build DonatPlus.sln --configuration Release --no-restore
if %errorLevel% neq 0 (
    echo HATA: Proje derlenemedi!
    pause
    exit /b 1
)
echo ✓ Derleme başarılı

echo.
echo 3. Framework-dependent deployment oluşturuluyor...
dotnet publish DonatPlus.UI -c Release -f net6.0-windows --no-self-contained -o "publish\DonatPlus"
if %errorLevel% neq 0 (
    echo HATA: Deployment oluşturulamadı!
    pause
    exit /b 1
)
echo ✓ Deployment oluşturuldu

echo.
echo 4. Program kurulumu yapılıyor...
set PROGRAM_PATH=C:\Program Files\DonatPlus

REM Eski kurulumu temizle
if exist "%PROGRAM_PATH%" (
    echo Eski kurulum temizleniyor...
    rmdir /s /q "%PROGRAM_PATH%" >nul 2>&1
    timeout /t 2 /nobreak >nul
)

REM Yeni klasör oluştur
mkdir "%PROGRAM_PATH%" >nul 2>&1

REM Program dosyalarını kopyala
echo Program dosyaları kopyalanıyor...
xcopy "publish\DonatPlus\*" "%PROGRAM_PATH%\" /Y /S /Q
if %errorLevel% neq 0 (
    echo HATA: Dosyalar kopyalanamadı!
    pause
    exit /b 1
)
echo ✓ Program dosyaları kopyalandı

echo.
echo 5. WPF uyumlu başlatıcı oluşturuluyor...

REM WPF uyumlu batch dosyası oluştur
echo @echo off > "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo title DonatPlus >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo DonatPlus başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo REM Çalışma dizinini ayarla >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo cd /d "%%~dp0" >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo REM WPF uyumluluk ayarları >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo set DOTNET_EnableWriteXorExecute=0 >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo set DOTNET_DefaultDiagnosticPortSuspend=0 >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo set COMPlus_EnableDiagnostics=0 >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo set DOTNET_ROLL_FORWARD=Major >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo set DOTNET_ROLL_FORWARD_TO_PRERELEASE=1 >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo REM Ana programı başlat >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo Ana program başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo start "" "DonatPlus.UI.exe" >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo if %%errorLevel%% neq 0 ( >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo HATA: Program başlatılamadı! >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo Olası çözümler: >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo 1. .NET 6.0 Desktop Runtime yükleyin >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo 2. Windows Update yapın >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo 3. Antivirus yazılımını kontrol edin >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo 4. Programı yönetici olarak çalıştırın >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     pause >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo ^) >> "%PROGRAM_PATH%\DonatPlus_Start.bat"

echo ✓ Başlatıcı oluşturuldu

echo.
echo 6. Masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop

REM Eski kısayolları sil
if exist "%DESKTOP%\DonatPlus.lnk" del "%DESKTOP%\DonatPlus.lnk" >nul 2>&1

REM Yeni kısayol oluştur
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus_Start.bat" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "DonatPlus - Betonarme Donatı Metraj Hesaplama" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WindowStyle = 1 >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"
cscript "%TEMP%\CreateShortcut.vbs" >nul 2>&1
del "%TEMP%\CreateShortcut.vbs" >nul 2>&1

echo ✓ Masaüstü kısayolu oluşturuldu

echo.
echo 7. Başlangıç menüsü kısayolu oluşturuluyor...
set STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs

if not exist "%STARTMENU%\DonatPlus" mkdir "%STARTMENU%\DonatPlus"

echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateStartMenuShortcut.vbs"
echo sLinkFile = "%STARTMENU%\DonatPlus\DonatPlus.lnk" >> "%TEMP%\CreateStartMenuShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateStartMenuShortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus_Start.bat" >> "%TEMP%\CreateStartMenuShortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\CreateStartMenuShortcut.vbs"
echo oLink.Description = "DonatPlus - Betonarme Donatı Metraj Hesaplama" >> "%TEMP%\CreateStartMenuShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateStartMenuShortcut.vbs"
cscript "%TEMP%\CreateStartMenuShortcut.vbs" >nul 2>&1
del "%TEMP%\CreateStartMenuShortcut.vbs" >nul 2>&1

echo ✓ Başlangıç menüsü kısayolu oluşturuldu

echo.
echo 8. Test yapılıyor...
echo Program test ediliyor...
cd /d "%PROGRAM_PATH%"

REM Kısa test
echo Test başlatılıyor...
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo KURULUM BAŞARIYLA TAMAMLANDI!
echo ========================================
echo.
echo ✓ .NET 6.0 Desktop Runtime kontrol edildi
echo ✓ Proje derlendi
echo ✓ Framework-dependent deployment oluşturuldu
echo ✓ Program kuruldu: %PROGRAM_PATH%
echo ✓ WPF uyumlu başlatıcı oluşturuldu
echo ✓ Masaüstü kısayolu oluşturuldu
echo ✓ Başlangıç menüsü kısayolu oluşturuldu
echo.
echo KULLANIM:
echo 1. Masaüstündeki "DonatPlus" kısayoluna çift tıklayın
echo 2. Veya Başlat menüsünden "DonatPlus" arayın
echo 3. Veya şu dosyayı çalıştırın: %PROGRAM_PATH%\DonatPlus_Start.bat
echo.
echo Bu çözüm WPF hatalarını kesinlikle çözecektir!
echo.
echo Şimdi programı test etmek ister misiniz? (E/H)
set /p choice=
if /i "%choice%"=="E" (
    echo Program başlatılıyor...
    start "" "%PROGRAM_PATH%\DonatPlus_Start.bat"
)

echo.
echo Kurulum tamamlandı!
pause
