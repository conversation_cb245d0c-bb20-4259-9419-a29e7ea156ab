@echo off
echo ========================================
echo DonatPlus Gelişmiş Kurulum Scripti
echo ========================================
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    echo Sağ tık yapıp "Yönetici olarak çalıştır" seçin.
    pause
    exit /b 1
)

echo.
echo 1. Sistem kontrolleri yapılıyor...

REM .NET 6.0 Desktop Runtime kontrolü
echo Checking .NET 6.0 Desktop Runtime...
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 6." >nul
if %errorLevel% neq 0 (
    echo UYARI: .NET 6.0 Desktop Runtime bulunamadı!
    echo .NET 6.0 Desktop Runtime indiriliyor...
    
    REM .NET 6.0 Desktop Runtime indir ve kur
    powershell -Command "& {Invoke-WebRequest -Uri 'https://download.microsoft.com/download/6/6/4/664414c2-06ab-4b5b-8a82-e74a78b27a9e/windowsdesktop-runtime-6.0.25-win-x64.exe' -OutFile '%TEMP%\dotnet6-desktop-runtime.exe'}"
    
    if exist "%TEMP%\dotnet6-desktop-runtime.exe" (
        echo .NET 6.0 Desktop Runtime kuruluyor...
        "%TEMP%\dotnet6-desktop-runtime.exe" /quiet /norestart
        timeout /t 30 /nobreak >nul
        del "%TEMP%\dotnet6-desktop-runtime.exe"
        echo .NET 6.0 Desktop Runtime kuruldu.
    ) else (
        echo HATA: .NET 6.0 Desktop Runtime indirilemedi!
        echo Manuel olarak şu adresten indirin:
        echo https://dotnet.microsoft.com/download/dotnet/6.0
        pause
    )
) else (
    echo ✓ .NET 6.0 Desktop Runtime mevcut
)

REM .NET 8.0 Runtime kontrolü
echo Checking .NET 8.0 Runtime...
dotnet --list-runtimes | findstr "Microsoft.NETCore.App 8." >nul
if %errorLevel% neq 0 (
    echo UYARI: .NET 8.0 Runtime bulunamadı!
    echo .NET 8.0 Runtime indiriliyor...
    
    powershell -Command "& {Invoke-WebRequest -Uri 'https://download.microsoft.com/download/8/4/8/848f28ae-78c0-4c6b-90f8-68a146e66820/dotnet-runtime-8.0.0-win-x64.exe' -OutFile '%TEMP%\dotnet8-runtime.exe'}"
    
    if exist "%TEMP%\dotnet8-runtime.exe" (
        echo .NET 8.0 Runtime kuruluyor...
        "%TEMP%\dotnet8-runtime.exe" /quiet /norestart
        timeout /t 30 /nobreak >nul
        del "%TEMP%\dotnet8-runtime.exe"
        echo .NET 8.0 Runtime kuruldu.
    ) else (
        echo UYARI: .NET 8.0 Runtime indirilemedi!
    )
) else (
    echo ✓ .NET 8.0 Runtime mevcut
)

REM Visual C++ Redistributable kontrolü
echo Checking Visual C++ Redistributable...
reg query "HKLM\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" >nul 2>&1
if %errorLevel% neq 0 (
    echo UYARI: Visual C++ Redistributable bulunamadı!
    echo Visual C++ Redistributable indiriliyor...
    
    powershell -Command "& {Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x64.exe' -OutFile '%TEMP%\vc_redist.exe'}"
    
    if exist "%TEMP%\vc_redist.exe" (
        echo Visual C++ Redistributable kuruluyor...
        "%TEMP%\vc_redist.exe" /quiet /norestart
        timeout /t 20 /nobreak >nul
        del "%TEMP%\vc_redist.exe"
        echo Visual C++ Redistributable kuruldu.
    )
) else (
    echo ✓ Visual C++ Redistributable mevcut
)

echo.
echo 2. Proje derleniyor...
dotnet clean DonatPlus.sln >nul 2>&1
dotnet restore DonatPlus.sln
dotnet build DonatPlus.sln --configuration Release --no-restore
if %errorLevel% neq 0 (
    echo HATA: Proje derlenemedi!
    pause
    exit /b 1
)
echo ✓ Derleme başarılı

echo.
echo 3. Self-contained deployment oluşturuluyor...
dotnet publish DonatPlus.UI -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true
if %errorLevel% neq 0 (
    echo HATA: Self-contained deployment oluşturulamadı!
    pause
    exit /b 1
)
echo ✓ Self-contained deployment oluşturuldu

echo.
echo 4. Ana program kurulumu...
set PROGRAM_PATH=C:\Program Files\DonatPlus
if not exist "%PROGRAM_PATH%" (
    mkdir "%PROGRAM_PATH%"
)

REM Self-contained dosyaları kopyala
xcopy "DonatPlus.UI\bin\Release\net6.0-windows\win-x64\publish\*" "%PROGRAM_PATH%\" /Y /S

echo.
echo 5. Masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus.UI.exe" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "DonatPlus - Betonarme Donatı Metraj Hesaplama" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.IconLocation = "%PROGRAM_PATH%\DonatPlus.UI.exe,0" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"
cscript "%TEMP%\CreateShortcut.vbs" >nul
del "%TEMP%\CreateShortcut.vbs"

echo.
echo 6. Registry ayarları yapılıyor...
REM Dosya ilişkilendirmeleri
reg add "HKLM\SOFTWARE\Classes\.dpproj" /ve /d "DonatPlus.Project" /f >nul
reg add "HKLM\SOFTWARE\Classes\DonatPlus.Project" /ve /d "DonatPlus Project File" /f >nul
reg add "HKLM\SOFTWARE\Classes\DonatPlus.Project\DefaultIcon" /ve /d "%PROGRAM_PATH%\DonatPlus.UI.exe,0" /f >nul
reg add "HKLM\SOFTWARE\Classes\DonatPlus.Project\shell\open\command" /ve /d "\"%PROGRAM_PATH%\DonatPlus.UI.exe\" \"%%1\"" /f >nul

echo.
echo 7. Test yapılıyor...
echo Program test ediliyor...
timeout /t 2 /nobreak >nul

REM Program test et
start /wait "" "%PROGRAM_PATH%\DonatPlus.UI.exe" --test-mode
if %errorLevel% neq 0 (
    echo UYARI: Program test edilemedi, ancak kurulum tamamlandı.
) else (
    echo ✓ Program testi başarılı
)

echo.
echo ========================================
echo KURULUM TAMAMLANDI!
echo ========================================
echo.
echo ✓ .NET Runtime'lar kontrol edildi/kuruldu
echo ✓ Visual C++ Redistributable kontrol edildi
echo ✓ Self-contained deployment oluşturuldu
echo ✓ Program kuruldu: %PROGRAM_PATH%
echo ✓ Masaüstü kısayolu oluşturuldu
echo ✓ Dosya ilişkilendirmeleri yapıldı
echo.
echo Program şimdi çalıştırılabilir durumda!
echo Masaüstündeki DonatPlus kısayoluna çift tıklayın.
echo.
pause
