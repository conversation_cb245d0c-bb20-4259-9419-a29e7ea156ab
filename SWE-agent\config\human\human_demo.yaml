env:
  deployment:
    image: python:3.11
agent:
  templates:
    system_template: |-
      Enter any commands you want to run.

      There are a few special commands you can use to raise exceptions for testing:
      `raise_runtime`, `raise_cost`, `raise_context`, `raise_function_calling:<error_code>`,
      etc.
    instance_template: |-
      We're currently solving the following issue within our repository. Here's the issue text:
      ISSUE:
      {{problem_statement}}

      (Open file: {{open_file}})
      (Current directory: {{working_dir}})
      bash-$
    next_step_template: |-
      {{observation}}
      (Open file: {{open_file}})
      (Current directory: {{working_dir}})
      bash-$
    next_step_no_output_template: |-
      Your command ran successfully and did not produce any output.
      (Open file: {{open_file}})
      (Current directory: {{working_dir}})
      bash-$
  tools:
    env_variables:
      WINDOW: 100
      OVERLAP: 2
    bundles:
      - path: tools/registry
      - path: tools/windowed
      - path: tools/search
      - path: tools/windowed_edit_linting
      - path: tools/submit
    parse_function:
      type: thought_action
  history_processors:
    - type: last_n_observations
      n: 5
  model:
    name: human_thought
