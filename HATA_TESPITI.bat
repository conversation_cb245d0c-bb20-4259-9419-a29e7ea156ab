@echo off
echo ========================================
echo DonatPlus Hata Tespiti
echo ========================================
echo.

echo 1. Program dosyaları kontrol ediliyor...
set PROGRAM_PATH=C:\Program Files\DonatPlus

if not exist "%PROGRAM_PATH%" (
    echo HATA: Program klasörü bulunamadı!
    echo Beklenen konum: %PROGRAM_PATH%
    pause
    exit /b 1
)

if not exist "%PROGRAM_PATH%\DonatPlus.UI.exe" (
    echo HATA: Ana program dosyası bulunamadı!
    echo Beklenen dosya: %PROGRAM_PATH%\DonatPlus.UI.exe
    pause
    exit /b 1
)

echo ✓ Program dosyaları mevcut

echo.
echo 2. .NET Runtime kontrol ediliyor...
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 6." >nul
if %errorLevel% neq 0 (
    echo UYARI: .NET 6.0 Desktop Runtime bulunamadı!
    echo .NET 6.0 Desktop Runtime gerekli.
    echo İndirme linki: https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
) else (
    echo ✓ .NET 6.0 Desktop Runtime mevcut
)

echo.
echo 3. Program manuel olarak test ediliyor...
echo Lütfen bekleyin, program başlatılıyor...

cd /d "%PROGRAM_PATH%"

REM WPF uyumluluk ayarları
set DOTNET_EnableWriteXorExecute=0
set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
set DOTNET_ROLL_FORWARD=Major

echo.
echo Program başlatılıyor...
echo Hata mesajlarını görmek için bu pencereyi açık tutun!
echo.

REM Programı başlat ve hata mesajlarını göster
DonatPlus.UI.exe

echo.
echo Program kapandı. Çıkış kodu: %errorLevel%

if %errorLevel% neq 0 (
    echo.
    echo HATA: Program başlatılamadı!
    echo Çıkış kodu: %errorLevel%
    echo.
    echo Olası nedenler:
    echo 1. .NET 6.0 Desktop Runtime eksik
    echo 2. Visual C++ Redistributable eksik
    echo 3. Windows güncellemeleri eksik
    echo 4. Antivirus yazılımı engelliyor
    echo.
) else (
    echo.
    echo Program normal şekilde kapandı.
)

echo.
echo 4. Sistem bilgileri:
echo Windows Versiyonu:
ver
echo.
echo .NET Versiyonları:
dotnet --list-runtimes
echo.

pause
