<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <AssemblyTitle>DonatPlus Reports</AssemblyTitle>
    <AssemblyDescription>Raporlama modülü</AssemblyDescription>
    <AssemblyCompany>DonatPlus</AssemblyCompany>
    <AssemblyProduct>DonatPlus</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="7.0.0" />
    <PackageReference Include="iText7" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DonatPlus.Core\DonatPlus.Core.csproj" />
    <ProjectReference Include="..\DonatPlus.Database\DonatPlus.Database.csproj" />
  </ItemGroup>

</Project>
