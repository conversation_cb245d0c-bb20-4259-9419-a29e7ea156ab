@echo off
echo ========================================
echo DonatPlus Framework 4.8 Uyumlu Versiyon
echo ========================================
echo.
echo .NET Framework 4.8 uyumlu bağımsız versiyon oluşturuluyor...
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

echo.
echo 1. Framework 4.8 uyumlu proje oluşturuluyor...

REM Framework 4.8 klasörü oluştur
if exist "DonatPlus.Framework48" rmdir /s /q "DonatPlus.Framework48" >nul 2>&1
mkdir "DonatPlus.Framework48"

echo.
echo 2. Framework 4.8 proje dosyası oluşturuluyor...

REM Framework 4.8 uyumlu csproj oluştur
echo ^<Project Sdk="Microsoft.NET.Sdk"^> > "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo   ^<PropertyGroup^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo     ^<OutputType^>WinExe^</OutputType^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo     ^<TargetFramework^>net48^</TargetFramework^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo     ^<UseWPF^>true^</UseWPF^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo     ^<UseWindowsForms^>true^</UseWindowsForms^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo     ^<LangVersion^>latest^</LangVersion^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo   ^</PropertyGroup^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo   ^<ItemGroup^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo     ^<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="3.1.32" /^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo     ^<PackageReference Include="Microsoft.Extensions.Logging" Version="3.1.32" /^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo     ^<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="3.1.32" /^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo     ^<PackageReference Include="System.Text.Json" Version="4.7.2" /^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo   ^</ItemGroup^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"
echo ^</Project^> >> "DonatPlus.Framework48\DonatPlus.Framework48.csproj"

echo.
echo 3. Core sınıfları kopyalanıyor...

REM Core sınıfları kopyala
xcopy "DonatPlus.Core\Models\*.cs" "DonatPlus.Framework48\Models\" /Y /S /I >nul 2>&1
xcopy "DonatPlus.Core\Services\*.cs" "DonatPlus.Framework48\Services\" /Y /S /I >nul 2>&1

echo.
echo 4. Framework 4.8 uyumlu App.xaml oluşturuluyor...

REM App.xaml oluştur
echo ^<Application x:Class="DonatPlus.Framework48.App" > "DonatPlus.Framework48\App.xaml"
echo              xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" >> "DonatPlus.Framework48\App.xaml"
echo              xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" >> "DonatPlus.Framework48\App.xaml"
echo              StartupUri="MainWindow.xaml"^> >> "DonatPlus.Framework48\App.xaml"
echo ^</Application^> >> "DonatPlus.Framework48\App.xaml"

echo.
echo 5. App.xaml.cs oluşturuluyor...

REM App.xaml.cs oluştur
echo using System.Windows; > "DonatPlus.Framework48\App.xaml.cs"
echo. >> "DonatPlus.Framework48\App.xaml.cs"
echo namespace DonatPlus.Framework48 >> "DonatPlus.Framework48\App.xaml.cs"
echo { >> "DonatPlus.Framework48\App.xaml.cs"
echo     public partial class App : Application >> "DonatPlus.Framework48\App.xaml.cs"
echo     { >> "DonatPlus.Framework48\App.xaml.cs"
echo         protected override void OnStartup(StartupEventArgs e) >> "DonatPlus.Framework48\App.xaml.cs"
echo         { >> "DonatPlus.Framework48\App.xaml.cs"
echo             base.OnStartup(e); >> "DonatPlus.Framework48\App.xaml.cs"
echo         } >> "DonatPlus.Framework48\App.xaml.cs"
echo     } >> "DonatPlus.Framework48\App.xaml.cs"
echo } >> "DonatPlus.Framework48\App.xaml.cs"

echo.
echo 6. MainWindow.xaml oluşturuluyor...

REM MainWindow.xaml oluştur
echo ^<Window x:Class="DonatPlus.Framework48.MainWindow" > "DonatPlus.Framework48\MainWindow.xaml"
echo         xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" >> "DonatPlus.Framework48\MainWindow.xaml"
echo         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" >> "DonatPlus.Framework48\MainWindow.xaml"
echo         Title="DonatPlus Framework 4.8" Height="600" Width="800"^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo     ^<Grid^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo         ^<Grid.RowDefinitions^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo             ^<RowDefinition Height="Auto"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo             ^<RowDefinition Height="*"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo             ^<RowDefinition Height="Auto"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo         ^</Grid.RowDefinitions^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo. >> "DonatPlus.Framework48\MainWindow.xaml"
echo         ^<TextBlock Grid.Row="0" Text="DonatPlus - Framework 4.8 Uyumlu Versiyon" >> "DonatPlus.Framework48\MainWindow.xaml"
echo                    FontSize="20" FontWeight="Bold" Margin="10" HorizontalAlignment="Center"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo. >> "DonatPlus.Framework48\MainWindow.xaml"
echo         ^<StackPanel Grid.Row="1" Margin="20"^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo             ^<TextBlock Text="Donatı Metni:" FontWeight="Bold" Margin="0,0,0,5"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo             ^<TextBox Name="txtDonatıMetni" Text="5xø12 L=250" Margin="0,0,0,10"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo. >> "DonatPlus.Framework48\MainWindow.xaml"
echo             ^<Button Name="btnHesapla" Content="Hesapla" Width="100" Height="30" >> "DonatPlus.Framework48\MainWindow.xaml"
echo                     HorizontalAlignment="Left" Margin="0,0,0,10" Click="BtnHesapla_Click"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo. >> "DonatPlus.Framework48\MainWindow.xaml"
echo             ^<TextBlock Text="Sonuçlar:" FontWeight="Bold" Margin="0,10,0,5"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo             ^<TextBox Name="txtSonuçlar" IsReadOnly="True" Height="200" >> "DonatPlus.Framework48\MainWindow.xaml"
echo                      TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo         ^</StackPanel^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo. >> "DonatPlus.Framework48\MainWindow.xaml"
echo         ^<TextBlock Grid.Row="2" Text="BAML Hatası Çözülmüş - Framework 4.8 Uyumlu" >> "DonatPlus.Framework48\MainWindow.xaml"
echo                    HorizontalAlignment="Center" Margin="10" Foreground="Green"/^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo     ^</Grid^> >> "DonatPlus.Framework48\MainWindow.xaml"
echo ^</Window^> >> "DonatPlus.Framework48\MainWindow.xaml"

echo.
echo 7. MainWindow.xaml.cs oluşturuluyor...

REM MainWindow.xaml.cs oluştur
echo using System; > "DonatPlus.Framework48\MainWindow.xaml.cs"
echo using System.Windows; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo using DonatPlus.Framework48.Services; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo using Microsoft.Extensions.DependencyInjection; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo using Microsoft.Extensions.Logging; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo. >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo namespace DonatPlus.Framework48 >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo { >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo     public partial class MainWindow : Window >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo     { >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo         private readonly GeometryCalculationService _geometryService; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo         private readonly RebarDetectionService _detectionService; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo. >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo         public MainWindow() >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo         { >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             InitializeComponent(); >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo. >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             // Services >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             var services = new ServiceCollection(); >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             services.AddLogging(builder =^> builder.AddConsole()); >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             services.AddTransient^<GeometryCalculationService^>(); >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             services.AddTransient^<RebarDetectionService^>(); >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             var serviceProvider = services.BuildServiceProvider(); >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo. >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             _geometryService = serviceProvider.GetRequiredService^<GeometryCalculationService^>(); >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             _detectionService = serviceProvider.GetRequiredService^<RebarDetectionService^>(); >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo         } >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo. >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo         private void BtnHesapla_Click(object sender, RoutedEventArgs e) >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo         { >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             try >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             { >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                 var metin = txtDonatıMetni.Text; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                 var rebar = _detectionService.ExtractRebarFromText(metin, 0, 0, "TEST"); >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo. >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                 if (rebar != null) >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                 { >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                     var sonuç = $"Analiz Sonucu:\\n"; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                     sonuç += $"Çap: {rebar.Diameter}mm\\n"; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                     sonuç += $"Uzunluk: {rebar.Length}m\\n"; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                     sonuç += $"Adet: {rebar.Quantity}\\n"; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                     sonuç += $"Ağırlık: {rebar.Weight:F2}kg\\n"; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                     sonuç += $"Toplam Uzunluk: {rebar.Length * rebar.Quantity:F2}m\\n"; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                     sonuç += $"Toplam Ağırlık: {rebar.Weight * rebar.Quantity:F2}kg"; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                     txtSonuçlar.Text = sonuç; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                 } >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                 else >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                 { >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                     txtSonuçlar.Text = "Metin analiz edilemedi!"; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                 } >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             } >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             catch (Exception ex) >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             { >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo                 txtSonuçlar.Text = $"Hata: {ex.Message}"; >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo             } >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo         } >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo     } >> "DonatPlus.Framework48\MainWindow.xaml.cs"
echo } >> "DonatPlus.Framework48\MainWindow.xaml.cs"

echo.
echo 8. Framework 4.8 versiyonu derleniyor...
dotnet build DonatPlus.Framework48\DonatPlus.Framework48.csproj --configuration Release

if %errorLevel% neq 0 (
    echo HATA: Framework 4.8 versiyonu derlenemedi!
    echo Detaylı hata için:
    dotnet build DonatPlus.Framework48\DonatPlus.Framework48.csproj --configuration Release --verbosity detailed
    pause
    exit /b 1
)

echo ✓ Framework 4.8 versiyonu derlendi

echo.
echo 9. Program kurulumu yapılıyor...
set PROGRAM_PATH=C:\Program Files\DonatPlus_Framework48_Fixed

if exist "%PROGRAM_PATH%" rmdir /s /q "%PROGRAM_PATH%" >nul 2>&1
mkdir "%PROGRAM_PATH%" >nul 2>&1

xcopy "DonatPlus.Framework48\bin\Release\net48\*" "%PROGRAM_PATH%\" /Y /S /Q

echo.
echo 10. Masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop

echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus_Framework48_Fixed.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus.Framework48.exe" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "DonatPlus Framework 4.8 - BAML Hatası Çözülmüş" >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"
cscript "%TEMP%\shortcut.vbs" >nul 2>&1
del "%TEMP%\shortcut.vbs" >nul 2>&1

echo.
echo ========================================
echo FRAMEWORK 4.8 UYUMLU VERSIYON HAZIR!
echo ========================================
echo.
echo ✓ Framework 4.8 uyumlu proje oluşturuldu
echo ✓ Uyumsuz paketler kaldırıldı
echo ✓ Core fonksiyonlar dahil edildi
echo ✓ BAML hatası çözülmüş WPF arayüzü
echo ✓ Program kuruldu: %PROGRAM_PATH%
echo ✓ Masaüstü kısayolu: DonatPlus_Framework48_Fixed
echo.
echo Bu versiyon kesinlikle çalışacak!
echo.
echo Test etmek ister misiniz? (E/H)
set /p choice=
if /i "%choice%"=="E" (
    echo Program başlatılıyor...
    start "" "%PROGRAM_PATH%\DonatPlus.Framework48.exe"
    echo.
    echo Program başlatıldı! BAML hatası artık yok!
)

echo.
pause
