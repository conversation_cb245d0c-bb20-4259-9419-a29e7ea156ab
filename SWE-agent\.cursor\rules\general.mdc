---
description:
globs:
alwaysApply: true
---

# Your rule content

- Use python with type annotations
- Target python 3.11 or higher
- Use `pathlib` instead of `os.path`. Also use `Path.read_text()` over `with ...open()` constructs
- Use `argparse` to add interfaces
- Keep code comments to a minimum and only highlight particularly logically challenging things
- Do not append to the README unless specifically requested