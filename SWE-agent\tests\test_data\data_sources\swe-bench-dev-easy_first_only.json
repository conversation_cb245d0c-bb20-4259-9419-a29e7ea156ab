[{"repo": "pydicom/pydicom", "instance_id": "pydicom__pydicom-1458", "base_commit": "8da0b9b215ebfad5756051c891def88e426787e7", "patch": "diff --git a/pydicom/pixel_data_handlers/numpy_handler.py b/pydicom/pixel_data_handlers/numpy_handler.py\n--- a/pydicom/pixel_data_handlers/numpy_handler.py\n+++ b/pydicom/pixel_data_handlers/numpy_handler.py\n@@ -43,7 +43,9 @@\n | (0028,0100) | BitsAllocated             | 1    | 1, 8, 16, 32, | Required |\n |             |                           |      | 64            |          |\n +-------------+---------------------------+------+---------------+----------+\n-| (0028,0103) | PixelRepresentation       | 1    | 0, 1          | Required |\n+| (0028,0101) | BitsStored                | 1    | 1, 8, 12, 16  | Optional |\n++-------------+---------------------------+------+---------------+----------+\n+| (0028,0103) | PixelRepresentation       | 1C   | 0, 1          | Optional |\n +-------------+---------------------------+------+---------------+----------+\n \n \"\"\"\n@@ -284,16 +286,28 @@ def get_pixeldata(ds: \"Dataset\", read_only: bool = False) -> \"np.ndarray\":\n             \"the dataset\"\n         )\n \n+    # Attributes required by both Floating Point Image Pixel Module Attributes\n+    # and Image Pixel Description Macro Attributes\n     required_elements = [\n-        'BitsAllocated', 'Rows', 'Columns', 'PixelRepresentation',\n+        'BitsAllocated', 'Rows', 'Columns',\n         'SamplesPerPixel', 'PhotometricInterpretation'\n     ]\n+    if px_keyword[0] == 'PixelData':\n+        # Attributess required by Image Pixel Description Macro Attributes\n+        required_elements.extend(['PixelRepresentation', 'BitsStored'])\n     missing = [elem for elem in required_elements if elem not in ds]\n     if missing:\n         raise AttributeError(\n             \"Unable to convert the pixel data as the following required \"\n             \"elements are missing from the dataset: \" + \", \".join(missing)\n         )\n+    if ds.SamplesPerPixel > 1:\n+        if not hasattr(ds, 'PlanarConfiguration'):\n+            raise AttributeError(\n+                \"Unable to convert the pixel data as the following \"\n+                \"conditionally required element is missing from the dataset: \"\n+                \"PlanarConfiguration\"\n+            )\n \n     # May be Pixel Data, Float Pixel Data or Double Float Pixel Data\n     pixel_data = getattr(ds, px_keyword[0])\n", "test_patch": "diff --git a/pydicom/tests/test_numpy_pixel_data.py b/pydicom/tests/test_numpy_pixel_data.py\n--- a/pydicom/tests/test_numpy_pixel_data.py\n+++ b/pydicom/tests/test_numpy_pixel_data.py\n@@ -26,6 +26,8 @@\n * PlanarConfiguration\n \"\"\"\n \n+from copy import deepcopy\n+\n import pytest\n \n from pydicom import config\n@@ -1068,6 +1070,7 @@ def test_endianness_not_set(self):\n         ds.Rows = 10\n         ds.Columns = 10\n         ds.BitsAllocated = 16\n+        ds.BitsStored = 16\n         ds.PixelRepresentation = 0\n         ds.SamplesPerPixel = 1\n         ds.PhotometricInterpretation = 'MONOCHROME2'\n@@ -1105,16 +1108,60 @@ def test_no_pixel_data_raises(self):\n         with pytest.raises(AttributeError, match=msg):\n             get_pixeldata(ds)\n \n-    def test_missing_required_elem(self):\n+    def test_missing_required_elem_pixel_data_monochrome(self):\n         \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"\n-        ds = dcmread(EXPL_16_1_1F)\n-        del ds.BitsAllocated\n+        required_attrs = (\n+            'BitsAllocated',\n+            'BitsStored',\n+            'Rows',\n+            'Columns',\n+            'SamplesPerPixel',\n+            'PhotometricInterpretation',\n+            'PixelRepresentation',\n+        )\n+        for attr in required_attrs:\n+            ds = dcmread(EXPL_16_1_1F)\n+            delattr(ds, attr)\n+            msg = (\n+                r\"Unable to convert the pixel data as the following required \"\n+                r\"elements are missing from the dataset: {}\".format(attr)\n+            )\n+            with pytest.raises(AttributeError, match=msg):\n+                get_pixeldata(ds)\n+\n+    def test_missing_required_elem_pixel_data_color(self):\n+        \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"\n+        ds = dcmread(EXPL_8_3_1F)\n+        del ds.Rows\n+        del ds.Columns\n+        msg = (\n+            r\"Unable to convert the pixel data as the following required \"\n+            r\"elements are missing from the dataset: Rows, Columns\"\n+        )\n+        with pytest.raises(AttributeError, match=msg):\n+            get_pixeldata(ds)\n+\n+    def test_missing_conditionally_required_elem_pixel_data_color(self):\n+        \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"\n+        ds = dcmread(EXPL_8_3_1F)\n+        del ds.PlanarConfiguration\n+        msg = (\n+            r\"Unable to convert the pixel data as the following conditionally \"\n+            r\"required element is missing from the dataset: \"\n+            r\"PlanarConfiguration\"\n+        )\n+        with pytest.raises(AttributeError, match=msg):\n+            get_pixeldata(ds)\n+\n+    def test_missing_required_elem_float_pixel_data_monochrome(self):\n+        \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"\n+        ds = dcmread(IMPL_32_1_1F)\n+        ds.FloatPixelData = ds.PixelData\n+        del ds.PixelData\n         del ds.Rows\n-        del ds.SamplesPerPixel\n         msg = (\n             r\"Unable to convert the pixel data as the following required \"\n-            r\"elements are missing from the dataset: BitsAllocated, Rows, \"\n-            r\"SamplesPerPixel\"\n+            r\"elements are missing from the dataset: Rows\"\n         )\n         with pytest.raises(AttributeError, match=msg):\n             get_pixeldata(ds)\n", "problem_statement": "Pixel Representation attribute should be optional for pixel data handler\n**Describe the bug**\r\nThe NumPy pixel data handler currently [requires the Pixel Representation attribute](https://github.com/pydicom/pydicom/blob/8da0b9b215ebfad5756051c891def88e426787e7/pydicom/pixel_data_handlers/numpy_handler.py#L46). This is problematic, because in case of Float Pixel Data or Double Float Pixel Data the attribute shall be absent. Compare [Floating Point Image Pixel Module Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.24.html) versus [Image Pixel Description Macro Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.3.html#table_C.7-11c)\r\n\r\n**Expected behavior**\r\nI would expect the `Dataset.pixel_array` property to be able to decode a Float Pixel Data or Double Float Pixel Data element without presence of the Pixel Representation element in the metadata.\r\n\r\n**Steps To Reproduce**\r\n```python\r\nimport numpy as np\r\nfrom pydicom.dataset import Dataset, FileMetaDataset\r\n\r\n\r\nds = Dataset()\r\nds.file_meta = FileMetaDataset()\r\nds.file_meta.TransferSyntaxUID = '1.2.840.10008.1.2.1'\r\n\r\nds.BitsAllocated = 32\r\nds.SamplesPerPixel = 1\r\nds.Rows = 5\r\nds.Columns = 5\r\nds.PhotometricInterpretation = 'MONOCHROME2'\r\n\r\npixel_array = np.zeros((ds.Rows, ds.Columns), dtype=np.float32)\r\nds.FloatPixelData = pixel_array.flatten().tobytes()\r\n\r\nnp.array_equal(ds.pixel_array, pixel_array)\r\n```\n", "hints_text": "", "created_at": "2021-08-04T15:22:07Z", "version": "2.2", "FAIL_TO_PASS": ["pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_required_elem_pixel_data_monochrome", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_conditionally_required_elem_pixel_data_color"], "PASS_TO_PASS": ["pydicom/tests/test_numpy_pixel_data.py::test_unsupported_syntaxes", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_environment", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_supported_dataset", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_jpeg_dcmtk.dcm-data0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/JPEG-lossy.dcm-data1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_jpeg_gdcm.dcm-data2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_jpeg_ls_lossless.dcm-data3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/guest/jy1682/.pydicom/data/emri_small_jpeg_2k_lossless.dcm-data4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/JPEG2000.dcm-data5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_RLE.dcm-data6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NoNumpyHandler::test_pixel_array_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_environment", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_unsupported_syntax_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_dataset_pixel_array_handler_needs_convert", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_dataset_pixel_array_no_pixels", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_jpeg_dcmtk.dcm-data0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/JPEG-lossy.dcm-data1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_jpeg_gdcm.dcm-data2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_jpeg_ls_lossless.dcm-data3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/guest/jy1682/.pydicom/data/emri_small_jpeg_2k_lossless.dcm-data4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/JPEG2000.dcm-data5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_can_access_unsupported_dataset[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_RLE.dcm-data6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_pixel_array_8bit_un_signed", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[numpy]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[NumPy]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[np]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[np_handler]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_decompress_using_handler[numpy_handler]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_pixel_array_16bit_un_signed", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_pixel_array_32bit_un_signed", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_1sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_1sample_2frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_3sample_1frame_odd_size", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_3sample_1frame_ybr422", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_3sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_8bit_3sample_2frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/liver_1frame.dcm-data0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/liver.dcm-data1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A.dcm-data2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_rgb_small_odd.dcm-data3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/SC_ybr_full_422_uncompressed.dcm-data4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A_2frame.dcm-data5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb.dcm-data6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_2frame.dcm-data7]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small.dcm-data8]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/emri_small.dcm-data9]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_16bit.dcm-data10]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_16bit_2frame.dcm-data11]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose_1frame.dcm-data12]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose.dcm-data13]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_32bit.dcm-data14]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_properties[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_32bit_2frame.dcm-data15]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_1bit_1sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_1bit_1sample_3frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_1sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_1sample_1frame_padded", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_1sample_10frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_3sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_16bit_3sample_2frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_1sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_1sample_15frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_3sample_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_3sample_2frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_float_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_32bit_float_15frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_64bit_float_1frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_little_64bit_float_15frame", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/liver_1frame.dcm-/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/liver_expb_1frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/liver.dcm-/n/fs/guest/jy1682/.pydicom/data/liver_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A.dcm-/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A_2frame.dcm-/n/fs/guest/jy1682/.pydicom/data/OBXXXX1A_expb_2frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_2frame.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_2frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small.dcm-/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/MR_small_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/emri_small.dcm-/n/fs/guest/jy1682/.pydicom/data/emri_small_big_endian.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_16bit.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_16bit.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_16bit_2frame.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_16bit_2frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose_1frame.dcm-/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose_expb_1frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose.dcm-/n/fs/p-swe-bench/temp/tmpqcg17m39/pydicom__pydicom__2.2/pydicom/data/test_files/rtdose_expb.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_32bit.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_32bit.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_big_endian_datasets[/n/fs/guest/jy1682/.pydicom/data/SC_rgb_32bit_2frame.dcm-/n/fs/guest/jy1682/.pydicom/data/SC_rgb_expb_32bit_2frame.dcm]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_endianness_not_set", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_NumpyHandler::test_read_only", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_no_pixel_data_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_required_elem_pixel_data_color", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_required_elem_float_pixel_data_monochrome", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_unknown_pixel_representation_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_unsupported_syntaxes_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_bad_length_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_padding_warns", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_change_photometric_interpretation", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_array_read_only", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_array_read_only_bit_packed", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_ybr422_excess_padding", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_ybr422_wrong_interpretation", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_float_pixel_data", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_double_float_pixel_data", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[-output0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00-output1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x01-output2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x02-output3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x04-output4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x08-output5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x10-output6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[@-output8]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x80-output9]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\xaa-output10]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\xf0-output11]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x0f-output12]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\xff-output13]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00\\x00-output14]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00\\x01-output15]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00\\x80-output16]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x00\\xff-output17]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x01\\x80-output18]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\x80\\x80-output19]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_UnpackBits::test_unpack[\\xff\\x80-output20]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[-input0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00-input1]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x01-input2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x02-input3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x04-input4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x08-input5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x10-input6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[@-input8]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x80-input9]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\xaa-input10]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\xf0-input11]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x0f-input12]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\xff-input13]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00\\x00-input14]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00\\x01-input15]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00\\x80-input16]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x00\\xff-input17]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x01\\x80-input18]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\x80\\x80-input19]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack[\\xff\\x80-input20]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_non_binary_input", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_ndarray_input", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_padding", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00@-input0]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x10-input2]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x08-input3]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x04-input4]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x02-input5]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x00\\x01-input6]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x80-input7]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[@-input8]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x10-input10]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x08-input11]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x04-input12]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x02-input13]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[\\x01-input14]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_pack_partial[-input15]", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_PackBits::test_functional"], "environment_setup_commit": "0fa18d2a2179c92efc22200ed6b3689e66cecf92", "query": "Pixel Representation attribute should be optional for pixel data handler\n**Describe the bug**\r\nThe NumPy pixel data handler currently [requires the Pixel Representation attribute](https://github.com/pydicom/pydicom/blob/8da0b9b215ebfad5756051c891def88e426787e7/pydicom/pixel_data_handlers/numpy_handler.py#L46). This is problematic, because in case of Float Pixel Data or Double Float Pixel Data the attribute shall be absent. Compare [Floating Point Image Pixel Module Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.24.html) versus [Image Pixel Description Macro Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.3.html#table_C.7-11c)\r\n\r\n**Expected behavior**\r\nI would expect the `Dataset.pixel_array` property to be able to decode a Float Pixel Data or Double Float Pixel Data element without presence of the Pixel Representation element in the metadata.\r\n\r\n**Steps To Reproduce**\r\n```python\r\nimport numpy as np\r\nfrom pydicom.dataset import Dataset, FileMetaDataset\r\n\r\n\r\nds = Dataset()\r\nds.file_meta = FileMetaDataset()\r\nds.file_meta.TransferSyntaxUID = '1.2.840.10008.1.2.1'\r\n\r\nds.BitsAllocated = 32\r\nds.SamplesPerPixel = 1\r\nds.Rows = 5\r\nds.Columns = 5\r\nds.PhotometricInterpretation = 'MONOCHROME2'\r\n\r\npixel_array = np.zeros((ds.Rows, ds.Columns), dtype=np.float32)\r\nds.FloatPixelData = pixel_array.flatten().tobytes()\r\n\r\nnp.array_equal(ds.pixel_array, pixel_array)\r\n```\n", "task_id": "pydicom__pydicom-1458"}]