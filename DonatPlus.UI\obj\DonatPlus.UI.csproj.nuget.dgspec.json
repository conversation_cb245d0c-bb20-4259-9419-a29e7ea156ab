{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.UI\\DonatPlus.UI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj", "projectName": "DonatPlus.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net48", "net6.0", "net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {}}, "net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}, "net48": {"targetAlias": "net48", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[6.0.9, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Database\\DonatPlus.Database.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Database\\DonatPlus.Database.csproj", "projectName": "DonatPlus.Database", "projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Database\\DonatPlus.Database.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Database\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net48", "net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj"}}}, "net48": {"targetAlias": "net48", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.11, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.11, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[7.0.11, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[7.0.11, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}, "net48": {"targetAlias": "net48", "dependencies": {"Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.118, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Reports\\DonatPlus.Reports.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Reports\\DonatPlus.Reports.csproj", "projectName": "DonatPlus.Reports", "projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Reports\\DonatPlus.Reports.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Reports\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Database\\DonatPlus.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Database\\DonatPlus.Database.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}, "iText7": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.UI\\DonatPlus.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.UI\\DonatPlus.UI.csproj", "projectName": "DonatPlus.UI", "projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.UI\\DonatPlus.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.UI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Database\\DonatPlus.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Database\\DonatPlus.Database.csproj"}, "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Reports\\DonatPlus.Reports.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Reports\\DonatPlus.Reports.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[7.0.11, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}