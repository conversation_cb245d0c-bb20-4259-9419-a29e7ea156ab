using System;
using System.Collections.Generic;
using System.Linq;
using DonatPlus.Core.Models;
using Microsoft.Extensions.Logging;

namespace DonatPlus.Core.Services
{
    /// <summary>
    /// Geometrik hesaplamalar için servis sınıfı
    /// </summary>
    public class GeometryCalculationService
    {
        private readonly ILogger<GeometryCalculationService> _logger;
        
        // Çelik yoğunluğu (kg/m³)
        private const double SteelDensity = 7850;
        
        // Standart donatı ağırlıkları (kg/m)
        private readonly Dictionary<double, double> _standardWeights = new Dictionary<double, double>
        {
            { 6, 0.222 },
            { 8, 0.395 },
            { 10, 0.617 },
            { 12, 0.888 },
            { 14, 1.21 },
            { 16, 1.58 },
            { 18, 2.00 },
            { 20, 2.47 },
            { 22, 2.98 },
            { 25, 3.85 },
            { 28, 4.83 },
            { 32, 6.31 }
        };
        
        public GeometryCalculationService(ILogger<GeometryCalculationService> logger)
        {
            _logger = logger;
        }
        
        /// <summary>
        /// Donatı ağırlığını hesaplar
        /// </summary>
        /// <param name="diameter">Çap (mm)</param>
        /// <param name="length">Uzunluk (m)</param>
        /// <returns>Ağırlık (kg)</returns>
        public virtual double CalculateRebarWeight(double diameter, double length)
        {
            try
            {
                // Geçersiz değerler için kontrol
                if (diameter <= 0 || length <= 0)
                {
                    return 0;
                }

                // Standart ağırlık tablosundan kontrol et
                if (_standardWeights.ContainsKey(diameter))
                {
                    return _standardWeights[diameter] * length;
                }

                // Formül ile hesapla: Ağırlık = π × (çap/2)² × uzunluk × yoğunluk
                var radius = diameter / 2000; // mm'den m'ye çevir
                var area = Math.PI * Math.Pow(radius, 2); // m²
                var weight = area * length * SteelDensity; // kg

                return Math.Round(weight, 3);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Donatı ağırlığı hesaplama hatası - Çap: {Diameter}mm, Uzunluk: {Length}m", diameter, length);
                return 0;
            }
        }
        
        /// <summary>
        /// İki nokta arasındaki mesafeyi hesaplar
        /// </summary>
        public double CalculateDistance(double x1, double y1, double x2, double y2)
        {
            return Math.Sqrt(Math.Pow(x2 - x1, 2) + Math.Pow(y2 - y1, 2));
        }
        
        /// <summary>
        /// Polyline uzunluğunu hesaplar
        /// </summary>
        public double CalculatePolylineLength(List<Point2D> points)
        {
            if (points == null || points.Count < 2)
                return 0;
                
            double totalLength = 0;
            for (int i = 1; i < points.Count; i++)
            {
                totalLength += CalculateDistance(
                    points[i - 1].X, points[i - 1].Y,
                    points[i].X, points[i].Y
                );
            }
            
            return totalLength / 1000; // mm'den m'ye çevir
        }
        
        /// <summary>
        /// Donatı büküm uzunluğunu hesaplar
        /// </summary>
        public double CalculateBendLength(double angle, double radius, double diameter)
        {
            try
            {
                // Büküm açısını radyana çevir
                var angleRad = angle * Math.PI / 180;
                
                // İç yarıçap = dış yarıçap - çap/2
                var innerRadius = Math.Max(radius - diameter / 2, diameter);
                
                // Büküm uzunluğu = açı × iç yarıçap
                var bendLength = angleRad * innerRadius;
                
                return bendLength / 1000; // mm'den m'ye çevir
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Büküm uzunluğu hesaplama hatası");
                return 0;
            }
        }
        
        /// <summary>
        /// Donatı kanca uzunluğunu hesaplar
        /// </summary>
        public double CalculateHookLength(double diameter, string hookType = "90")
        {
            try
            {
                // TS 500'e göre kanca uzunlukları
                switch (hookType)
                {
                    case "90":
                        return 12 * diameter / 1000; // 12d
                    case "135":
                        return 6 * diameter / 1000; // 6d
                    case "180":
                        return 4 * diameter / 1000; // 4d
                    default:
                        return 12 * diameter / 1000;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Kanca uzunluğu hesaplama hatası");
                return 0;
            }
        }
        
        /// <summary>
        /// Bindirme boyu hesaplar
        /// </summary>
        public double CalculateLapLength(double diameter, string concreteClass = "C25", string steelGrade = "S420")
        {
            try
            {
                // TS 500'e göre temel bindirme boyu
                double basicLapLength = 40 * diameter / 1000; // 40d (m cinsinden)
                
                // Beton sınıfına göre düzeltme
                double concreteCoeff = concreteClass switch
                {
                    "C20" => 1.2,
                    "C25" => 1.0,
                    "C30" => 0.9,
                    "C35" => 0.8,
                    _ => 1.0
                };
                
                // Çelik sınıfına göre düzeltme
                double steelCoeff = steelGrade switch
                {
                    "S220" => 0.7,
                    "S420" => 1.0,
                    "S500" => 1.2,
                    _ => 1.0
                };
                
                return basicLapLength * concreteCoeff * steelCoeff;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bindirme boyu hesaplama hatası");
                return 40 * diameter / 1000; // Varsayılan değer
            }
        }
        
        /// <summary>
        /// Donatı kesim optimizasyonu yapar
        /// </summary>
        public List<CuttingPlan> OptimizeCutting(List<RebarElement> rebars, double standardLength = 12.0)
        {
            var cuttingPlans = new List<CuttingPlan>();
            
            try
            {
                // Çaplara göre grupla
                var groupedByDiameter = rebars.GroupBy(r => r.Diameter);
                
                foreach (var group in groupedByDiameter)
                {
                    var diameter = group.Key;
                    var elements = group.ToList();
                    
                    // Uzunluklara göre sırala
                    var lengths = elements.SelectMany(e => 
                        Enumerable.Repeat(e.Length, e.Quantity)
                    ).OrderByDescending(l => l).ToList();
                    
                    var plan = new CuttingPlan
                    {
                        Diameter = diameter,
                        StandardLength = standardLength,
                        Cuts = new List<Cut>()
                    };
                    
                    // Basit kesim optimizasyonu (First Fit Decreasing)
                    while (lengths.Any())
                    {
                        var cut = new Cut { Pieces = new List<double>() };
                        double remainingLength = standardLength;
                        
                        for (int i = lengths.Count - 1; i >= 0; i--)
                        {
                            if (lengths[i] <= remainingLength)
                            {
                                cut.Pieces.Add(lengths[i]);
                                remainingLength -= lengths[i];
                                lengths.RemoveAt(i);
                            }
                        }
                        
                        cut.WasteLength = remainingLength;
                        plan.Cuts.Add(cut);
                    }
                    
                    cuttingPlans.Add(plan);
                }
                
                return cuttingPlans;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Kesim optimizasyonu hatası");
                return cuttingPlans;
            }
        }
        
        /// <summary>
        /// Alan hesaplar (çokgen için)
        /// </summary>
        public double CalculatePolygonArea(List<Point2D> points)
        {
            if (points == null || points.Count < 3)
                return 0;
                
            double area = 0;
            int n = points.Count;
            
            for (int i = 0; i < n; i++)
            {
                int j = (i + 1) % n;
                area += points[i].X * points[j].Y;
                area -= points[j].X * points[i].Y;
            }
            
            return Math.Abs(area) / 2;
        }
    }
    
    /// <summary>
    /// 2D nokta sınıfı
    /// </summary>
    public class Point2D
    {
        public double X { get; set; }
        public double Y { get; set; }
        
        public Point2D(double x, double y)
        {
            X = x;
            Y = y;
        }
    }
    
    /// <summary>
    /// Kesim planı
    /// </summary>
    public class CuttingPlan
    {
        public double Diameter { get; set; }
        public double StandardLength { get; set; }
        public List<Cut> Cuts { get; set; } = new List<Cut>();
        public double TotalWaste => Cuts.Sum(c => c.WasteLength);
        public double WastePercentage => (TotalWaste / (Cuts.Count * StandardLength)) * 100;
    }
    
    /// <summary>
    /// Tek kesim
    /// </summary>
    public class Cut
    {
        public List<double> Pieces { get; set; } = new List<double>();
        public double WasteLength { get; set; }
        public double UsedLength => Pieces.Sum();
    }
}
