using DonatPlus.Database.DbContext;
using DonatPlus.Database.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace DonatPlus.UI
{
    /// <summary>
    /// MainWindow.xaml etkileşim mantığı
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<MainWindow> _logger;
        private readonly DonatPlusContext _dbContext;
        private readonly DispatcherTimer _timer;
        
        private Project? _currentProject;
        
        public MainWindow(IServiceProvider serviceProvider, ILogger<MainWindow> logger, DonatPlusContext dbContext)
        {
            InitializeComponent();
            
            _serviceProvider = serviceProvider;
            _logger = logger;
            _dbContext = dbContext;
            
            // Timer başlat
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();
            
            // Başlangıç durumu
            UpdateUI();
            
            _logger.LogInformation("Ana pencere başlatıldı");
        }
        
        private void Timer_Tick(object? sender, EventArgs e)
        {
            TxtCurrentTime.Text = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
        }
        
        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Veritabanı bağlantısını kontrol et
                await CheckDatabaseConnection();
                
                TxtStatus.Text = "Uygulama başlatıldı";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Pencere yükleme hatası");
                MessageBox.Show($"Başlatma hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async Task CheckDatabaseConnection()
        {
            try
            {
                await _dbContext.Database.CanConnectAsync();
                DbStatusIndicator.Fill = System.Windows.Media.Brushes.Green;
                _logger.LogInformation("Veritabanı bağlantısı başarılı");
            }
            catch (Exception ex)
            {
                DbStatusIndicator.Fill = System.Windows.Media.Brushes.Red;
                _logger.LogError(ex, "Veritabanı bağlantı hatası");
            }
        }
        
        private void BtnNewProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newProjectDialog = new Views.NewProjectDialog();
                if (newProjectDialog.ShowDialog() == true)
                {
                    _currentProject = newProjectDialog.Project;
                    UpdateUI();
                    TxtStatus.Text = "Yeni proje oluşturuldu";
                    
                    _logger.LogInformation("Yeni proje oluşturuldu: {ProjectName}", _currentProject.Name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yeni proje oluşturma hatası");
                MessageBox.Show($"Proje oluşturma hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void BtnOpenProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("Proje açma özelliği yakında eklenecek.", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
                TxtStatus.Text = "Proje açma özelliği geliştiriliyor";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Proje açma hatası");
                MessageBox.Show($"Proje açma hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void BtnAnalyzeDrawing_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentProject == null)
                {
                    MessageBox.Show("Önce bir proje seçmelisiniz.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                MessageBox.Show("Çizim analizi özelliği yakında eklenecek.", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
                TxtStatus.Text = "Çizim analizi özelliği geliştiriliyor";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Çizim analizi hatası");
                MessageBox.Show($"Çizim analizi hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void BtnCalculations_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentProject == null)
                {
                    MessageBox.Show("Önce bir proje seçmelisiniz.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                MessageBox.Show("Hesaplamalar özelliği yakında eklenecek.", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
                TxtStatus.Text = "Hesaplamalar özelliği geliştiriliyor";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hesaplamalar görüntüleme hatası");
                MessageBox.Show($"Hesaplamalar hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void BtnReports_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentProject == null)
                {
                    MessageBox.Show("Önce bir proje seçmelisiniz.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                MessageBox.Show("Raporlar özelliği yakında eklenecek.", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
                TxtStatus.Text = "Raporlar özelliği geliştiriliyor";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Raporlar görüntüleme hatası");
                MessageBox.Show($"Raporlar hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void BtnSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("Ayarlar özelliği yakında eklenecek.", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
                TxtStatus.Text = "Ayarlar özelliği geliştiriliyor";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ayarlar açma hatası");
                MessageBox.Show($"Ayarlar hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnHelp_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("Yardım özelliği yakında eklenecek.", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
                TxtStatus.Text = "Yardım özelliği geliştiriliyor";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yardım açma hatası");
                MessageBox.Show($"Yardım hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void BtnQuickAnalyze_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentProject == null)
                {
                    MessageBox.Show("Önce bir proje seçmelisiniz.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                // Hızlı analiz işlemi
                MessageBox.Show("Hızlı analiz özelliği yakında eklenecek.", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
                
                TxtStatus.Text = "Hızlı analiz tamamlandı";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hızlı analiz hatası");
                MessageBox.Show($"Hızlı analiz hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void BtnSaveProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentProject == null)
                {
                    MessageBox.Show("Kaydedilecek proje bulunamadı.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                if (_currentProject.Id == 0)
                {
                    _dbContext.Projects.Add(_currentProject);
                }
                else
                {
                    _currentProject.ModifiedDate = DateTime.Now;
                    _dbContext.Projects.Update(_currentProject);
                }
                
                await _dbContext.SaveChangesAsync();
                
                MessageBox.Show("Proje başarıyla kaydedildi.", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
                TxtStatus.Text = "Proje kaydedildi";
                
                _logger.LogInformation("Proje kaydedildi: {ProjectName}", _currentProject.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Proje kaydetme hatası");
                MessageBox.Show($"Proje kaydetme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateUI()
        {
            if (_currentProject != null)
            {
                TxtProjectName.Text = _currentProject.Name;
                TxtProjectInfo.Text = $"Proje No: {_currentProject.ProjectNumber} | Müşteri: {_currentProject.ClientName}";
                
                // Menü butonlarını etkinleştir
                BtnAnalyzeDrawing.IsEnabled = true;
                BtnCalculations.IsEnabled = true;
                BtnReports.IsEnabled = true;
                BtnQuickAnalyze.IsEnabled = true;
                BtnSaveProject.IsEnabled = true;
            }
            else
            {
                TxtProjectName.Text = "Proje seçilmedi";
                TxtProjectInfo.Text = "Yeni bir proje oluşturun veya mevcut bir projeyi açın";
                
                // Menü butonlarını devre dışı bırak
                BtnAnalyzeDrawing.IsEnabled = false;
                BtnCalculations.IsEnabled = false;
                BtnReports.IsEnabled = false;
                BtnQuickAnalyze.IsEnabled = false;
                BtnSaveProject.IsEnabled = false;
            }
        }
        
        private void BtnMinimize_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }
        
        private void BtnMaximize_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
        }
        
        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
        
        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            _dbContext?.Dispose();
            base.OnClosed(e);
        }
    }
}
