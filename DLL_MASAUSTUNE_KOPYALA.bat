@echo off
echo ========================================
echo DLL Dosyasını Masaüstüne Kopyalama
echo ========================================
echo.

echo DLL dosyası aranıyor ve masaüstüne kopyalanıyor...

set DESKTOP=%USERPROFILE%\Desktop
set DLL_FOUND=0

REM Olası DLL konumlarını kontrol et
if exist "DonatPlus.AutoCAD\bin\Release\net48\DonatPlus.AutoCAD.dll" (
    copy "DonatPlus.AutoCAD\bin\Release\net48\DonatPlus.AutoCAD.dll" "%DESKTOP%\" >nul
    echo ✓ DonatPlus.AutoCAD.dll masaüstüne kopyalandı
    set DLL_FOUND=1
)

if exist "DonatPlus.AutoCAD.Simple\bin\Release\net48\DonatPlus.AutoCAD.Simple.dll" (
    copy "DonatPlus.AutoCAD.Simple\bin\Release\net48\DonatPlus.AutoCAD.Simple.dll" "%DESKTOP%\" >nul
    echo ✓ DonatPlus.AutoCAD.Simple.dll masaüstüne kopyalandı
    set DLL_FOUND=1
)

if %DLL_FOUND%==0 (
    echo UYARI: DLL dosyası bulunamadı!
    echo Önce AUTOCAD_DLL_COZUM.bat çalıştırın.
) else (
    echo.
    echo ========================================
    echo DLL MASAÜSTÜNDE HAZIR!
    echo ========================================
    echo.
    echo AUTOCAD'DE KULLANIM:
    echo 1. AutoCAD'i açın
    echo 2. Komut: NETLOAD
    echo 3. Masaüstünden DLL dosyasını seçin
    echo 4. Komut: DONATPLUS
)

echo.
pause
