<Application x:Class="DonatPlus.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Özel stiller -->
            <Style x:Key="TitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Margin" Value="0,0,0,10"/>
            </Style>
            
            <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Margin" Value="0,0,0,5"/>
            </Style>
            
            <Style x:Key="InfoTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Opacity" Value="0.7"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
            </Style>
            
            <!-- Veri grid stilleri -->
            <Style x:Key="DataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                <Setter Property="AutoGenerateColumns" Value="False"/>
                <Setter Property="CanUserAddRows" Value="False"/>
                <Setter Property="CanUserDeleteRows" Value="False"/>
                <Setter Property="IsReadOnly" Value="True"/>
                <Setter Property="SelectionMode" Value="Single"/>
                <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                <Setter Property="HeadersVisibility" Value="Column"/>
                <Setter Property="AlternatingRowBackground" Value="#F5F5F5"/>
            </Style>
            
            <!-- Kart stilleri -->
            <Style x:Key="InfoCardStyle" TargetType="materialDesign:Card">
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            </Style>
            
            <!-- Buton stilleri -->
            <Style x:Key="ActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
            </Style>
            
            <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Padding" Value="16,8"/>
            </Style>
            
            <!-- Form stilleri -->
            <Style x:Key="FormTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                <Setter Property="Margin" Value="0,5"/>
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            </Style>
            
            <Style x:Key="FormComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
                <Setter Property="Margin" Value="0,5"/>
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            </Style>
            
            <!-- Progress bar stilleri -->
            <Style x:Key="ProgressBarStyle" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
                <Setter Property="Height" Value="6"/>
                <Setter Property="Margin" Value="0,10"/>
            </Style>
            
            <!-- Tab control stilleri -->
            <Style x:Key="TabControlStyle" TargetType="TabControl" BasedOn="{StaticResource MaterialDesignTabControl}">
                <Setter Property="materialDesign:ColorZoneAssist.Mode" Value="PrimaryMid"/>
            </Style>
            
            <!-- Renk tanımlamaları -->
            <SolidColorBrush x:Key="SuccessColor" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningColor" Color="#FF9800"/>
            <SolidColorBrush x:Key="ErrorColor" Color="#F44336"/>
            <SolidColorBrush x:Key="InfoColor" Color="#2196F3"/>
            
            <!-- Converter'lar -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>
