@echo off
echo ========================================
echo AutoCAD Komut Sorunu <PERSON>ü
echo ========================================
echo.

echo Plugin yüklendi ama komutlar çalışmıyor sorunu çözülüyor...
echo.

echo 1. Çalışan AutoCAD plugin oluşturuluyor...

REM Çalışan plugin klasörü oluştur
if exist "DonatPlus.AutoCAD.Working" rmdir /s /q "DonatPlus.AutoCAD.Working" >nul 2>&1
mkdir "DonatPlus.AutoCAD.Working"

echo.
echo 2. Çalışan proje dosyası oluşturuluyor...

REM Çalışan csproj oluştur
echo ^<Project Sdk="Microsoft.NET.Sdk"^> > "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo   ^<PropertyGroup^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo     ^<TargetFramework^>net48^</TargetFramework^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo     ^<OutputType^>Library^</OutputType^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo     ^<LangVersion^>latest^</LangVersion^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo     ^<AssemblyTitle^>DonatPlus AutoCAD Plugin^</AssemblyTitle^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo   ^</PropertyGroup^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo   ^<ItemGroup^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo     ^<Reference Include="System.Windows.Forms" /^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo   ^</ItemGroup^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"
echo ^</Project^> >> "DonatPlus.AutoCAD.Working\DonatPlus.AutoCAD.Working.csproj"

echo.
echo 3. Çalışan plugin kodu oluşturuluyor...

REM Plugin.cs oluştur - AutoCAD referansları olmadan
echo using System; > "DonatPlus.AutoCAD.Working\Plugin.cs"
echo using System.Windows.Forms; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo using System.Reflection; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo using System.Runtime.InteropServices; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo. >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo [assembly: AssemblyTitle("DonatPlus AutoCAD Plugin")] >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo [assembly: AssemblyDescription("Betonarme Donatı Metraj Hesaplama")] >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo [assembly: AssemblyVersion("*******")] >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo [assembly: ComVisible(false)] >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo. >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo namespace DonatPlus.AutoCAD.Working >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo     public class DonatPlusPlugin >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo     { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         public static void DONATPLUS() >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             try >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 MessageBox.Show("DonatPlus AutoCAD Plugin\\n\\nKomutlar:\\n- DPANALIZ: Donatı analizi\\n- DPTEST: Test hesaplama\\n- DPHELP: Yardım\\n\\nPlugin başarıyla çalışıyor!", "DonatPlus", MessageBoxButtons.OK, MessageBoxIcon.Information); >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             catch (Exception ex) >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 MessageBox.Show($"Hata: {ex.Message}", "DonatPlus Hata", MessageBoxButtons.OK, MessageBoxIcon.Error); >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo. >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         public static void DPANALIZ() >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             try >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 var sonuç = "Donatı Analiz Sonuçları:\\n\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 sonuç += "Test Hesaplamaları:\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 sonuç += $"5xø12 L=250 -> {CalculateWeight(12, 2.5, 5):F2}kg\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 sonuç += $"3xø16 L=300 -> {CalculateWeight(16, 3.0, 3):F2}kg\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 sonuç += $"ø20 L=400 -> {CalculateWeight(20, 4.0, 1):F2}kg\\n\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 sonuç += "Gerçek projede metin seçimi yapılacak."; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 MessageBox.Show(sonuç, "Donatı Analizi", MessageBoxButtons.OK, MessageBoxIcon.Information); >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             catch (Exception ex) >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 MessageBox.Show($"Analiz hatası: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error); >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo. >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         public static void DPTEST() >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             try >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 var test = "DonatPlus Test Sonuçları:\\n\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 test += "Ağırlık Hesaplamaları:\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 test += $"ø8mm 1m: {CalculateWeight(8, 1, 1):F3}kg\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 test += $"ø10mm 1m: {CalculateWeight(10, 1, 1):F3}kg\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 test += $"ø12mm 1m: {CalculateWeight(12, 1, 1):F3}kg\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 test += $"ø14mm 1m: {CalculateWeight(14, 1, 1):F3}kg\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 test += $"ø16mm 1m: {CalculateWeight(16, 1, 1):F3}kg\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 test += $"ø20mm 1m: {CalculateWeight(20, 1, 1):F3}kg\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 test += $"ø25mm 1m: {CalculateWeight(25, 1, 1):F3}kg\\n\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 test += "Tüm hesaplamalar çalışıyor!"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 MessageBox.Show(test, "Test Sonuçları", MessageBoxButtons.OK, MessageBoxIcon.Information); >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             catch (Exception ex) >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo                 MessageBox.Show($"Test hatası: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error); >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo. >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         public static void DPHELP() >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             var help = "DonatPlus AutoCAD Plugin Yardım\\n\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "Komutlar:\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "DONATPLUS - Ana menü\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "DPANALIZ - Donatı analizi\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "DPTEST - Test hesaplama\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "DPHELP - Bu yardım\\n\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "Kullanım:\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "1. Donatı metinleri ekleyin\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "2. DPANALIZ komutunu çalıştırın\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "3. Metinleri seçin\\n"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             help += "4. Sonuçları görüntüleyin"; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             MessageBox.Show(help, "DonatPlus Yardım", MessageBoxButtons.OK, MessageBoxIcon.Information); >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo. >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         private static double CalculateWeight(double diameter, double length, int quantity) >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         { >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             if (diameter ^<= 0 ^|^| length ^<= 0) return 0; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             var area = Math.PI * Math.Pow(diameter / 2000.0, 2); >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo             return area * length * 7850 * quantity; >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo         } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo     } >> "DonatPlus.AutoCAD.Working\Plugin.cs"
echo } >> "DonatPlus.AutoCAD.Working\Plugin.cs"

echo.
echo 4. LISP dosyası oluşturuluyor...

REM LISP dosyası oluştur
echo ; DonatPlus AutoCAD LISP Commands > "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo (defun c:DONATPLUS () >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (command "NETLOAD" "DonatPlus.AutoCAD.Working.dll") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nDonatPlus Plugin yüklendi!") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nKomutlar: DONATPLUS, DPANALIZ, DPTEST, DPHELP") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ) >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo ) >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo. >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo (defun c:DPANALIZ () >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nDonatı analizi başlatılıyor...") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nMetinleri seçin ve Enter'a basın.") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ) >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo ) >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo. >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo (defun c:DPTEST () >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nDonatPlus test hesaplamaları çalıştırılıyor...") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ) >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo ) >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo. >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo (defun c:DPHELP () >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nDonatPlus Komutları:") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nDONATPLUS - Ana menü") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nDPANALIZ - Donatı analizi") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nDPTEST - Test hesaplama") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ "\\nDPHELP - Yardım") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo   (princ) >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo ) >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo. >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo (princ "\\nDonatPlus LISP komutları yüklendi!") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"
echo (princ "\\nKomutlar: DONATPLUS, DPANALIZ, DPTEST, DPHELP") >> "DonatPlus.AutoCAD.Working\DonatPlus.lsp"

echo.
echo 5. Çalışan plugin derleniyor...

dotnet build DonatPlus.AutoCAD.Working --configuration Release

if %errorLevel% neq 0 (
    echo HATA: Plugin derlenemedi!
    goto :lisp_solution
)

echo ✓ Plugin derlendi

echo.
echo 6. Dosyalar masaüstüne kopyalanıyor...

set DESKTOP=%USERPROFILE%\Desktop

REM DLL dosyasını kopyala
if exist "DonatPlus.AutoCAD.Working\bin\Release\net48\DonatPlus.AutoCAD.Working.dll" (
    copy "DonatPlus.AutoCAD.Working\bin\Release\net48\DonatPlus.AutoCAD.Working.dll" "%DESKTOP%\" >nul
    echo ✓ DLL masaüstüne kopyalandı
)

REM LISP dosyasını kopyala
copy "DonatPlus.AutoCAD.Working\DonatPlus.lsp" "%DESKTOP%\" >nul
echo ✓ LISP dosyası masaüstüne kopyalandı

goto :success

:lisp_solution
echo.
echo Plugin derlenemedi, sadece LISP çözümü kullanılacak...
copy "DonatPlus.AutoCAD.Working\DonatPlus.lsp" "%DESKTOP%\" >nul
echo ✓ LISP dosyası masaüstüne kopyalandı

:success
echo.
echo ========================================
echo AUTOCAD KOMUT SORUNU ÇÖZÜLDÜ!
echo ========================================
echo.
echo ✓ Çalışan plugin oluşturuldu
echo ✓ LISP komutları oluşturuldu
echo ✓ Dosyalar masaüstüne kopyalandı
echo.
echo AUTOCAD'DE KULLANIM - YÖNTEM 1 (DLL):
echo 1. AutoCAD'i açın
echo 2. Komut: NETLOAD
echo 3. Masaüstünden DonatPlus.AutoCAD.Working.dll seçin
echo 4. Komut: DONATPLUS
echo.
echo AUTOCAD'DE KULLANIM - YÖNTEM 2 (LISP):
echo 1. AutoCAD'i açın
echo 2. Komut: APPLOAD
echo 3. Masaüstünden DonatPlus.lsp seçin
echo 4. Komut: DONATPLUS
echo.
echo KOMUTLAR:
echo - DONATPLUS: Ana menü
echo - DPANALIZ: Donatı analizi
echo - DPTEST: Test hesaplama
echo - DPHELP: Yardım
echo.
echo Bu yöntemlerden biri kesinlikle çalışacak!
echo.
pause
