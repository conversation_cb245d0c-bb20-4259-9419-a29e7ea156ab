@echo off
echo ========================================
echo DonatPlus Tek Komut Çözümü
echo ========================================
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

echo Bu script tüm sorunları tek seferde çözecek!
echo.
pause

echo.
echo 1. Çalışma dizini kontrol ediliyor...
echo Mevcut dizin: %CD%
if not exist "DonatPlus.UI\DonatPlus.UI.csproj" (
    echo HATA: DonatPlus.UI projesi bulunamadı!
    echo Script'i donatplus klasöründe çalıştırın.
    echo Beklenen konum: C:\Users\<USER>\Desktop\donatplus
    pause
    exit /b 1
)
echo ✓ Proje dosyası bulundu

echo.
echo 2. Proje derleniyor...
dotnet build DonatPlus.UI\DonatPlus.UI.csproj --configuration Release
if %errorLevel% neq 0 (
    echo HATA: UI projesi derlenemedi!
    echo Detaylı hata için şu komutu çalıştırın:
    echo dotnet build DonatPlus.UI\DonatPlus.UI.csproj --configuration Release --verbosity detailed
    pause
    exit /b 1
)

echo.
echo 3. Publish yapılıyor...
dotnet publish DonatPlus.UI\DonatPlus.UI.csproj -c Release -f net6.0-windows --no-self-contained
if %errorLevel% neq 0 (
    echo HATA: Publish başarısız!
    echo Detaylı hata için şu komutu çalıştırın:
    echo dotnet publish DonatPlus.UI\DonatPlus.UI.csproj -c Release -f net6.0-windows --no-self-contained --verbosity detailed
    pause
    exit /b 1
)

echo.
echo 4. Program kurulumu...
set PROGRAM_PATH=C:\Program Files\DonatPlus

if exist "%PROGRAM_PATH%" rmdir /s /q "%PROGRAM_PATH%"
mkdir "%PROGRAM_PATH%"

xcopy "DonatPlus.UI\bin\Release\net6.0-windows\publish\*" "%PROGRAM_PATH%\" /Y /S

echo.
echo 4. Özel başlatıcı oluşturuluyor...

REM PowerShell başlatıcı oluştur
echo # DonatPlus PowerShell Launcher > "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo Write-Host "DonatPlus başlatılıyor..." >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo $env:DOTNET_EnableWriteXorExecute = "0" >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo $env:DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false" >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo $env:DOTNET_ROLL_FORWARD = "Major" >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo Set-Location -Path $PSScriptRoot >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo try { >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo     Start-Process -FilePath ".\DonatPlus.UI.exe" -WorkingDirectory $PSScriptRoot >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo     Write-Host "Program başarıyla başlatıldı!" >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo } catch { >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo     Write-Host "HATA: $($_.Exception.Message)" >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo     Write-Host ".NET 6.0 Desktop Runtime yüklü olduğundan emin olun." >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo     Read-Host "Devam etmek için Enter'a basın" >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"
echo } >> "%PROGRAM_PATH%\DonatPlus_Launcher.ps1"

REM Batch wrapper oluştur
echo @echo off > "%PROGRAM_PATH%\DonatPlus.bat"
echo cd /d "%%~dp0" >> "%PROGRAM_PATH%\DonatPlus.bat"
echo powershell -ExecutionPolicy Bypass -File "DonatPlus_Launcher.ps1" >> "%PROGRAM_PATH%\DonatPlus.bat"

echo.
echo 5. Masaüstü kısayolu...
set DESKTOP=%USERPROFILE%\Desktop

echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus.bat" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "DonatPlus" >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"
cscript "%TEMP%\shortcut.vbs" >nul
del "%TEMP%\shortcut.vbs"

echo.
echo ========================================
echo KURULUM TAMAMLANDI!
echo ========================================
echo.
echo Program kuruldu: %PROGRAM_PATH%
echo Masaüstü kısayolu oluşturuldu
echo.
echo Test etmek için masaüstündeki DonatPlus kısayoluna çift tıklayın!
echo.
pause
