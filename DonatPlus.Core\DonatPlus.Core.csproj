<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <AssemblyTitle>DonatPlus Core Library</AssemblyTitle>
    <AssemblyDescription>Betonarme donatı metraj hesaplama yazılımı - <PERSON> kütüphane</AssemblyDescription>
    <AssemblyCompany>DonatPlus</AssemblyCompany>
    <AssemblyProduct>DonatPlus</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="7.0.3" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
  </ItemGroup>

</Project>
