#if NET48
using System;
using System.Collections.Generic;
using System.Linq;
#endif

namespace DonatPlus.Core.Models
{
    /// <summary>
    /// Donatı elemanını temsil eden model sınıfı
    /// </summary>
    public class RebarElement
    {
        public int Id { get; set; }
        public string ElementType { get; set; } = string.Empty; // Kiriş, Kolon, Döşeme, Perde
        public double Diameter { get; set; } // mm cinsinden çap
        public double Length { get; set; } // m cinsinden uzunluk
        public int Quantity { get; set; } // Adet
        public double Weight { get; set; } // kg cinsinden ağırlık
        public string Grade { get; set; } = "S420"; // Çelik sınıfı
        public string Position { get; set; } = string.Empty; // Pozisyon numarası
        public string Description { get; set; } = string.Empty; // Açıklama
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // AutoCAD'den gelen koordinat bilgileri
        public double StartX { get; set; }
        public double StartY { get; set; }
        public double EndX { get; set; }
        public double EndY { get; set; }
        public string LayerName { get; set; } = string.Empty;
        
        // Hesaplanan değerler
        public double TotalWeight => Weight * Quantity;
        public double TotalLength => Length * Quantity;
        
        // Çelik hasır için özel alanlar
        public bool IsWireMesh { get; set; }
        public double MeshSpacingX { get; set; } // cm
        public double MeshSpacingY { get; set; } // cm
        public double MeshWidth { get; set; } // m
        public double MeshHeight { get; set; } // m
        
        public List<RebarBend> Bends { get; set; } = new List<RebarBend>();
    }
    
    /// <summary>
    /// Donatı büküm bilgilerini temsil eden sınıf
    /// </summary>
    public class RebarBend
    {
        public double Angle { get; set; } // Derece cinsinden açı
        public double Radius { get; set; } // mm cinsinden büküm yarıçapı
        public double X { get; set; } // Büküm noktası X koordinatı
        public double Y { get; set; } // Büküm noktası Y koordinatı
    }
    
    /// <summary>
    /// Donatı hesaplama sonuçlarını temsil eden sınıf
    /// </summary>
    public class RebarCalculationResult
    {
        public List<RebarElement> RebarElements { get; set; } = new List<RebarElement>();
        public double TotalWeight { get; set; }
        public double TotalLength { get; set; }
        public Dictionary<double, double> WeightByDiameter { get; set; } = new Dictionary<double, double>();
        public Dictionary<string, double> WeightByElementType { get; set; } = new Dictionary<string, double>();
        public DateTime CalculationDate { get; set; } = DateTime.Now;
        public string ProjectName { get; set; } = string.Empty;
        public string CalculatedBy { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// Çelik hasır şablonu
    /// </summary>
    public class WireMeshTemplate
    {
        public string Name { get; set; } = string.Empty;
        public double DiameterX { get; set; } // mm
        public double DiameterY { get; set; } // mm
        public double SpacingX { get; set; } // cm
        public double SpacingY { get; set; } // cm
        public double WeightPerSquareMeter { get; set; } // kg/m²
        public string Standard { get; set; } = "TS 648"; // Standart
    }
}
