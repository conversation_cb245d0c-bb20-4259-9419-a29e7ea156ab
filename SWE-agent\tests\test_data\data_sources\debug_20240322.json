[{"repo": "scikit-learn/scikit-learn", "instance_id": "scikit-learn__scikit-learn-11574", "base_commit": "dd69361a0d9c6ccde0d2353b00b86e0e7541a3e3", "patch": "diff --git a/sklearn/ensemble/iforest.py b/sklearn/ensemble/iforest.py\n--- a/sklearn/ensemble/iforest.py\n+++ b/sklearn/ensemble/iforest.py\n@@ -70,6 +70,10 @@ class IsolationForest(BaseBagging, OutlierMixin):\n         on the decision function. If 'auto', the decision function threshold is\n         determined as in the original paper.\n \n+        .. versionchanged:: 0.20\n+           The default value of ``contamination`` will change from 0.1 in 0.20\n+           to ``'auto'`` in 0.22.\n+\n     max_features : int or float, optional (default=1.0)\n         The number of features to draw from X to train each base estimator.\n \n@@ -150,12 +154,6 @@ def __init__(self,\n             n_jobs=n_jobs,\n             random_state=random_state,\n             verbose=verbose)\n-\n-        if contamination == \"legacy\":\n-            warnings.warn('default contamination parameter 0.1 will change '\n-                          'in version 0.22 to \"auto\". This will change the '\n-                          'predict method behavior.',\n-                          DeprecationWarning)\n         self.contamination = contamination\n \n     def _set_oob_score(self, X, y):\n@@ -178,6 +176,15 @@ def fit(self, X, y=None, sample_weight=None):\n         -------\n         self : object\n         \"\"\"\n+        if self.contamination == \"legacy\":\n+            warnings.warn('default contamination parameter 0.1 will change '\n+                          'in version 0.22 to \"auto\". This will change the '\n+                          'predict method behavior.',\n+                          FutureWarning)\n+            self._contamination = 0.1\n+        else:\n+            self._contamination = self.contamination\n+\n         X = check_array(X, accept_sparse=['csc'])\n         if issparse(X):\n             # Pre-sort indices to avoid that each individual tree of the\n@@ -219,19 +226,16 @@ def fit(self, X, y=None, sample_weight=None):\n                                           max_depth=max_depth,\n                                           sample_weight=sample_weight)\n \n-        if self.contamination == \"auto\":\n+        if self._contamination == \"auto\":\n             # 0.5 plays a special role as described in the original paper.\n             # we take the opposite as we consider the opposite of their score.\n             self.offset_ = -0.5\n             # need to save (depreciated) threshold_ in this case:\n             self._threshold_ = sp.stats.scoreatpercentile(\n                 self.score_samples(X), 100. * 0.1)\n-        elif self.contamination == \"legacy\":  # to be rm in 0.22\n-            self.offset_ = sp.stats.scoreatpercentile(\n-                self.score_samples(X), 100. * 0.1)\n         else:\n             self.offset_ = sp.stats.scoreatpercentile(\n-                self.score_samples(X), 100. * self.contamination)\n+                self.score_samples(X), 100. * self._contamination)\n \n         return self\n \n", "test_patch": "diff --git a/sklearn/ensemble/tests/test_iforest.py b/sklearn/ensemble/tests/test_iforest.py\n--- a/sklearn/ensemble/tests/test_iforest.py\n+++ b/sklearn/ensemble/tests/test_iforest.py\n@@ -62,6 +62,7 @@ def test_iforest():\n                             **params).fit(X_train).predict(X_test)\n \n \*****************************('ignore:default contamination')\n def test_iforest_sparse():\n     \"\"\"Check IForest for various parameter settings on sparse input.\"\"\"\n     rng = check_random_state(0)\n@@ -89,6 +90,7 @@ def test_iforest_sparse():\n             assert_array_equal(sparse_results, dense_results)\n \n \*****************************('ignore:default contamination')\n def test_iforest_error():\n     \"\"\"Test that it gives proper exception on deficient input.\"\"\"\n     X = iris.data\n@@ -127,6 +129,7 @@ def test_iforest_error():\n     assert_raises(ValueError, IsolationForest().fit(X).predict, X[:, 1:])\n \n \*****************************('ignore:default contamination')\n def test_recalculate_max_depth():\n     \"\"\"Check max_depth recalculation when max_samples is reset to n_samples\"\"\"\n     X = iris.data\n@@ -135,6 +138,7 @@ def test_recalculate_max_depth():\n         assert_equal(est.max_depth, int(np.ceil(np.log2(X.shape[0]))))\n \n \*****************************('ignore:default contamination')\n def test_max_samples_attribute():\n     X = iris.data\n     clf = IsolationForest().fit(X)\n@@ -150,6 +154,7 @@ def test_max_samples_attribute():\n     assert_equal(clf.max_samples_, 0.4*X.shape[0])\n \n \*****************************('ignore:default contamination')\n def test_iforest_parallel_regression():\n     \"\"\"Check parallel regression.\"\"\"\n     rng = check_random_state(0)\n@@ -174,6 +179,7 @@ def test_iforest_parallel_regression():\n     assert_array_almost_equal(y1, y3)\n \n \*****************************('ignore:default contamination')\n def test_iforest_performance():\n     \"\"\"Test Isolation Forest performs well\"\"\"\n \n@@ -213,6 +219,7 @@ def test_iforest_works():\n         assert_array_equal(pred, 6 * [1] + 2 * [-1])\n \n \*****************************('ignore:default contamination')\n def test_max_samples_consistency():\n     # Make sure validated max_samples in iforest and BaseBagging are identical\n     X = iris.data\n@@ -220,6 +227,7 @@ def test_max_samples_consistency():\n     assert_equal(clf.max_samples_, clf._max_samples)\n \n \*****************************('ignore:default contamination')\n def test_iforest_subsampled_features():\n     # It tests non-regression for #5732 which failed at predict.\n     rng = check_random_state(0)\n@@ -244,6 +252,7 @@ def test_iforest_average_path_length():\n                               [1., result_one, result_two], decimal=10)\n \n \*****************************('ignore:default contamination')\n def test_score_samples():\n     X_train = [[1, 1], [1, 2], [2, 1]]\n     clf1 = IsolationForest(contamination=0.1).fit(X_train)\n@@ -257,12 +266,15 @@ def test_score_samples():\n \n \n def test_deprecation():\n-    assert_warns_message(DeprecationWarning,\n+    X = [[0.0], [1.0]]\n+    clf = IsolationForest()\n+\n+    assert_warns_message(FutureWarning,\n                          'default contamination parameter 0.1 will change '\n                          'in version 0.22 to \"auto\"',\n-                         IsolationForest, )\n-    X = [[0.0], [1.0]]\n-    clf = IsolationForest().fit(X)\n+                         clf.fit, X)\n+\n+    clf = IsolationForest(contamination='auto').fit(X)\n     assert_warns_message(DeprecationWarning,\n                          \"threshold_ attribute is deprecated in 0.20 and will\"\n                          \" be removed in 0.22.\",\ndiff --git a/sklearn/linear_model/tests/test_sag.py b/sklearn/linear_model/tests/test_sag.py\n--- a/sklearn/linear_model/tests/test_sag.py\n+++ b/sklearn/linear_model/tests/test_sag.py\n@@ -17,6 +17,7 @@\n from sklearn.utils.extmath import row_norms\n from sklearn.utils.testing import assert_almost_equal\n from sklearn.utils.testing import assert_array_almost_equal\n+from sklearn.utils.testing import assert_allclose\n from sklearn.utils.testing import assert_greater\n from sklearn.utils.testing import assert_raise_message\n from sklearn.utils.testing import ignore_warnings\n@@ -269,7 +270,6 @@ def test_classifier_matching():\n         assert_array_almost_equal(intercept2, clf.intercept_, decimal=9)\n \n \n-@ignore_warnings\n def test_regressor_matching():\n     n_samples = 10\n     n_features = 5\n@@ -295,10 +295,10 @@ def test_regressor_matching():\n                                dloss=squared_dloss,\n                                fit_intercept=fit_intercept)\n \n-    assert_array_almost_equal(weights1, clf.coef_, decimal=10)\n-    assert_array_almost_equal(intercept1, clf.intercept_, decimal=10)\n-    assert_array_almost_equal(weights2, clf.coef_, decimal=10)\n-    assert_array_almost_equal(intercept2, clf.intercept_, decimal=10)\n+    assert_allclose(weights1, clf.coef_)\n+    assert_allclose(intercept1, clf.intercept_)\n+    assert_allclose(weights2, clf.coef_)\n+    assert_allclose(intercept2, clf.intercept_)\n \n \n @ignore_warnings\n", "problem_statement": "IsolationForest contamination deprecation in __init__ not in fit\nneed to move the deprecation and fix the tests.\n", "hints_text": "", "created_at": "2018-07-16T22:39:16Z", "version": "0.20", "FAIL_TO_PASS": ["sklearn/ensemble/tests/test_iforest.py::test_deprecation"], "PASS_TO_PASS": ["sklearn/ensemble/tests/test_iforest.py::test_iforest", "sklearn/ensemble/tests/test_iforest.py::test_iforest_sparse", "sklearn/ensemble/tests/test_iforest.py::test_iforest_error", "sklearn/ensemble/tests/test_iforest.py::test_recalculate_max_depth", "sklearn/ensemble/tests/test_iforest.py::test_max_samples_attribute", "sklearn/ensemble/tests/test_iforest.py::test_iforest_parallel_regression", "sklearn/ensemble/tests/test_iforest.py::test_iforest_performance", "sklearn/ensemble/tests/test_iforest.py::test_iforest_works", "sklearn/ensemble/tests/test_iforest.py::test_max_samples_consistency", "sklearn/ensemble/tests/test_iforest.py::test_iforest_subsampled_features", "sklearn/ensemble/tests/test_iforest.py::test_iforest_average_path_length", "sklearn/ensemble/tests/test_iforest.py::test_score_samples", "sklearn/linear_model/tests/test_sag.py::test_classifier_matching", "sklearn/linear_model/tests/test_sag.py::test_regressor_matching", "sklearn/linear_model/tests/test_sag.py::test_sag_pobj_matches_logistic_regression", "sklearn/linear_model/tests/test_sag.py::test_sag_pobj_matches_ridge_regression", "sklearn/linear_model/tests/test_sag.py::test_sag_regressor_computed_correctly", "sklearn/linear_model/tests/test_sag.py::test_get_auto_step_size", "sklearn/linear_model/tests/test_sag.py::test_sag_regressor", "sklearn/linear_model/tests/test_sag.py::test_sag_classifier_computed_correctly", "sklearn/linear_model/tests/test_sag.py::test_sag_multiclass_computed_correctly", "sklearn/linear_model/tests/test_sag.py::test_classifier_results", "sklearn/linear_model/tests/test_sag.py::test_binary_classifier_class_weight", "sklearn/linear_model/tests/test_sag.py::test_multiclass_classifier_class_weight", "sklearn/linear_model/tests/test_sag.py::test_classifier_single_class", "sklearn/linear_model/tests/test_sag.py::test_step_size_alpha_error", "sklearn/linear_model/tests/test_sag.py::test_multinomial_loss", "sklearn/linear_model/tests/test_sag.py::test_multinomial_loss_ground_truth"], "environment_setup_commit": "55bf5d93e5674f13a1134d93a11fd0cd11aabcd1"}, {"repo": "django/django", "instance_id": "django__django-15136", "base_commit": "ed2018037d152eef7e68f339b4562f8aadc2b7a0", "patch": "diff --git a/django/contrib/admin/widgets.py b/django/contrib/admin/widgets.py\n--- a/django/contrib/admin/widgets.py\n+++ b/django/contrib/admin/widgets.py\n@@ -8,7 +8,7 @@\n from django.conf import settings\n from django.core.exceptions import ValidationError\n from django.core.validators import URLValidator\n-from django.db.models import CASCADE\n+from django.db.models import CASCADE, UUIDField\n from django.urls import reverse\n from django.urls.exceptions import NoReverseMatch\n from django.utils.html import smart_urlquote\n@@ -149,7 +149,10 @@ def get_context(self, name, value, attrs):\n             context['related_url'] = related_url\n             context['link_title'] = _('Lookup')\n             # The JavaScript code looks for this class.\n-            context['widget']['attrs'].setdefault('class', 'vForeignKeyRawIdAdminField')\n+            css_class = 'vForeignKeyRawIdAdminField'\n+            if isinstance(self.rel.get_related_field(), UUIDField):\n+                css_class += ' vUUIDField'\n+            context['widget']['attrs'].setdefault('class', css_class)\n         else:\n             context['related_url'] = None\n         if context['widget']['value']:\n", "test_patch": "diff --git a/tests/admin_widgets/tests.py b/tests/admin_widgets/tests.py\n--- a/tests/admin_widgets/tests.py\n+++ b/tests/admin_widgets/tests.py\n@@ -26,7 +26,7 @@\n \n from .models import (\n     Advisor, Album, Band, Bee, Car, Company, Event, Honeycomb, Individual,\n-    Inventory, Member, MyFileField, Profile, School, Student,\n+    Inventory, Member, MyFileField, Profile, ReleaseEvent, School, Student,\n     UnsafeLimitChoicesTo, VideoStream,\n )\n from .widgetadmin import site as widget_admin_site\n@@ -538,19 +538,27 @@ def test_render(self):\n         band.album_set.create(\n             name='Hybrid Theory', cover_art=r'albums\\hybrid_theory.jpg'\n         )\n-        rel = Album._meta.get_field('band').remote_field\n-\n-        w = widgets.ForeignKeyRawIdWidget(rel, widget_admin_site)\n+        rel_uuid = Album._meta.get_field('band').remote_field\n+        w = widgets.ForeignKeyRawIdWidget(rel_uuid, widget_admin_site)\n         self.assertHTMLEqual(\n             w.render('test', band.uuid, attrs={}),\n             '<input type=\"text\" name=\"test\" value=\"%(banduuid)s\" '\n-            'class=\"vForeignKeyRawIdAdminField\">'\n+            'class=\"vForeignKeyRawIdAdminField vUUIDField\">'\n             '<a href=\"/admin_widgets/band/?_to_field=uuid\" class=\"related-lookup\" '\n             'id=\"lookup_id_test\" title=\"Lookup\"></a>&nbsp;<strong>'\n             '<a href=\"/admin_widgets/band/%(bandpk)s/change/\">Linkin Park</a>'\n             '</strong>' % {'banduuid': band.uuid, 'bandpk': band.pk}\n         )\n \n+        rel_id = ReleaseEvent._meta.get_field('album').remote_field\n+        w = widgets.ForeignKeyRawIdWidget(rel_id, widget_admin_site)\n+        self.assertHTMLEqual(\n+            w.render('test', None, attrs={}),\n+            '<input type=\"text\" name=\"test\" class=\"vForeignKeyRawIdAdminField\">'\n+            '<a href=\"/admin_widgets/album/?_to_field=id\" class=\"related-lookup\" '\n+            'id=\"lookup_id_test\" title=\"Lookup\"></a>',\n+        )\n+\n     def test_relations_to_non_primary_key(self):\n         # ForeignKeyRawIdWidget works with fields which aren't related to\n         # the model's primary key.\n", "problem_statement": "Admin foreign key raw inputs are too small when referring to a UUID field\nDescription\n\t\nPR: ​https://github.com/django/django/pull/12926\n", "hints_text": "", "created_at": "2021-11-28T06:59:22Z", "version": "4.1", "FAIL_TO_PASS": ["test_render (admin_widgets.tests.ForeignKeyRawIdWidgetTest)"], "PASS_TO_PASS": ["test_CharField (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_DateField (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_DateTimeField (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_EmailField (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_FileField (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_ForeignKey (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_IntegerField (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_TextField (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_TimeField (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_URLField (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_choices_with_radio_fields (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_field_with_choices (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_filtered_many_to_many (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_formfield_overrides (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "formfield_overrides works for a custom field class.", "Overriding the widget for DateTimeField doesn't overrides the default", "The autocomplete_fields, raw_id_fields, filter_vertical, and", "Widget instances in formfield_overrides are not shared between", "test_inheritance (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "m2m fields help text as it applies to admin app (#9321).", "test_many_to_many (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_radio_fields_ForeignKey (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_raw_id_ForeignKey (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_raw_id_many_to_many (admin_widgets.tests.AdminFormfieldForDBFieldTests)", "test_attrs (admin_widgets.tests.AdminTimeWidgetTest)", "test_attrs (admin_widgets.tests.AdminUUIDWidgetTests)", "test_attrs (admin_widgets.tests.AdminDateWidgetTest)", "test_render (admin_widgets.tests.FilteredSelectMultipleWidgetTest)", "test_stacked_render (admin_widgets.tests.FilteredSelectMultipleWidgetTest)", "test_localization (admin_widgets.tests.AdminSplitDateTimeWidgetTest)", "test_render (admin_widgets.tests.AdminSplitDateTimeWidgetTest)", "test_get_context_validates_url (admin_widgets.tests.AdminURLWidgetTest)", "test_render (admin_widgets.tests.AdminURLWidgetTest)", "test_render_idn (admin_widgets.tests.AdminURLWidgetTest)", "WARNING: This test doesn't use assertHTMLEqual since it will get rid", "test_custom_widget_render (admin_widgets.tests.RelatedFieldWidgetWrapperTests)", "test_no_can_add_related (admin_widgets.tests.RelatedFieldWidgetWrapperTests)", "test_on_delete_cascade_rel_cant_delete_related (admin_widgets.tests.RelatedFieldWidgetWrapperTests)", "test_select_multiple_widget_cant_change_delete_related (admin_widgets.tests.RelatedFieldWidgetWrapperTests)", "test_widget_delegates_value_omitted_from_data (admin_widgets.tests.RelatedFieldWidgetWrapperTests)", "test_widget_is_hidden (admin_widgets.tests.RelatedFieldWidgetWrapperTests)", "test_widget_is_not_hidden (admin_widgets.tests.RelatedFieldWidgetWrapperTests)", "test_fk_related_model_not_in_admin (admin_widgets.tests.ForeignKeyRawIdWidgetTest)", "test_fk_to_self_model_not_in_admin (admin_widgets.tests.ForeignKeyRawIdWidgetTest)", "test_proper_manager_for_label_lookup (admin_widgets.tests.ForeignKeyRawIdWidgetTest)", "test_relations_to_non_primary_key (admin_widgets.tests.ForeignKeyRawIdWidgetTest)", "test_render_fk_as_pk_model (admin_widgets.tests.ForeignKeyRawIdWidgetTest)", "test_render_unsafe_limit_choices_to (admin_widgets.tests.ForeignKeyRawIdWidgetTest)", "test_m2m_related_model_not_in_admin (admin_widgets.tests.ManyToManyRawIdWidgetTest)", "test_render (admin_widgets.tests.ManyToManyRawIdWidgetTest)", "Ensure the user can only see their own cars in the foreign key dropdown.", "test_changelist_Foreign<PERSON>ey (admin_widgets.tests.AdminForeignKeyWidgetChangeList)", "File widgets should render as a link when they're marked \"read only.\"", "test_render (admin_widgets.tests.AdminFileWidgetTests)", "test_render_disabled (admin_widgets.tests.AdminFileWidgetTests)", "test_render_required (admin_widgets.tests.AdminFileWidgetTests)", "test_invalid_target_id (admin_widgets.tests.AdminForeignKeyRawIdWidget)", "test_label_and_url_for_value_invalid_uuid (admin_widgets.tests.AdminForeignKeyRawIdWidget)", "test_nonexistent_target_id (admin_widgets.tests.AdminForeignKeyRawIdWidget)", "test_url_params_from_lookup_dict_any_iterable (admin_widgets.tests.AdminForeignKeyRawIdWidget)", "test_url_params_from_lookup_dict_callable (admin_widgets.tests.AdminForeignKeyRawIdWidget)"], "environment_setup_commit": "647480166bfe7532e8c471fef0146e3a17e6c0c9"}, {"repo": "django/django", "instance_id": "django__django-13841", "base_commit": "cfe47b7686df0c4c87270a83d6d7f933323ed7e6", "patch": "diff --git a/django/contrib/auth/password_validation.py b/django/contrib/auth/password_validation.py\n--- a/django/contrib/auth/password_validation.py\n+++ b/django/contrib/auth/password_validation.py\n@@ -8,7 +8,7 @@\n from django.core.exceptions import (\n     FieldDoesNotExist, ImproperlyConfigured, ValidationError,\n )\n-from django.utils.functional import lazy\n+from django.utils.functional import cached_property, lazy\n from django.utils.html import format_html, format_html_join\n from django.utils.module_loading import import_string\n from django.utils.translation import gettext as _, ngettext\n@@ -167,9 +167,14 @@ class CommonPasswordValidator:\n     https://gist.github.com/roycewilliams/281ce539915a947a23db17137d91aeb7\n     The password list must be lowercased to match the comparison in validate().\n     \"\"\"\n-    DEFAULT_PASSWORD_LIST_PATH = Path(__file__).resolve().parent / 'common-passwords.txt.gz'\n+\n+    @cached_property\n+    def DEFAULT_PASSWORD_LIST_PATH(self):\n+        return Path(__file__).resolve().parent / 'common-passwords.txt.gz'\n \n     def __init__(self, password_list_path=DEFAULT_PASSWORD_LIST_PATH):\n+        if password_list_path is CommonPasswordValidator.DEFAULT_PASSWORD_LIST_PATH:\n+            password_list_path = self.DEFAULT_PASSWORD_LIST_PATH\n         try:\n             with gzip.open(password_list_path, 'rt', encoding='utf-8') as f:\n                 self.passwords = {x.strip() for x in f}\ndiff --git a/django/forms/renderers.py b/django/forms/renderers.py\n--- a/django/forms/renderers.py\n+++ b/django/forms/renderers.py\n@@ -7,8 +7,6 @@\n from django.utils.functional import cached_property\n from django.utils.module_loading import import_string\n \n-ROOT = Path(__file__).parent\n-\n \n @functools.lru_cache()\n def get_default_renderer():\n@@ -33,7 +31,7 @@ def get_template(self, template_name):\n     def engine(self):\n         return self.backend({\n             'APP_DIRS': True,\n-            'DIRS': [ROOT / self.backend.app_dirname],\n+            'DIRS': [Path(__file__).parent / self.backend.app_dirname],\n             'NAME': 'djangoforms',\n             'OPTIONS': {},\n         })\ndiff --git a/django/utils/version.py b/django/utils/version.py\n--- a/django/utils/version.py\n+++ b/django/utils/version.py\n@@ -77,6 +77,10 @@ def get_git_changeset():\n     This value isn't guaranteed to be unique, but collisions are very unlikely,\n     so it's sufficient for generating the development version numbers.\n     \"\"\"\n+    # Repository may not be found if __file__ is undefined, e.g. in a frozen\n+    # module.\n+    if '__file__' not in globals():\n+        return None\n     repo_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))\n     git_log = subprocess.run(\n         'git log --pretty=format:%ct --quiet -1 HEAD',\ndiff --git a/django/views/debug.py b/django/views/debug.py\n--- a/django/views/debug.py\n+++ b/django/views/debug.py\n@@ -26,7 +26,15 @@\n     libraries={'i18n': 'django.templatetags.i18n'},\n )\n \n-CURRENT_DIR = Path(__file__).parent\n+\n+def builtin_template_path(name):\n+    \"\"\"\n+    Return a path to a builtin template.\n+\n+    Avoid calling this function at the module level or in a class-definition\n+    because __file__ may not exist, e.g. in frozen environments.\n+    \"\"\"\n+    return Path(__file__).parent / 'templates' / name\n \n \n class ExceptionCycleWarning(UserWarning):\n@@ -248,11 +256,11 @@ class ExceptionReporter:\n \n     @property\n     def html_template_path(self):\n-        return CURRENT_DIR / 'templates' / 'technical_500.html'\n+        return builtin_template_path('technical_500.html')\n \n     @property\n     def text_template_path(self):\n-        return CURRENT_DIR / 'templates' / 'technical_500.txt'\n+        return builtin_template_path('technical_500.txt')\n \n     def __init__(self, request, exc_type, exc_value, tb, is_email=False):\n         self.request = request\n@@ -534,7 +542,7 @@ def technical_404_response(request, exception):\n             module = obj.__module__\n             caller = '%s.%s' % (module, caller)\n \n-    with Path(CURRENT_DIR, 'templates', 'technical_404.html').open(encoding='utf-8') as fh:\n+    with builtin_template_path('technical_404.html').open(encoding='utf-8') as fh:\n         t = DEBUG_ENGINE.from_string(fh.read())\n     reporter_filter = get_default_exception_reporter_filter()\n     c = Context({\n@@ -553,7 +561,7 @@ def technical_404_response(request, exception):\n \n def default_urlconf(request):\n     \"\"\"Create an empty URLconf 404 error response.\"\"\"\n-    with Path(CURRENT_DIR, 'templates', 'default_urlconf.html').open(encoding='utf-8') as fh:\n+    with builtin_template_path('default_urlconf.html').open(encoding='utf-8') as fh:\n         t = DEBUG_ENGINE.from_string(fh.read())\n     c = Context({\n         'version': get_docs_version(),\n", "test_patch": "diff --git a/tests/version/tests.py b/tests/version/tests.py\n--- a/tests/version/tests.py\n+++ b/tests/version/tests.py\n@@ -1,17 +1,37 @@\n+from unittest import skipUnless\n+\n+import django.utils.version\n from django import get_version\n from django.test import SimpleTestCase\n-from django.utils.version import get_complete_version, get_version_tuple\n+from django.utils.version import (\n+    get_complete_version, get_git_changeset, get_version_tuple,\n+)\n \n \n class VersionTests(SimpleTestCase):\n \n     def test_development(self):\n+        get_git_changeset.cache_clear()\n         ver_tuple = (1, 4, 0, 'alpha', 0)\n         # This will return a different result when it's run within or outside\n         # of a git clone: 1.4.devYYYYMMDDHHMMSS or 1.4.\n         ver_string = get_version(ver_tuple)\n         self.assertRegex(ver_string, r'1\\.4(\\.dev[0-9]+)?')\n \n+    @skipUnless(\n+        hasattr(django.utils.version, '__file__'),\n+        'test_development() checks the same when __file__ is already missing, '\n+        'e.g. in a frozen environments'\n+    )\n+    def test_development_no_file(self):\n+        get_git_changeset.cache_clear()\n+        version_file = django.utils.version.__file__\n+        try:\n+            del django.utils.version.__file__\n+            self.test_development()\n+        finally:\n+            django.utils.version.__file__ = version_file\n+\n     def test_releases(self):\n         tuples_to_strings = (\n             ((1, 4, 0, 'alpha', 1), '1.4a1'),\n", "problem_statement": "Access __file__ lazily rather than at module level\nDescription\n\t \n\t\t(last modified by <PERSON>)\n\t \nSo-called frozen Python environments (such as those mentioned in #30950) that do not set all modules' ​__file__ variable, which ​need not be defined, cannot even import Django (without some workarounds) because a small number of Django modules use __file__ at the module level, in a class defined at the module level, or in a function that is called automatically upon import.\nFive modules that use __file__ like this are likely to be imported when using Django and thereby cause a frozen Python to crash with a NameError or similar exception.\nImporting django.forms.renderers can be avoided only by avoiding both forms and the ORM altogether as it's imported from django.db.models.\nImporting django.views.debug might be avoidable if DEBUG=False or by avoiding all of the views and URLs APIs.\ndjango.utils.version's get_git_changeset is called when django is imported in pre-alpha development versions.\nImporting django.contrib.auth.password_validation is only avoidable by not using the Auth app.\ndjango.utils.translation.trans_real uses __file__ to find Django's localization files upon activation; this avoidable only by setting USE_I18N=False. Dealing with trans_real is sufficiently thorny (and, being an English speaker with English-speaking clients, I can avoid it for now) that I will not address it further here except to say that it might need to be part of the larger discussion at #30950.\nWhat this ticket is not\nI am not proposing removing use of __file__ at this time. That would require a longer discussion of intended semantics such as #30950. This ticket is only about removing use of __file__ at the module (or class definition) level in Django application code (not test code). Further I am not proposing banning use of __file__ at the module level at this time, hence minimal new tests and no update to the Django coding style documentation. That too would require a longer conversation.\nProposed fixes\nI have pushed ​PR GH-13841 to address the four of those modules other than trans_real. I dealt with each module's use of __file__ in separate commits to make them easier to discuss and separate/cherry-pick if needed. Below I link to the individual commits as I discuss each of the four modules. These first two are fairly easy, but the second two may require further consideration.\ndjango.forms.renders (​54d539c)\nRemove the undocumented module constant ROOT and replace its single use.\ndjango.utils.version (​f4edc6e)\nTreat the lack of module-global __file__ the same as a failure of git log by returning None from get_git_changeset.\ndjango.views.debug (​07f46b7)\nThe module-level constant CURRENT_DIR is used only in the module itself and is undocumented, so I'm assuming it's an obscure private symbol that no one will miss. I've replaced it with a module-level private function _builtin_template_path that refactors and centralizes finding built-in templates for the entire module.\nThe one tricky part is that #32105 added the html_template_path and text_template_path attributes django.views.debug.ExceptionReporter. I didn't want to disturb #32105's goal of making the template paths easily override-able, so I avoided calling _builtin_template_path in the class definition by making detecting the presence of the attributes in __init__ and setting defaults there. Alternatives include making the attributes properties with setters or cached properties without setters.\ndjango.contrib.auth.password_validation (​24aa80b)\nThe CommonPasswordValidator-class constant DEFAULT_PASSWORD_LIST_PATH is used only in one place, the class's instance constructor. While the nature of DEFAULT_PASSWORD_LIST_PATH is not documented, its existence is inside the docs for the ​constructor's signature. I've changed DEFAULT_PASSWORD_LIST_PATH from a class constant into an instance attribute. Another possibility is making DEFAULT_PASSWORD_LIST_PATH be a django.utils.functional.classproperty.\n", "hints_text": "", "created_at": "2021-01-04T20:40:02Z", "version": "4.0", "FAIL_TO_PASS": ["test_development_no_file (version.tests.VersionTests)"], "PASS_TO_PASS": ["test_development (version.tests.VersionTests)", "test_get_version_invalid_version (version.tests.VersionTests)", "test_get_version_tuple (version.tests.VersionTests)", "test_releases (version.tests.VersionTests)"], "environment_setup_commit": "475cffd1d64c690cdad16ede4d5e81985738ceb4"}, {"repo": "django/django", "instance_id": "django__django-17029", "base_commit": "953f29f700a60fc09b08b2c2270c12c447490c6a", "patch": "diff --git a/django/apps/registry.py b/django/apps/registry.py\n--- a/django/apps/registry.py\n+++ b/django/apps/registry.py\n@@ -373,6 +373,7 @@ def clear_cache(self):\n \n         This is mostly used in tests.\n         \"\"\"\n+        self.get_swappable_settings_name.cache_clear()\n         # Call expire cache on each model. This will purge\n         # the relation tree and the fields cache.\n         self.get_models.cache_clear()\n", "test_patch": "diff --git a/tests/apps/tests.py b/tests/apps/tests.py\n--- a/tests/apps/tests.py\n+++ b/tests/apps/tests.py\n@@ -197,6 +197,17 @@ def test_get_model(self):\n         with self.assertRaises(ValueError):\n             apps.get_model(\"admin_LogEntry\")\n \n+    @override_settings(INSTALLED_APPS=SOME_INSTALLED_APPS)\n+    def test_clear_cache(self):\n+        # Set cache.\n+        self.assertIsNone(apps.get_swappable_settings_name(\"admin.LogEntry\"))\n+        apps.get_models()\n+\n+        apps.clear_cache()\n+\n+        self.assertEqual(apps.get_swappable_settings_name.cache_info().currsize, 0)\n+        self.assertEqual(apps.get_models.cache_info().currsize, 0)\n+\n     @override_settings(INSTALLED_APPS=[\"apps.apps.RelabeledAppsConfig\"])\n     def test_relabeling(self):\n         self.assertEqual(apps.get_app_config(\"relabeled\").name, \"apps\")\n", "problem_statement": "Apps.clear_cache() does not clear get_swappable_settings_name cache.\nDescription\n\t\nWe use apps.clear_cache() in django-stubs to be able to reset the previous state on consequential mypy runs.\nCode: ​https://github.com/typeddjango/django-stubs/pull/1601/files#diff-c49d8fe2cd0a58fad3c36ab3a88c7745e9622f3098e60cd512953eb17b8a1994R63-R64\nBut, looks like we don't clear all the object's cache this way, because get_swappable_settings_name (which is a functools._lru_cache_wrapper) is not cleared.\nI think that this is not correct. .clear_cache doc states: Clear all internal caches, for methods that alter the app registry.\nLooks like that is not the case.\nI propose to add: self.get_swappable_settings_name.cache_clear() line to def clear_cache.\nIf others agree, I will make a PR.\nOriginal discussion: ​https://github.com/typeddjango/django-stubs/pull/1601#discussion_r1246344533\n", "hints_text": "Thanks for the report, tentatively accepted.", "created_at": "2023-06-29T13:18:26Z", "version": "5.0", "FAIL_TO_PASS": ["test_clear_cache (apps.tests.AppsTests.test_clear_cache)"], "PASS_TO_PASS": ["test_app_default_auto_field (apps.tests.AppConfigTests.test_app_default_auto_field)", "test_default_auto_field_setting (apps.tests.AppConfigTests.test_default_auto_field_setting)", "If single element in __path__, use it (in preference to __file__).", "If the __path__ attr contains duplicate paths and there is no", "If the __path__ attr is empty, use __file__ if set.", "If the __path__ attr is empty and there is no __file__, raise.", "If path set as class attr, overrides __path__ and __file__.", "test_invalid_label (apps.tests.AppConfigTests.test_invalid_label)", "If the __path__ attr is length>1, use __file__ if set.", "If the __path__ attr is length>1 and there is no __file__, raise.", "If there is no __path__ attr, use __file__.", "If there is no __path__ or __file__, raise ImproperlyConfigured.", "If subclass sets path as class attr, no module attributes needed.", "test_repr (apps.tests.AppConfigTests.test_repr)", "A Py3.3+ namespace package with multiple locations cannot be an app.", "Multiple locations are ok only if app-config has explicit path.", "A Py3.3+ namespace package can be an app if it has only one path.", "Tests when INSTALLED_APPS contains an incorrect app config.", "test_duplicate_labels (apps.tests.AppsTests.test_duplicate_labels)", "test_duplicate_names (apps.tests.AppsTests.test_duplicate_names)", "Makes a new model at runtime and ensures it goes into the right place.", "Tests apps.get_app_config().", "Tests apps.get_app_configs().", "apps.get_containing_app_config() should raise an exception if", "Tests apps.get_model().", "App discovery should preserve stack traces. Regression test for #22920.", "Tests apps.is_installed().", "Tests apps.lazy_model_operation().", "Test for behavior when two models clash in the app registry.", "apps.get_models() raises an exception if apps.models_ready isn't True.", "The models in the models.py file were loaded correctly.", "Load an app that doesn't provide an AppConfig class.", "Tests when INSTALLED_APPS contains an app that doesn't exist, either", "test_no_such_app_config (apps.tests.AppsTests.test_no_such_app_config)", "test_no_such_app_config_with_choices (apps.tests.AppsTests.test_no_such_app_config_with_choices)", "Tests when INSTALLED_APPS contains a class that isn't an app config.", "Load an app that provides an AppConfig class.", "Tests the ready property of the main registry.", "test_relabeling (apps.tests.AppsTests.test_relabeling)", "Only one main registry can exist.", "Load an app that provides two AppConfig classes.", "Load an app that provides two AppConfig classes, one being the default.", "Load an app that provides two default AppConfig classes."], "environment_setup_commit": "4a72da71001f154ea60906a2f74898d32b7322a7"}, {"repo": "django/django", "instance_id": "django__django-13251", "base_commit": "b6dfdaff33f19757b1cb9b3bf1d17f28b94859d4", "patch": "diff --git a/django/db/models/query.py b/django/db/models/query.py\n--- a/django/db/models/query.py\n+++ b/django/db/models/query.py\n@@ -204,7 +204,7 @@ def __init__(self, model=None, query=None, using=None, hints=None):\n     def query(self):\n         if self._deferred_filter:\n             negate, args, kwargs = self._deferred_filter\n-            self._filter_or_exclude_inplace(negate, *args, **kwargs)\n+            self._filter_or_exclude_inplace(negate, args, kwargs)\n             self._deferred_filter = None\n         return self._query\n \n@@ -939,7 +939,7 @@ def filter(self, *args, **kwargs):\n         set.\n         \"\"\"\n         self._not_support_combined_queries('filter')\n-        return self._filter_or_exclude(False, *args, **kwargs)\n+        return self._filter_or_exclude(False, args, kwargs)\n \n     def exclude(self, *args, **kwargs):\n         \"\"\"\n@@ -947,9 +947,9 @@ def exclude(self, *args, **kwargs):\n         set.\n         \"\"\"\n         self._not_support_combined_queries('exclude')\n-        return self._filter_or_exclude(True, *args, **kwargs)\n+        return self._filter_or_exclude(True, args, kwargs)\n \n-    def _filter_or_exclude(self, negate, *args, **kwargs):\n+    def _filter_or_exclude(self, negate, args, kwargs):\n         if args or kwargs:\n             assert not self.query.is_sliced, \\\n                 \"Cannot filter a query once a slice has been taken.\"\n@@ -959,10 +959,10 @@ def _filter_or_exclude(self, negate, *args, **kwargs):\n             self._defer_next_filter = False\n             clone._deferred_filter = negate, args, kwargs\n         else:\n-            clone._filter_or_exclude_inplace(negate, *args, **kwargs)\n+            clone._filter_or_exclude_inplace(negate, args, kwargs)\n         return clone\n \n-    def _filter_or_exclude_inplace(self, negate, *args, **kwargs):\n+    def _filter_or_exclude_inplace(self, negate, args, kwargs):\n         if negate:\n             self._query.add_q(~Q(*args, **kwargs))\n         else:\n@@ -983,7 +983,7 @@ def complex_filter(self, filter_obj):\n             clone.query.add_q(filter_obj)\n             return clone\n         else:\n-            return self._filter_or_exclude(False, **filter_obj)\n+            return self._filter_or_exclude(False, args=(), kwargs=filter_obj)\n \n     def _combinator_query(self, combinator, *other_qs, all=False):\n         # Clone the query to inherit the select list and everything\n", "test_patch": "diff --git a/tests/queries/models.py b/tests/queries/models.py\n--- a/tests/queries/models.py\n+++ b/tests/queries/models.py\n@@ -42,6 +42,7 @@ class Note(models.Model):\n     note = models.CharField(max_length=100)\n     misc = models.CharField(max_length=10)\n     tag = models.ForeignKey(Tag, models.SET_NULL, blank=True, null=True)\n+    negate = models.BooleanField(default=True)\n \n     class Meta:\n         ordering = ['note']\ndiff --git a/tests/queries/tests.py b/tests/queries/tests.py\n--- a/tests/queries/tests.py\n+++ b/tests/queries/tests.py\n@@ -47,7 +47,7 @@ def setUpTestData(cls):\n \n         cls.n1 = Note.objects.create(note='n1', misc='foo', id=1)\n         cls.n2 = Note.objects.create(note='n2', misc='bar', id=2)\n-        cls.n3 = Note.objects.create(note='n3', misc='foo', id=3)\n+        cls.n3 = Note.objects.create(note='n3', misc='foo', id=3, negate=False)\n \n         ann1 = Annotation.objects.create(name='a1', tag=cls.t1)\n         ann1.notes.add(cls.n1)\n@@ -1216,6 +1216,13 @@ def test_field_with_filterable(self):\n             [self.a3, self.a4],\n         )\n \n+    def test_negate_field(self):\n+        self.assertSequenceEqual(\n+            Note.objects.filter(negate=True),\n+            [self.n1, self.n2],\n+        )\n+        self.assertSequenceEqual(Note.objects.exclude(negate=True), [self.n3])\n+\n \n class Queries2Tests(TestCase):\n     @classmethod\n", "problem_statement": "Filtering on a field named `negate` raises a TypeError\nDescription\n\t\nFiltering on a model with a field named negate raises a TypeError.\nFor example:\nclass Foo(models.Model):\n\tnegate = models.BooleanField()\nFoo.objects.filter(negate=True)\nraises TypeError: _filter_or_exclude() got multiple values for argument 'negate'\nnegate is not documented as a reserved argument for .filter(). I'm currently using .filter(negate__exact=True) as a workaround.\n", "hints_text": "We should either document this limitation or change _filter_or_exclude and friends signature from (negate, *args, **kwargs) to (negate, args, kwargs). I think the second approach is favourable as there's not much benefits in using arguments unpacking in these private methods as long as the public methods filter and exclude preserve their signature.\n<PERSON>, would you be interested in submitting a PR with the changes below plus a regression test in tests/query/tests.py? django/db/models/query.py diff --git a/django/db/models/query.py b/django/db/models/query.py index 07d6ffd4ca..d655ede8d9 100644 a b class QuerySet: 204204 def query(self): 205205 if self._deferred_filter: 206206 negate, args, kwargs = self._deferred_filter 207 self._filter_or_exclude_inplace(negate, *args, **kwargs) 207 self._filter_or_exclude_inplace(negate, args, kwargs) 208208 self._deferred_filter = None 209209 return self._query 210210 … … class QuerySet: 939939 set. 940940 \"\"\" 941941 self._not_support_combined_queries('filter') 942 return self._filter_or_exclude(False, *args, **kwargs) 942 return self._filter_or_exclude(False, args, kwargs) 943943 944944 def exclude(self, *args, **kwargs): 945945 \"\"\" … … class QuerySet: 947947 set. 948948 \"\"\" 949949 self._not_support_combined_queries('exclude') 950 return self._filter_or_exclude(True, *args, **kwargs) 950 return self._filter_or_exclude(True, args, kwargs) 951951 952 def _filter_or_exclude(self, negate, *args, **kwargs): 952 def _filter_or_exclude(self, negate, args, kwargs): 953953 if args or kwargs: 954954 assert not self.query.is_sliced, \\ 955955 \"Cannot filter a query once a slice has been taken.\" … … class QuerySet: 959959 self._defer_next_filter = False 960960 clone._deferred_filter = negate, args, kwargs 961961 else: 962 clone._filter_or_exclude_inplace(negate, *args, **kwargs) 962 clone._filter_or_exclude_inplace(negate, args, kwargs) 963963 return clone 964964 965 def _filter_or_exclude_inplace(self, negate, *args, **kwargs): 965 def _filter_or_exclude_inplace(self, negate, args, kwargs): 966966 if negate: 967967 self._query.add_q(~Q(*args, **kwargs)) 968968 else: … … class QuerySet: 983983 clone.query.add_q(filter_obj) 984984 return clone 985985 else: 986 return self._filter_or_exclude(False, **filter_obj) 986 return self._filter_or_exclude(False, args=(), kwargs=filter_obj) 987987 988988 def _combinator_query(self, combinator, *other_qs, all=False): 989989 # Clone the query to inherit the select list and everything\nReplying to Simon Charette: Aaron, would you be interested in submitting a PR with the changes below plus a regression test in tests/query/tests.py? Sure! I can do that this evening. Thanks for the patch :)", "created_at": "2020-07-28T11:59:53Z", "version": "3.2", "FAIL_TO_PASS": ["test_negate_field (queries.tests.Queries1Tests)"], "PASS_TO_PASS": ["test_ticket7371 (queries.tests.CustomPkTests)", "test_no_extra_params (queries.tests.DefaultValuesInsertTest)", "test_ticket_21879 (queries.tests.ReverseM2MCustomPkTests)", "test_invalid_values (queries.tests.TestInvalidValuesRelation)", "test_ticket_7302 (queries.tests.EscapingTests)", "test_emptyqueryset_values (queries.tests.EmptyQuerySetTests)", "test_ticket_19151 (queries.tests.EmptyQuerySetTests)", "test_values_subquery (queries.tests.EmptyQuerySetTests)", "test_ticket14729 (queries.tests.RawQueriesTests)", "test_empty_full_handling_conjunction (queries.tests.WhereNodeTest)", "test_empty_full_handling_disjunction (queries.tests.WhereNodeTest)", "test_empty_nodes (queries.tests.WhereNodeTest)", "test_evaluated_proxy_count (queries.tests.ProxyQueryCleanupTest)", "#13227 -- If a queryset is already evaluated, it can still be used as a query arg", "test_no_fields_cloning (queries.tests.CloneTests)", "test_no_model_options_cloning (queries.tests.CloneTests)", "test_in_list_limit (queries.tests.ConditionalTests)", "test_infinite_loop (queries.tests.ConditionalTests)", "test_double_subquery_in (queries.tests.DoubleInSubqueryTests)", "test_ticket8597 (queries.tests.ComparisonTests)", "test_ticket22023 (queries.tests.Queries3Tests)", "test_ticket7107 (queries.tests.Queries3Tests)", "test_ticket8683 (queries.tests.Queries3Tests)", "test_ticket7872 (queries.tests.DisjunctiveFilterTests)", "test_ticket8283 (queries.tests.DisjunctiveFilterTests)", "test_ticket10432 (queries.tests.GeneratorExpressionTests)", "test_ticket15786 (queries.tests.Exclude15786)", "test_ticket_24278 (queries.tests.TestTicket24279)", "test_tickets_3045_3288 (queries.tests.SelectRelatedTests)", "test_ticket_18785 (queries.tests.Ticket18785Tests)", "test_ticket_14056 (queries.tests.Ticket14056Tests)", "test_invalid_order_by (queries.tests.QuerySetExceptionTests)", "test_invalid_queryset_model (queries.tests.QuerySetExceptionTests)", "test_iter_exceptions (queries.tests.QuerySetExceptionTests)", "test_21001 (queries.tests.EmptyStringsAsNullTest)", "test_direct_exclude (queries.tests.EmptyStringsAsNullTest)", "test_joined_exclude (queries.tests.EmptyStringsAsNullTest)", "test_exists (queries.tests.ExistsSql)", "test_ticket_18414 (queries.tests.ExistsSql)", "test_ticket_21203 (queries.tests.Ticket21203Tests)", "test_annotated_ordering (queries.tests.QuerysetOrderedTests)", "test_cleared_default_ordering (queries.tests.QuerysetOrderedTests)", "test_empty_queryset (queries.tests.QuerysetOrderedTests)", "test_explicit_ordering (queries.tests.QuerysetOrderedTests)", "test_no_default_or_explicit_ordering (queries.tests.QuerysetOrderedTests)", "test_order_by_extra (queries.tests.QuerysetOrderedTests)", "test_reverse_trimming (queries.tests.ReverseJoinTrimmingTest)", "test_empty_string_promotion (queries.tests.EmptyStringPromotionTests)", "test_join_already_in_query (queries.tests.NullableRelOrderingTests)", "test_ticket10028 (queries.tests.NullableRelOrderingTests)", "test_values_in_subquery (queries.tests.ValuesSubqueryTests)", "test_ticket_12807 (queries.tests.Ticket12807Tests)", "test_ticket_24605 (queries.tests.TestTicket24605)", "test_ticket_21787 (queries.tests.ForeignKeyToBaseExcludeTests)", "test_ticket_19964 (queries.tests.RelabelCloneTest)", "test_ticket7778 (queries.tests.SubclassFKTests)", "test_primary_key (queries.tests.IsNullTests)", "test_to_field (queries.tests.IsNullTests)", "test_ticket_22429 (queries.tests.Ticket22429Tests)", "test_double_exclude (queries.tests.NullInExcludeTest)", "test_null_in_exclude_qs (queries.tests.NullInExcludeTest)", "test_ticket_20788 (queries.tests.Ticket20788Tests)", "test_ticket_20101 (queries.tests.Ticket20101Tests)", "test_non_nullable_fk_not_promoted (queries.tests.ValuesJoinPromotionTests)", "test_ticket_21376 (queries.tests.ValuesJoinPromotionTests)", "test_values_no_promotion_for_existing (queries.tests.ValuesJoinPromotionTests)", "test_fk_reuse (queries.tests.JoinReuseTest)", "test_fk_reuse_annotation (queries.tests.JoinReuseTest)", "test_fk_reuse_disjunction (queries.tests.JoinReuseTest)", "test_fk_reuse_order_by (queries.tests.JoinReuseTest)", "test_fk_reuse_select_related (queries.tests.JoinReuseTest)", "test_inverted_q_across_relations (queries.tests.JoinReuseTest)", "test_revfk_noreuse (queries.tests.JoinReuseTest)", "test_revo2o_reuse (queries.tests.JoinReuseTest)", "test_ticket_20955 (queries.tests.Ticket20955Tests)", "test_empty_resultset_sql (queries.tests.WeirdQuerysetSlicingTests)", "test_empty_sliced_subquery (queries.tests.WeirdQuerysetSlicingTests)", "test_empty_sliced_subquery_exclude (queries.tests.WeirdQuerysetSlicingTests)", "test_tickets_7698_10202 (queries.tests.WeirdQuerysetSlicingTests)", "test_zero_length_values_slicing (queries.tests.WeirdQuerysetSlicingTests)", "test_correct_lookup (queries.tests.RelatedLookupTypeTests)", "test_values_queryset_lookup (queries.tests.RelatedLookupTypeTests)", "test_wrong_backward_lookup (queries.tests.RelatedLookupTypeTests)", "test_wrong_type_lookup (queries.tests.RelatedLookupTypeTests)", "test_exclude_plain (queries.tests.ExcludeTest17600)", "test_exclude_plain_distinct (queries.tests.ExcludeTest17600)", "test_exclude_with_q_is_equal_to_plain_exclude (queries.tests.ExcludeTest17600)", "test_exclude_with_q_is_equal_to_plain_exclude_variation (queries.tests.ExcludeTest17600)", "test_exclude_with_q_object_distinct (queries.tests.ExcludeTest17600)", "test_exclude_with_q_object_no_distinct (queries.tests.ExcludeTest17600)", "test_can_combine_queries_using_and_and_or_operators (queries.tests.QuerySetSupportsPythonIdioms)", "test_can_get_items_using_index_and_slice_notation (queries.tests.QuerySetSupportsPythonIdioms)", "test_can_get_number_of_items_in_queryset_using_standard_len (queries.tests.QuerySetSupportsPythonIdioms)", "test_invalid_index (queries.tests.QuerySetSupportsPythonIdioms)", "test_slicing_can_slice_again_after_slicing (queries.tests.QuerySetSupportsPythonIdioms)", "test_slicing_cannot_combine_queries_once_sliced (queries.tests.QuerySetSupportsPythonIdioms)", "test_slicing_cannot_filter_queryset_once_sliced (queries.tests.QuerySetSupportsPythonIdioms)", "test_slicing_cannot_reorder_queryset_once_sliced (queries.tests.QuerySetSupportsPythonIdioms)", "hint: inverting your ordering might do what you need", "test_slicing_with_steps_can_be_used (queries.tests.QuerySetSupportsPythonIdioms)", "test_slicing_with_tests_is_not_lazy (queries.tests.QuerySetSupportsPythonIdioms)", "test_slicing_without_step_is_lazy (queries.tests.QuerySetSupportsPythonIdioms)", "test_ticket12239 (queries.tests.Queries2Tests)", "test_ticket4289 (queries.tests.Queries2Tests)", "test_ticket7759 (queries.tests.Queries2Tests)", "test_ticket_23605 (queries.tests.Ticket23605Tests)", "test_exclude_many_to_many (queries.tests.ManyToManyExcludeTest)", "test_ticket_12823 (queries.tests.ManyToManyExcludeTest)", "test_or_with_both_slice (queries.tests.QuerySetBitwiseOperationTests)", "test_or_with_both_slice_and_ordering (queries.tests.QuerySetBitwiseOperationTests)", "test_or_with_lhs_slice (queries.tests.QuerySetBitwiseOperationTests)", "test_or_with_rhs_slice (queries.tests.QuerySetBitwiseOperationTests)", "test_disjunction_promotion1 (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion2 (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion3 (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion3_demote (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion4 (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion4_demote (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion5_demote (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion6 (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion7 (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion_fexpression (queries.tests.DisjunctionPromotionTests)", "test_disjunction_promotion_select_related (queries.tests.DisjunctionPromotionTests)", "test_in_query (queries.tests.ToFieldTests)", "test_in_subquery (queries.tests.ToFieldTests)", "test_nested_in_subquery (queries.tests.ToFieldTests)", "test_recursive_fk (queries.tests.ToFieldTests)", "test_recursive_fk_reverse (queries.tests.ToFieldTests)", "test_reverse_in (queries.tests.ToFieldTests)", "test_single_object (queries.tests.ToFieldTests)", "test_single_object_reverse (queries.tests.ToFieldTests)", "test_extra_select_literal_percent_s (queries.tests.Queries5Tests)", "test_ordering (queries.tests.Queries5Tests)", "test_ticket5261 (queries.tests.Queries5Tests)", "test_ticket7045 (queries.tests.Queries5Tests)", "test_ticket7256 (queries.tests.Queries5Tests)", "test_ticket9848 (queries.tests.Queries5Tests)", "test_distinct_ordered_sliced_subquery_aggregation (queries.tests.Queries6Tests)", "test_multiple_columns_with_the_same_name_slice (queries.tests.Queries6Tests)", "test_nested_queries_sql (queries.tests.Queries6Tests)", "test_parallel_iterators (queries.tests.Queries6Tests)", "test_ticket3739 (queries.tests.Queries6Tests)", "test_ticket_11320 (queries.tests.Queries6Tests)", "test_tickets_8921_9188 (queries.tests.Queries6Tests)", "test_extra_multiple_select_params_values_order_by (queries.tests.ValuesQuerysetTests)", "test_extra_select_params_values_order_in_extra (queries.tests.ValuesQuerysetTests)", "test_extra_values (queries.tests.ValuesQuerysetTests)", "test_extra_values_list (queries.tests.ValuesQuerysetTests)", "test_extra_values_order_in_extra (queries.tests.ValuesQuerysetTests)", "test_extra_values_order_multiple (queries.tests.ValuesQuerysetTests)", "test_extra_values_order_twice (queries.tests.ValuesQuerysetTests)", "test_field_error_values_list (queries.tests.ValuesQuerysetTests)", "test_flat_extra_values_list (queries.tests.ValuesQuerysetTests)", "test_flat_values_list (queries.tests.ValuesQuerysetTests)", "test_named_values_list_bad_field_name (queries.tests.ValuesQuerysetTests)", "test_named_values_list_expression (queries.tests.ValuesQuerysetTests)", "test_named_values_list_expression_with_default_alias (queries.tests.ValuesQuerysetTests)", "test_named_values_list_flat (queries.tests.ValuesQuerysetTests)", "test_named_values_list_with_fields (queries.tests.ValuesQuerysetTests)", "test_named_values_list_without_fields (queries.tests.ValuesQuerysetTests)", "test_AB_ACB (queries.tests.UnionTests)", "test_A_AB (queries.tests.UnionTests)", "test_A_AB2 (queries.tests.UnionTests)", "test_BAB_BAC (queries.tests.UnionTests)", "test_BAB_BACB (queries.tests.UnionTests)", "test_BA_BCA__BAB_BAC_BCA (queries.tests.UnionTests)", "test_distinct_ordered_sliced_subquery (queries.tests.SubqueryTests)", "Subselects honor any manual ordering", "test_related_sliced_subquery (queries.tests.SubqueryTests)", "test_slice_subquery_and_query (queries.tests.SubqueryTests)", "Delete queries can safely contain sliced subqueries", "test_isnull_filter_promotion (queries.tests.NullJoinPromotionOrTest)", "test_null_join_demotion (queries.tests.NullJoinPromotionOrTest)", "test_ticket_17886 (queries.tests.NullJoinPromotionOrTest)", "test_ticket_21366 (queries.tests.NullJoinPromotionOrTest)", "test_ticket_21748 (queries.tests.NullJoinPromotionOrTest)", "test_ticket_21748_complex_filter (queries.tests.NullJoinPromotionOrTest)", "test_ticket_21748_double_negated_and (queries.tests.NullJoinPromotionOrTest)", "test_ticket_21748_double_negated_or (queries.tests.NullJoinPromotionOrTest)", "test_exclude_nullable_fields (queries.tests.ExcludeTests)", "test_exclude_reverse_fk_field_ref (queries.tests.ExcludeTests)", "test_exclude_with_circular_fk_relation (queries.tests.ExcludeTests)", "test_subquery_exclude_outerref (queries.tests.ExcludeTests)", "test_ticket14511 (queries.tests.ExcludeTests)", "test_to_field (queries.tests.ExcludeTests)", "test_combine_join_reuse (queries.tests.Queries4Tests)", "test_filter_reverse_non_integer_pk (queries.tests.Queries4Tests)", "test_join_reuse_order (queries.tests.Queries4Tests)", "test_order_by_resetting (queries.tests.Queries4Tests)", "test_order_by_reverse_fk (queries.tests.Queries4Tests)", "test_ticket10181 (queries.tests.Queries4Tests)", "test_ticket11811 (queries.tests.Queries4Tests)", "test_ticket14876 (queries.tests.Queries4Tests)", "test_ticket15316_exclude_false (queries.tests.Queries4Tests)", "test_ticket15316_exclude_true (queries.tests.Queries4Tests)", "test_ticket15316_filter_false (queries.tests.Queries4Tests)", "test_ticket15316_filter_true (queries.tests.Queries4Tests)", "test_ticket15316_one2one_exclude_false (queries.tests.Queries4Tests)", "test_ticket15316_one2one_exclude_true (queries.tests.Queries4Tests)", "test_ticket15316_one2one_filter_false (queries.tests.Queries4Tests)", "test_ticket15316_one2one_filter_true (queries.tests.Queries4Tests)", "test_ticket24525 (queries.tests.Queries4Tests)", "test_ticket7095 (queries.tests.Queries4Tests)", "test_avoid_infinite_loop_on_too_many_subqueries (queries.tests.Queries1Tests)", "test_common_mixed_case_foreign_keys (queries.tests.Queries1Tests)", "test_deferred_load_qs_pickling (queries.tests.Queries1Tests)", "test_double_exclude (queries.tests.Queries1Tests)", "test_error_raised_on_filter_with_dictionary (queries.tests.Queries1Tests)", "test_exclude (queries.tests.Queries1Tests)", "test_exclude_in (queries.tests.Queries1Tests)", "test_excluded_intermediary_m2m_table_joined (queries.tests.Queries1Tests)", "test_field_with_filterable (queries.tests.Queries1Tests)", "test_get_clears_ordering (queries.tests.Queries1Tests)", "test_heterogeneous_qs_combination (queries.tests.Queries1Tests)", "test_lookup_constraint_fielderror (queries.tests.Queries1Tests)", "test_nested_exclude (queries.tests.Queries1Tests)", "test_order_by_join_unref (queries.tests.Queries1Tests)", "test_order_by_raw_column_alias_warning (queries.tests.Queries1Tests)", "test_order_by_rawsql (queries.tests.Queries1Tests)", "test_order_by_tables (queries.tests.Queries1Tests)", "test_reasonable_number_of_subq_aliases (queries.tests.Queries1Tests)", "test_subquery_condition (queries.tests.Queries1Tests)", "test_ticket10205 (queries.tests.Queries1Tests)", "test_ticket10432 (queries.tests.Queries1Tests)", "test_ticket1050 (queries.tests.Queries1Tests)", "test_ticket10742 (queries.tests.Queries1Tests)", "test_ticket17429 (queries.tests.Queries1Tests)", "test_ticket1801 (queries.tests.Queries1Tests)", "test_ticket19672 (queries.tests.Queries1Tests)", "test_ticket2091 (queries.tests.Queries1Tests)", "test_ticket2253 (queries.tests.Queries1Tests)", "test_ticket2306 (queries.tests.Queries1Tests)", "test_ticket2400 (queries.tests.Queries1Tests)", "test_ticket2496 (queries.tests.Queries1Tests)", "test_ticket3037 (queries.tests.Queries1Tests)", "test_ticket3141 (queries.tests.Queries1Tests)", "test_ticket4358 (queries.tests.Queries1Tests)", "test_ticket4464 (queries.tests.Queries1Tests)", "test_ticket4510 (queries.tests.Queries1Tests)", "test_ticket6074 (queries.tests.Queries1Tests)", "test_ticket6154 (queries.tests.Queries1Tests)", "test_ticket6981 (queries.tests.Queries1Tests)", "test_ticket7076 (queries.tests.Queries1Tests)", "test_ticket7096 (queries.tests.Queries1Tests)", "test_ticket7098 (queries.tests.Queries1Tests)", "test_ticket7155 (queries.tests.Queries1Tests)", "test_ticket7181 (queries.tests.Queries1Tests)", "test_ticket7235 (queries.tests.Queries1Tests)", "test_ticket7277 (queries.tests.Queries1Tests)", "test_ticket7323 (queries.tests.Queries1Tests)", "test_ticket7378 (queries.tests.Queries1Tests)", "test_ticket7791 (queries.tests.Queries1Tests)", "test_ticket7813 (queries.tests.Queries1Tests)", "test_ticket8439 (queries.tests.Queries1Tests)", "test_ticket9411 (queries.tests.Queries1Tests)", "test_ticket9926 (queries.tests.Queries1Tests)", "test_ticket9985 (queries.tests.Queries1Tests)", "test_ticket9997 (queries.tests.Queries1Tests)", "test_ticket_10790_1 (queries.tests.Queries1Tests)", "test_ticket_10790_2 (queries.tests.Queries1Tests)", "test_ticket_10790_3 (queries.tests.Queries1Tests)", "test_ticket_10790_4 (queries.tests.Queries1Tests)", "test_ticket_10790_5 (queries.tests.Queries1Tests)", "test_ticket_10790_6 (queries.tests.Queries1Tests)", "test_ticket_10790_7 (queries.tests.Queries1Tests)", "test_ticket_10790_8 (queries.tests.Queries1Tests)", "test_ticket_10790_combine (queries.tests.Queries1Tests)", "test_ticket_20250 (queries.tests.Queries1Tests)", "test_tickets_1878_2939 (queries.tests.Queries1Tests)", "test_tickets_2076_7256 (queries.tests.Queries1Tests)", "test_tickets_2080_3592 (queries.tests.Queries1Tests)", "test_tickets_2874_3002 (queries.tests.Queries1Tests)", "test_tickets_4088_4306 (queries.tests.Queries1Tests)", "test_tickets_5321_7070 (queries.tests.Queries1Tests)", "test_tickets_5324_6704 (queries.tests.Queries1Tests)", "test_tickets_6180_6203 (queries.tests.Queries1Tests)", "test_tickets_7087_12242 (queries.tests.Queries1Tests)", "test_tickets_7204_7506 (queries.tests.Queries1Tests)", "test_tickets_7448_7707 (queries.tests.Queries1Tests)"], "environment_setup_commit": "65dfb06a1ab56c238cc80f5e1c31f61210c4577d"}]