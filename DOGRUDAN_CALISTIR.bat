@echo off
echo ========================================
echo DonatPlus Doğrudan <PERSON>ırma
echo ========================================
echo.

echo Bu script programı kurulum yapmadan doğrudan çalıştırır!
echo.

REM Proje dosyası kontrolü
if not exist "DonatPlus.UI\DonatPlus.UI.csproj" (
    echo HATA: DonatPlus.UI projesi bulunamadı!
    echo Bu script'i donatplus klasöründe çalıştırın.
    pause
    exit /b 1
)

echo 1. Hızlı derleme yapılıyor...
dotnet build DonatPlus.UI\DonatPlus.UI.csproj --configuration Release --verbosity quiet
if %errorLevel% neq 0 (
    echo HATA: Derleme başarısız!
    echo Detaylı derleme yapılıyor...
    dotnet build DonatPlus.UI\DonatPlus.UI.csproj --configuration Release
    pause
    exit /b 1
)
echo ✓ Derleme tamamlandı

echo.
echo 2. Program doğrudan başlatılıyor...

REM WPF uyumluluk ayarları
set DOTNET_EnableWriteXorExecute=0
set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
set DOTNET_ROLL_FORWARD=Major

REM Çalışma dizinini ayarla
cd DonatPlus.UI\bin\Release\net6.0-windows

echo Program başlatılıyor...
start "" "DonatPlus.UI.exe"

if %errorLevel% neq 0 (
    echo HATA: Program başlatılamadı!
    echo.
    echo .NET 6.0 Desktop Runtime yüklü olduğundan emin olun:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    pause
) else (
    echo ✓ Program başarıyla başlatıldı!
    echo.
    echo Program çalışıyor. Bu pencereyi kapatabilirsiniz.
)

pause
