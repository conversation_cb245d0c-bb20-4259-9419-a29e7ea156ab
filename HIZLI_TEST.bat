@echo off
echo ========================================
echo DonatPlus Hızlı Test Scripti
echo ========================================
echo.

echo 1. Proje derlen<PERSON>yor...
dotnet build DonatPlus.sln --configuration Release --verbosity quiet
if %errorLevel% neq 0 (
    echo HATA: <PERSON>je derlene<PERSON>!
    pause
    exit /b 1
)
echo ✓ Derleme başarılı

echo.
echo 2. Unit testler çalıştırılıyor...
dotnet test DonatPlus.Tests --configuration Release --no-build --verbosity quiet
if %errorLevel% neq 0 (
    echo HATA: Testler başarısız!
    pause
    exit /b 1
)
echo ✓ Tüm testler geçti

echo.
echo 3. Console test uygulaması çalıştırılıyor...
echo (Test sonuçlarını görmek için herhangi bir tuşa basın)
pause
cd DonatPlus.TestConsole
dotnet run --configuration Release
cd ..

echo.
echo 4. Ana program başlatılıyor...
echo (Program açılacak, test edebilirsiniz)
start "" "DonatPlus.UI\bin\Release\net6.0-windows\DonatPlus.UI.exe"

echo.
echo ========================================
echo TEST TAMAMLANDI!
echo ========================================
echo.
echo ✓ Derleme: Başarılı
echo ✓ Unit Testler: 60/60 geçti
echo ✓ Console Test: Çalıştırıldı
echo ✓ Ana Program: Başlatıldı
echo.
echo AutoCAD testi için:
echo 1. AutoCAD'i açın
echo 2. NETLOAD komutu ile DonatPlus.AutoCAD.dll'i yükleyin
echo 3. DONATPLUS komutunu çalıştırın
echo.
pause
