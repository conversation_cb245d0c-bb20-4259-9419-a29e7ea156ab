using System;
using System.Collections.Generic;
using DonatPlus.Core.Models;
using DonatPlus.Core.Services;
using Microsoft.Extensions.Logging;

namespace DonatPlus.TestConsole
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== DonatPlus Test Console ===");
            Console.WriteLine();

            // Logger setup
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var geometryLogger = loggerFactory.CreateLogger<GeometryCalculationService>();
            var detectionLogger = loggerFactory.CreateLogger<RebarDetectionService>();

            // Services
            var geometryService = new GeometryCalculationService(geometryLogger);
            var detectionService = new RebarDetectionService(detectionLogger, geometryService);

            // Test 1: Donatı Ağırlık Hesaplama
            Console.WriteLine("1. Donatı Ağırlık Hesaplama Testi:");
            var weight12mm = geometryService.CalculateRebarWeight(12, 5.0); // 12mm, 5m
            var weight16mm = geometryService.CalculateRebarWeight(16, 3.0); // 16mm, 3m
            Console.WriteLine($"   ø12mm, 5m: {weight12mm:F3} kg");
            Console.WriteLine($"   ø16mm, 3m: {weight16mm:F3} kg");
            Console.WriteLine();

            // Test 2: Metin Analizi
            Console.WriteLine("2. Metin Analizi Testi:");
            var testTexts = new[]
            {
                "5xø12 L=250",
                "ø16 L=350 3 adet",
                "Φ20 L=120.5"
            };

            foreach (var text in testTexts)
            {
                var rebar = detectionService.ExtractRebarFromText(text, 0, 0, "KIRIŞ");
                if (rebar != null)
                {
                    Console.WriteLine($"   '{text}' -> Çap: {rebar.Diameter}mm, Uzunluk: {rebar.Length}m, Adet: {rebar.Quantity}, Ağırlık: {rebar.Weight:F3}kg");
                }
                else
                {
                    Console.WriteLine($"   '{text}' -> Analiz edilemedi");
                }
            }
            Console.WriteLine();

            // Test 3: Büküm Uzunluğu Hesaplama
            Console.WriteLine("3. Büküm Uzunluğu Hesaplama Testi:");
            var bend45 = geometryService.CalculateBendLength(45, 50, 12);
            var bend90 = geometryService.CalculateBendLength(90, 50, 12);
            var bend180 = geometryService.CalculateBendLength(180, 50, 12);
            Console.WriteLine($"   45° büküm: {bend45:F4}m");
            Console.WriteLine($"   90° büküm: {bend90:F4}m");
            Console.WriteLine($"   180° büküm: {bend180:F4}m");
            Console.WriteLine();

            // Test 4: Bindirme Boyu Hesaplama
            Console.WriteLine("4. Bindirme Boyu Hesaplama Testi:");
            var lap12 = geometryService.CalculateLapLength(12, "C25", "S420");
            var lap16 = geometryService.CalculateLapLength(16, "C30", "S500");
            Console.WriteLine($"   ø12mm, C25, S420: {lap12:F3}m");
            Console.WriteLine($"   ø16mm, C30, S500: {lap16:F3}m");
            Console.WriteLine();

            // Test 5: Kesim Optimizasyonu
            Console.WriteLine("5. Kesim Optimizasyonu Testi:");
            var rebars = new List<RebarElement>
            {
                new() { Diameter = 12, Length = 3.0, Quantity = 5 },
                new() { Diameter = 12, Length = 4.0, Quantity = 3 },
                new() { Diameter = 16, Length = 5.0, Quantity = 2 }
            };

            var cuttingPlans = geometryService.OptimizeCutting(rebars, 12.0);
            foreach (var plan in cuttingPlans)
            {
                Console.WriteLine($"   ø{plan.Diameter}mm çap:");
                Console.WriteLine($"     Toplam {plan.Cuts.Count} çubuk gerekli");
                Console.WriteLine($"     Fire oranı: {plan.WastePercentage:F1}%");
                Console.WriteLine($"     Toplam fire: {plan.TotalWaste:F2}m");
                
                for (int i = 0; i < plan.Cuts.Count; i++)
                {
                    var cut = plan.Cuts[i];
                    Console.WriteLine($"     Çubuk {i+1}: {string.Join(" + ", cut.Pieces.Select(p => $"{p}m"))} = {cut.UsedLength}m (Fire: {cut.WasteLength}m)");
                }
                Console.WriteLine();
            }

            // Test 6: Donatı Listesi Oluşturma
            Console.WriteLine("6. Donatı Listesi Testi:");
            var rebarList = new List<RebarElement>
            {
                new()
                {
                    Diameter = 12,
                    Length = 5.0,
                    Quantity = 10,
                    ElementType = "Kiriş",
                    Description = "Ana kiriş donatısı",
                    Weight = geometryService.CalculateRebarWeight(12, 5.0) * 10
                },
                new()
                {
                    Diameter = 16,
                    Length = 3.0,
                    Quantity = 8,
                    ElementType = "Kolon",
                    Description = "Kolon ana donatısı",
                    Weight = geometryService.CalculateRebarWeight(16, 3.0) * 8
                }
            };

            Console.WriteLine($"   Toplam donatı elementi: {rebarList.Count}");
            Console.WriteLine($"   Toplam ağırlık: {rebarList.Sum(r => r.Weight):F2} kg");
            Console.WriteLine($"   Toplam uzunluk: {rebarList.Sum(r => r.Length * r.Quantity):F2} m");

            foreach (var rebar in rebarList)
            {
                Console.WriteLine($"   - {rebar.Description}: ø{rebar.Diameter}mm x {rebar.Length}m x {rebar.Quantity} adet = {rebar.Weight:F2}kg");
            }
            Console.WriteLine();

            Console.WriteLine("=== Test Tamamlandı ===");
            Console.WriteLine("Tüm core fonksiyonlar başarıyla çalışıyor!");
            Console.WriteLine();
            Console.WriteLine("Çıkmak için bir tuşa basın...");
            Console.ReadKey();
        }
    }
}
