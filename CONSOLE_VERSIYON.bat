@echo off
echo ========================================
echo DonatPlus Console Versiyonu
echo ========================================
echo.
echo WPF sorunları varsa Console versiyonunu kullanın!
echo.

if not exist "DonatPlus.TestConsole\DonatPlus.TestConsole.csproj" (
    echo HATA: Console projesi bulunamadı!
    pause
    exit /b 1
)

echo 1. Console versiyonu derleniyor...
dotnet build DonatPlus.TestConsole\DonatPlus.TestConsole.csproj --configuration Release

if %errorLevel% neq 0 (
    echo HATA: Console projesi derlenemedi!
    pause
    exit /b 1
)

echo ✓ Console versiyonu derlendi

echo.
echo 2. Console versiyonu çalıştırılıyor...
echo Bu versiyon WPF kullanmaz, kesi<PERSON><PERSON>le çalışır!
echo.

cd DonatPlus.TestConsole
dotnet run --configuration Release

echo.
echo Console versiyonu tamamlandı!
echo.
echo WPF versiyonu için WPF_HATA_COZUM.bat çalıştırın.
pause
