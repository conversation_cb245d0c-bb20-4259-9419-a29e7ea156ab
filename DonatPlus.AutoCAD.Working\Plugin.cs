using System; 
using System.Windows.Forms; 
using System.Reflection; 
using System.Runtime.InteropServices; 
 
[assembly: AssemblyTitle("DonatPlus AutoCAD Plugin")] 
[assembly: AssemblyDescription("Betonarme Donatı Metraj Hesaplama")] 
[assembly: AssemblyVersion("*******")] 
[assembly: ComVisible(false)] 
 
namespace DonatPlus.AutoCAD.Working 
{ 
    public class DonatPlusPlugin 
    { 
        public static void DONATPLUS() 
        { 
            try 
            { 
                MessageBox.Show("DonatPlus AutoCAD Plugin\\n\\nKomutlar:\\n- DPANALIZ: Donatı analizi\\n- DPTEST: Test hesaplama\\n- DPHELP: Yardım\\n\\nPlugin başarıyla çalışıyor!", "DonatPlus", MessageBoxButtons.OK, MessageBoxIcon.Information); 
            } 
            catch (Exception ex) 
            { 
                MessageBox.Show($"Hata: {ex.Message}", "DonatPlus Hata", MessageBoxButtons.OK, MessageBoxIcon.Error); 
            } 
        } 
 
        public static void DPANALIZ() 
        { 
            try 
            { 
                var sonuç = "Donatı Analiz Sonuçları:\\n\\n"; 
                sonuç += "Test Hesaplamaları:\\n"; 
                sonuç += $"5xø12 L=250 -> {CalculateWeight(12, 2.5, 5):F2}kg\\n"; 
                sonuç += $"3xø16 L=300 -> {CalculateWeight(16, 3.0, 3):F2}kg\\n"; 
                sonuç += $"ø20 L=400 -> {CalculateWeight(20, 4.0, 1):F2}kg\\n\\n"; 
                sonuç += "Gerçek projede metin seçimi yapılacak."; 
                MessageBox.Show(sonuç, "Donatı Analizi", MessageBoxButtons.OK, MessageBoxIcon.Information); 
            } 
            catch (Exception ex) 
            { 
                MessageBox.Show($"Analiz hatası: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error); 
            } 
        } 
 
        public static void DPTEST() 
        { 
            try 
            { 
                var test = "DonatPlus Test Sonuçları:\\n\\n"; 
                test += "Ağırlık Hesaplamaları:\\n"; 
                test += $"ø8mm 1m: {CalculateWeight(8, 1, 1):F3}kg\\n"; 
                test += $"ø10mm 1m: {CalculateWeight(10, 1, 1):F3}kg\\n"; 
                test += $"ø12mm 1m: {CalculateWeight(12, 1, 1):F3}kg\\n"; 
                test += $"ø14mm 1m: {CalculateWeight(14, 1, 1):F3}kg\\n"; 
                test += $"ø16mm 1m: {CalculateWeight(16, 1, 1):F3}kg\\n"; 
                test += $"ø20mm 1m: {CalculateWeight(20, 1, 1):F3}kg\\n"; 
                test += $"ø25mm 1m: {CalculateWeight(25, 1, 1):F3}kg\\n\\n"; 
                test += "Tüm hesaplamalar çalışıyor!"; 
                MessageBox.Show(test, "Test Sonuçları", MessageBoxButtons.OK, MessageBoxIcon.Information); 
            } 
            catch (Exception ex) 
            { 
                MessageBox.Show($"Test hatası: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error); 
            } 
        } 
 
        public static void DPHELP() 
        { 
            var help = "DonatPlus AutoCAD Plugin Yardım\\n\\n"; 
            help += "Komutlar:\\n"; 
            help += "DONATPLUS - Ana menü\\n"; 
            help += "DPANALIZ - Donatı analizi\\n"; 
            help += "DPTEST - Test hesaplama\\n"; 
            help += "DPHELP - Bu yardım\\n\\n"; 
            help += "Kullanım:\\n"; 
            help += "1. Donatı metinleri ekleyin\\n"; 
            help += "2. DPANALIZ komutunu çalıştırın\\n"; 
            help += "3. Metinleri seçin\\n"; 
            help += "4. Sonuçları görüntüleyin"; 
            MessageBox.Show(help, "DonatPlus Yardım", MessageBoxButtons.OK, MessageBoxIcon.Information); 
        } 
 
        private static double CalculateWeight(double diameter, double length, int quantity) 
        { 
            if (diameter <= 0 || length <= 0) return 0; 
            var area = Math.PI * Math.Pow(diameter / 2000.0, 2); 
            return area * length * 7850 * quantity; 
        } 
    } 
} 
