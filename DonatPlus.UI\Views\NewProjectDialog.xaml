<Window x:Class="DonatPlus.UI.Views.NewProjectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Yeni Proje Oluştur" Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Başlık -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Yeni Proje <PERSON>" Style="{StaticResource TitleTextStyle}"/>
            <TextBlock Text="Proje bilgilerini doldurun" Style="{StaticResource InfoTextStyle}"/>
        </StackPanel>

        <!-- Form -->
        <ScrollViewer Grid.Row="1">
            <StackPanel>
                <TextBox x:Name="TxtProjectName" 
                         Style="{StaticResource FormTextBoxStyle}"
                         materialDesign:HintAssist.Hint="Proje Adı *"
                         MaxLength="200"/>

                <TextBox x:Name="TxtProjectNumber" 
                         Style="{StaticResource FormTextBoxStyle}"
                         materialDesign:HintAssist.Hint="Proje Numarası"
                         MaxLength="100"/>

                <TextBox x:Name="TxtClientName" 
                         Style="{StaticResource FormTextBoxStyle}"
                         materialDesign:HintAssist.Hint="Müşteri Adı"
                         MaxLength="200"/>

                <TextBox x:Name="TxtLocation" 
                         Style="{StaticResource FormTextBoxStyle}"
                         materialDesign:HintAssist.Hint="Proje Konumu"
                         MaxLength="300"/>

                <TextBox x:Name="TxtEngineer" 
                         Style="{StaticResource FormTextBoxStyle}"
                         materialDesign:HintAssist.Hint="Sorumlu Mühendis"
                         MaxLength="100"/>

                <ComboBox x:Name="CmbConcreteClass" 
                          Style="{StaticResource FormComboBoxStyle}"
                          materialDesign:HintAssist.Hint="Beton Sınıfı"
                          SelectedIndex="2">
                    <ComboBoxItem Content="C20"/>
                    <ComboBoxItem Content="C25"/>
                    <ComboBoxItem Content="C30"/>
                    <ComboBoxItem Content="C35"/>
                    <ComboBoxItem Content="C40"/>
                </ComboBox>

                <ComboBox x:Name="CmbSteelGrade" 
                          Style="{StaticResource FormComboBoxStyle}"
                          materialDesign:HintAssist.Hint="Çelik Sınıfı"
                          SelectedIndex="1">
                    <ComboBoxItem Content="S220"/>
                    <ComboBoxItem Content="S420"/>
                    <ComboBoxItem Content="S500"/>
                </ComboBox>

                <TextBox x:Name="TxtDescription" 
                         Style="{StaticResource FormTextBoxStyle}"
                         materialDesign:HintAssist.Hint="Açıklama"
                         MaxLength="500"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         Height="80"/>

                <CheckBox x:Name="ChkIsActive" 
                          Content="Aktif proje" 
                          IsChecked="True" 
                          Margin="0,10"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Butonlar -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="BtnCancel" 
                    Style="{StaticResource SecondaryButtonStyle}"
                    Content="İptal" 
                    Click="BtnCancel_Click"/>
            <Button x:Name="BtnCreate" 
                    Style="{StaticResource ActionButtonStyle}"
                    Content="Oluştur" 
                    Click="BtnCreate_Click"/>
        </StackPanel>
    </Grid>
</Window>
