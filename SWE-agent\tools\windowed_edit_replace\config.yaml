tools:
  edit:
    signature: |
      edit <search> <replace> [<replace-all>]
    docstring: >
      Replace first occurrence of <search> with <replace> in the currently displayed lines.
      If replace-all is True , replace all occurrences of <search> with <replace>.

      For example, if you are looking at this file:

      def fct():
          print("Hello world")

      and you want to edit the file to read:

      def fct():
          print("Hello")
          print("world")

      you can search for `Hello world` and replace with `"Hello"\n    print("world")`
      (note the extra spaces before the print statement!).

      Tips:

      1. Always include proper whitespace/indentation
      2. When you are adding an if/with/try statement, you need to INDENT the block that follows, so make sure to include it in both your search and replace strings!
      3. If you are wrapping code in a try statement, make sure to also add an 'except' or 'finally' block.

      Before every edit, please

      1. Explain the code you want to edit and why it is causing the problem
      2. Explain the edit you want to make and how it fixes the problem
      3. Explain how the edit does not break existing functionality
    arguments:
      - name: search
        type: string
        description: "the text to search for (make sure to include proper whitespace if needed)"
        required: true
      - name: replace
        type: string
        description: "the text to replace the search with (make sure to include proper whitespace if needed)"
        required: true
      - name: replace-all
        type: boolean
        description: "replace all occurrences rather than the first occurrence within the displayed lines"
        required: false
  insert:
    signature: |
      insert <text> [<line>]
    docstring: >
      Insert <text> at the end of the currently opened file or after <line> if specified.
    arguments:
      - name: text
        type: string
        description: "the text to insert"
        required: true
      - name: line
        type: integer
        description: "the line number to insert the text as new lines after"
        required: false
