[{"repo": "django/django", "instance_id": "django__django-16255", "base_commit": "444b6da7cc229a58a2c476a52e45233001dc7073", "patch": "diff --git a/django/contrib/sitemaps/__init__.py b/django/contrib/sitemaps/__init__.py\n--- a/django/contrib/sitemaps/__init__.py\n+++ b/django/contrib/sitemaps/__init__.py\n@@ -167,7 +167,7 @@ def get_latest_lastmod(self):\n             return None\n         if callable(self.lastmod):\n             try:\n-                return max([self.lastmod(item) for item in self.items()])\n+                return max([self.lastmod(item) for item in self.items()], default=None)\n             except TypeError:\n                 return None\n         else:\n", "test_patch": "diff --git a/tests/sitemaps_tests/test_http.py b/tests/sitemaps_tests/test_http.py\n--- a/tests/sitemaps_tests/test_http.py\n+++ b/tests/sitemaps_tests/test_http.py\n@@ -507,6 +507,16 @@ def test_callable_sitemod_full(self):\n         self.assertXMLEqual(index_response.content.decode(), expected_content_index)\n         self.assertXMLEqual(sitemap_response.content.decode(), expected_content_sitemap)\n \n+    def test_callable_sitemod_no_items(self):\n+        index_response = self.client.get(\"/callable-lastmod-no-items/index.xml\")\n+        self.assertNotIn(\"Last-Modified\", index_response)\n+        expected_content_index = \"\"\"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n+        <sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n+        <sitemap><loc>http://example.com/simple/sitemap-callable-lastmod.xml</loc></sitemap>\n+        </sitemapindex>\n+        \"\"\"\n+        self.assertXMLEqual(index_response.content.decode(), expected_content_index)\n+\n \n # RemovedInDjango50Warning\n class DeprecatedTests(SitemapTestsBase):\ndiff --git a/tests/sitemaps_tests/urls/http.py b/tests/sitemaps_tests/urls/http.py\n--- a/tests/sitemaps_tests/urls/http.py\n+++ b/tests/sitemaps_tests/urls/http.py\n@@ -114,6 +114,16 @@ def lastmod(self, obj):\n         return obj.lastmod\n \n \n+class CallableLastmodNoItemsSitemap(Sitemap):\n+    location = \"/location/\"\n+\n+    def items(self):\n+        return []\n+\n+    def lastmod(self, obj):\n+        return obj.lastmod\n+\n+\n class GetLatestLastmodNoneSiteMap(Sitemap):\n     changefreq = \"never\"\n     priority = 0.5\n@@ -233,6 +243,10 @@ def testmodelview(request, id):\n     \"callable-lastmod\": CallableLastmodFullSitemap,\n }\n \n+callable_lastmod_no_items_sitemap = {\n+    \"callable-lastmod\": CallableLastmodNoItemsSitemap,\n+}\n+\n urlpatterns = [\n     path(\"simple/index.xml\", views.index, {\"sitemaps\": simple_sitemaps}),\n     path(\"simple-paged/index.xml\", views.index, {\"sitemaps\": simple_sitemaps_paged}),\n@@ -417,6 +431,11 @@ def testmodelview(request, id):\n         views.sitemap,\n         {\"sitemaps\": callable_lastmod_full_sitemap},\n     ),\n+    path(\n+        \"callable-lastmod-no-items/index.xml\",\n+        views.index,\n+        {\"sitemaps\": callable_lastmod_no_items_sitemap},\n+    ),\n     path(\n         \"generic-lastmod/index.xml\",\n         views.index,\n", "problem_statement": "Sitemaps without items raise ValueError on callable lastmod.\nDescription\n\t\nWhen sitemap contains not items, but supports returning lastmod for an item, it fails with a ValueError:\nTraceback (most recent call last):\n File \"/usr/local/lib/python3.10/site-packages/django/core/handlers/exception.py\", line 55, in inner\n\tresponse = get_response(request)\n File \"/usr/local/lib/python3.10/site-packages/django/core/handlers/base.py\", line 197, in _get_response\n\tresponse = wrapped_callback(request, *callback_args, **callback_kwargs)\n File \"/usr/local/lib/python3.10/site-packages/django/utils/decorators.py\", line 133, in _wrapped_view\n\tresponse = view_func(request, *args, **kwargs)\n File \"/usr/local/lib/python3.10/site-packages/django/contrib/sitemaps/views.py\", line 34, in inner\n\tresponse = func(request, *args, **kwargs)\n File \"/usr/local/lib/python3.10/site-packages/django/contrib/sitemaps/views.py\", line 76, in index\n\tsite_lastmod = site.get_latest_lastmod()\n File \"/usr/local/lib/python3.10/site-packages/django/contrib/sitemaps/__init__.py\", line 170, in get_latest_lastmod\n\treturn max([self.lastmod(item) for item in self.items()])\nException Type: ValueError at /sitemap.xml\nException Value: max() arg is an empty sequence\nSomething like this might be a solution:\n\t def get_latest_lastmod(self):\n\t\t if not hasattr(self, \"lastmod\"):\n\t\t\t return None\n\t\t if callable(self.lastmod):\n\t\t\t try:\n\t\t\t\t return max([self.lastmod(item) for item in self.items()])\n-\t\t\texcept TypeError:\n+\t\t\texcept (TypeError, ValueError):\n\t\t\t\t return None\n\t\t else:\n\t\t\t return self.lastmod\n", "hints_text": "Thanks for the report.\nThe default argument of max() can be used.", "created_at": "2022-11-04T13:49:40Z", "version": "4.2", "FAIL_TO_PASS": ["test_callable_sitemod_no_items (sitemaps_tests.test_http.HTTPSitemapTests)"], "PASS_TO_PASS": ["A cached sitemap index can be rendered (#2713).", "A i18n sitemap index with limited languages can be rendered.", "A i18n sitemap index with x-default can be rendered.", "A i18n sitemap with alternate/hreflang links can be rendered.", "A simple i18n sitemap index can be rendered, without logging variable", "A simple sitemap can be rendered", "A simple sitemap can be rendered with a custom template", "A simple sitemap index can be rendered", "A simple sitemap index can be rendered with a custom template", "A simple sitemap section can be rendered", "A sitemap may have multiple pages.", "A sitemap may not be callable.", "All items in the sitemap have `lastmod`. The `Last-Modified` header", "Check to make sure that the raw item is included with each", "Check we get ImproperlyConfigured if we don't pass a site object to", "Check we get ImproperlyConfigured when we don't pass a site object to", "Last-Modified header is missing when sitemap has no lastmod", "Last-Modified header is omitted when lastmod not on all items", "Last-Modified header is set correctly", "Not all items have `lastmod`. Therefore the `Last-Modified` header", "The Last-Modified header is omitted when lastmod isn't found in all", "The Last-Modified header is set to the most recent sitemap lastmod.", "The Last-Modified header should be converted from timezone aware dates", "The Last-Modified header should be support dates (without time).", "The priority value should not be localized.", "lastmod datestamp shows timezones if Sitemap.get_latest_lastmod", "sitemapindex.lastmod is included when Sitemap.lastmod is", "sitemapindex.lastmod is omitted when Sitemap.lastmod is", "test_empty_page (sitemaps_tests.test_http.HTTPSitemapTests)", "test_empty_sitemap (sitemaps_tests.test_http.HTTPSitemapTests)", "test_no_section (sitemaps_tests.test_http.HTTPSitemapTests)", "test_page_not_int (sitemaps_tests.test_http.HTTPSitemapTests)", "test_requestsite_sitemap (sitemaps_tests.test_http.HTTPSitemapTests)", "test_simple_sitemap_custom_index_warning (sitemaps_tests.test_http.DeprecatedTests)", "test_sitemap_without_entries (sitemaps_tests.test_http.HTTPSitemapTests)", "test_x_robots_sitemap (sitemaps_tests.test_http.HTTPSitemapTests)"], "environment_setup_commit": "0fbdb9784da915fce5dcc1fe82bac9b4785749e5"}, {"repo": "sympy/sympy", "instance_id": "sympy__sympy-13031", "base_commit": "2dfa7457f20ee187fbb09b5b6a1631da4458388c", "patch": "diff --git a/sympy/matrices/sparse.py b/sympy/matrices/sparse.py\n--- a/sympy/matrices/sparse.py\n+++ b/sympy/matrices/sparse.py\n@@ -985,8 +985,10 @@ def col_join(self, other):\n         >>> C == A.row_insert(A.rows, Matrix(B))\n         True\n         \"\"\"\n-        if not self:\n-            return type(self)(other)\n+        # A null matrix can always be stacked (see  #10770)\n+        if self.rows == 0 and self.cols != other.cols:\n+            return self._new(0, other.cols, []).col_join(other)\n+\n         A, B = self, other\n         if not A.cols == B.cols:\n             raise ShapeError()\n@@ -1191,8 +1193,10 @@ def row_join(self, other):\n         >>> C == A.col_insert(A.cols, B)\n         True\n         \"\"\"\n-        if not self:\n-            return type(self)(other)\n+        # A null matrix can always be stacked (see  #10770)\n+        if self.cols == 0 and self.rows != other.rows:\n+            return self._new(other.rows, 0, []).row_join(other)\n+\n         A, B = self, other\n         if not A.rows == B.rows:\n             raise ShapeError()\n", "test_patch": "diff --git a/sympy/matrices/tests/test_sparse.py b/sympy/matrices/tests/test_sparse.py\n--- a/sympy/matrices/tests/test_sparse.py\n+++ b/sympy/matrices/tests/test_sparse.py\n@@ -26,6 +26,12 @@ def sparse_zeros(n):\n     assert type(a.row_join(b)) == type(a)\n     assert type(a.col_join(b)) == type(a)\n \n+    # make sure 0 x n matrices get stacked correctly\n+    sparse_matrices = [SparseMatrix.zeros(0, n) for n in range(4)]\n+    assert SparseMatrix.hstack(*sparse_matrices) == Matrix(0, 6, [])\n+    sparse_matrices = [SparseMatrix.zeros(n, 0) for n in range(4)]\n+    assert SparseMatrix.vstack(*sparse_matrices) == Matrix(6, 0, [])\n+\n     # test element assignment\n     a = SparseMatrix((\n         (1, 0),\n", "problem_statement": "Behavior of <PERSON> hstack and vstack changed in sympy 1.1\nIn sympy 1.0:\r\n```\r\nimport sympy as sy\r\nM1 = sy.Matrix.zeros(0, 0)\r\nM2 = sy.Matrix.zeros(0, 1)\r\nM3 = sy.Matrix.zeros(0, 2)\r\nM4 = sy.Matrix.zeros(0, 3)\r\nsy.Matrix.hstack(M1, M2, M3, M4).shape\r\n```\r\nreturns \r\n`(0, 6)`\r\n\r\nNow, same in sympy 1.1:\r\n```\r\nimport sympy as sy\r\nM1 = sy.Matrix.zeros(0, 0)\r\nM2 = sy.Matrix.zeros(0, 1)\r\nM3 = sy.Matrix.zeros(0, 2)\r\nM4 = sy.Matrix.zeros(0, 3)\r\nsy.Matrix.hstack(M1, M2, M3, M4).shape\r\n```\r\nreturns\r\n`(0, 3)\r\n`\r\nwhereas:\r\n```\r\nimport sympy as sy\r\nM1 = sy.Matrix.zeros(1, 0)\r\nM2 = sy.Matrix.zeros(1, 1)\r\nM3 = sy.Matrix.zeros(1, 2)\r\nM4 = sy.Matrix.zeros(1, 3)\r\nsy.Matrix.hstack(M1, M2, M3, M4).shape\r\n```\r\nreturns\r\n`(1, 6)\r\n`\n", "hints_text": "CC @siefkenj \nI update my comment in case someone already read it. We still have an issue with matrices shape in [pyphs](https://github.com/pyphs/pyphs/issues/49#issuecomment-316618994), but hstack and vstack seem ok in sympy 1.1.1rc1:\r\n\r\n```\r\n>>> import sympy as sy\r\n>>> sy.__version__\r\n'1.1.1rc1'\r\n>>> '1.1.1rc1'\r\n'1.1.1rc1'\r\n>>> matrices = [sy.Matrix.zeros(0, n) for n in range(4)]\r\n>>> sy.Matrix.hstack(*matrices).shape\r\n(0, 6)\r\n>>> matrices = [sy.Matrix.zeros(1, n) for n in range(4)]\r\n>>> sy.Matrix.hstack(*matrices).shape\r\n(1, 6)\r\n>>> matrices = [sy.Matrix.zeros(n, 0) for n in range(4)]\r\n>>> sy.Matrix.vstack(*matrices).shape\r\n(6, 0)\r\n>>> matrices = [sy.Matrix.zeros(1, n) for n in range(4)]\r\n>>> sy.Matrix.hstack(*matrices).shape\r\n(1, 6)\r\n>>> \r\n```\nThe problem is solved with Matrix but not SparseMatrix:\r\n```\r\n>>> import sympy as sy\r\n>>> sy.__version__\r\n'1.1.1rc1'\r\n>>> matrices = [Matrix.zeros(0, n) for n in range(4)]\r\n>>> Matrix.hstack(*matrices)\r\nMatrix(0, 6, [])\r\n>>> sparse_matrices = [SparseMatrix.zeros(0, n) for n in range(4)]\r\n>>> SparseMatrix.hstack(*sparse_matrices)\r\nMatrix(0, 3, [])\r\n>>> \r\n```\nBisected to 27e9ee425819fa09a4cbb8179fb38939cc693249. Should we revert that commit? CC @aravindkanna\nAny thoughts? This is the last fix to potentially go in the 1.1.1 release, but I want to cut a release candidate today or tomorrow, so speak now, or hold your peace (until the next major release).\nI am away at a conference. The change should be almost identical to the fix for dense matrices, if someone can manage to get a patch in. I *might* be able to do it tomorrow.\nOkay.  I've looked this over and its convoluted...\r\n\r\n`SparseMatrix` should impliment `_eval_col_join`.  `col_join` should not be implemented.  It is, and that is what `hstack` is calling, which is why my previous patch didn't fix `SparseMatrix`s as well.  However, the patch that @asmeurer referenced ensures that `SparseMatrix.row_join(DenseMatrix)` returns a `SparseMatrix` whereas `CommonMatrix.row_join(SparseMatrix, DenseMatrix)` returns a `classof(SparseMatrix, DenseMatrix)` which happens to be a `DenseMatrix`.  I don't think that these should behave differently.  This API needs to be better thought out.\nSo is there a simple fix that can be made for the release or should this be postponed?", "created_at": "2017-07-23T15:48:13Z", "version": "1.1", "FAIL_TO_PASS": ["test_sparse_matrix"], "PASS_TO_PASS": ["test_CL_RL", "test_add", "test_copyin", "test_errors", "test_len", "test_sparse_solve", "test_sparse_zeros_sparse_eye", "test_trace", "test_transpose"], "environment_setup_commit": "ec9e3c0436fbff934fa84e22bf07f1b3ef5bfac3"}, {"repo": "pytest-dev/pytest", "instance_id": "pytest-dev__pytest-11143", "base_commit": "6995257cf470d2143ad1683824962de4071c0eb7", "patch": "diff --git a/src/_pytest/assertion/rewrite.py b/src/_pytest/assertion/rewrite.py\n--- a/src/_pytest/assertion/rewrite.py\n+++ b/src/_pytest/assertion/rewrite.py\n@@ -676,6 +676,7 @@ def run(self, mod: ast.Module) -> None:\n                 expect_docstring\n                 and isinstance(item, ast.Expr)\n                 and isinstance(item.value, ast.Constant)\n+                and isinstance(item.value.value, str)\n             ):\n                 doc = item.value.value\n                 if self.is_rewrite_disabled(doc):\n", "test_patch": "diff --git a/testing/test_assertrewrite.py b/testing/test_assertrewrite.py\n--- a/testing/test_assertrewrite.py\n+++ b/testing/test_assertrewrite.py\n@@ -2042,3 +2042,17 @@ def test_max_increased_verbosity(self, pytester: Pytester) -> None:\n         self.create_test_file(pytester, DEFAULT_REPR_MAX_SIZE * 10)\n         result = pytester.runpytest(\"-vv\")\n         result.stdout.no_fnmatch_line(\"*xxx...xxx*\")\n+\n+\n+class TestIssue11140:\n+    def test_constant_not_picked_as_module_docstring(self, pytester: Pytester) -> None:\n+        pytester.makepyfile(\n+            \"\"\"\\\n+            0\n+\n+            def test_foo():\n+                pass\n+            \"\"\"\n+        )\n+        result = pytester.runpytest()\n+        assert result.ret == 0\n", "problem_statement": "Rewrite fails when first expression of file is a number and mistaken as docstring \n<!--\r\nThanks for submitting an issue!\r\n\r\nQuick check-list while reporting bugs:\r\n-->\r\n\r\n- [x] a detailed description of the bug or problem you are having\r\n- [x] output of `pip list` from the virtual environment you are using\r\n- [x] pytest and operating system versions\r\n- [x] minimal example if possible\r\n```\r\nInstalling collected packages: zipp, six, PyYAML, python-dateutil, MarkupSafe, importlib-metadata, watchdog, tomli, soupsieve, pyyaml-env-tag, pycparser, pluggy, packaging, mergedeep, Markdown, jinja2, iniconfig, ghp-import, exceptiongroup, click, websockets, urllib3, tqdm, smmap, pytest, pyee, mkdocs, lxml, importlib-resources, idna, cssselect, charset-normalizer, cffi, certifi, beautifulsoup4, attrs, appdirs, w3lib, typing-extensions, texttable, requests, pyzstd, pytest-metadata, pyquery, pyppmd, pyppeteer, pynacl, pymdown-extensions, pycryptodomex, pybcj, pyasn1, py, psutil, parse, multivolumefile, mkdocs-autorefs, inflate64, gitdb, fake-useragent, cryptography, comtypes, bs4, brotli, bcrypt, allure-python-commons, xlwt, xlrd, rsa, requests-html, pywinauto, python-i18n, python-dotenv, pytest-rerunfailures, pytest-html, pytest-check, PySocks, py7zr, paramiko, mkdocstrings, loguru, GitPython, ftputil, crcmod, chardet, brotlicffi, allure-pytest\r\nSuccessfully installed GitPython-3.1.31 Markdown-3.3.7 MarkupSafe-2.1.3 PySocks-1.7.1 PyYAML-6.0 allure-pytest-2.13.2 allure-python-commons-2.13.2 appdirs-1.4.4 attrs-23.1.0 bcrypt-4.0.1 beautifulsoup4-4.12.2 brotli-1.0.9 brotlicffi-1.0.9.2 bs4-0.0.1 certifi-2023.5.7 cffi-1.15.1 chardet-5.1.0 charset-normalizer-3.1.0 click-8.1.3 comtypes-1.2.0 crcmod-1.7 cryptography-41.0.1 cssselect-1.2.0 exceptiongroup-1.1.1 fake-useragent-1.1.3 ftputil-5.0.4 ghp-import-2.1.0 gitdb-4.0.10 idna-3.4 importlib-metadata-6.7.0 importlib-resources-5.12.0 inflate64-0.3.1 iniconfig-2.0.0 jinja2-3.1.2 loguru-0.7.0 lxml-4.9.2 mergedeep-1.3.4 mkdocs-1.4.3 mkdocs-autorefs-0.4.1 mkdocstrings-0.22.0 multivolumefile-0.2.3 packaging-23.1 paramiko-3.2.0 parse-1.19.1 pluggy-1.2.0 psutil-5.9.5 py-1.11.0 py7zr-0.20.5 pyasn1-0.5.0 pybcj-1.0.1 pycparser-2.21 pycryptodomex-3.18.0 pyee-8.2.2 pymdown-extensions-10.0.1 pynacl-1.5.0 pyppeteer-1.0.2 pyppmd-1.0.0 pyquery-2.0.0 pytest-7.4.0 pytest-check-2.1.5 pytest-html-3.2.0 pytest-metadata-3.0.0 pytest-rerunfailures-11.1.2 python-dateutil-2.8.2 python-dotenv-1.0.0 python-i18n-0.3.9 pywinauto-0.6.6 pyyaml-env-tag-0.1 pyzstd-0.15.9 requests-2.31.0 requests-html-0.10.0 rsa-4.9 six-1.16.0 smmap-5.0.0 soupsieve-2.4.1 texttable-1.6.7 tomli-2.0.1 tqdm-4.65.0 typing-extensions-4.6.3 urllib3-1.26.16 w3lib-2.1.1 watchdog-3.0.0 websockets-10.4 xlrd-2.0.1 xlwt-1.3.0 zipp-3.15.0\r\n```\r\nuse `pytest -k xxx`， report an error：`TypeError: argument of type 'int' is not iterable`\r\n\r\nit seems a error in collecting testcase\r\n```\r\n==================================== ERRORS ====================================\r\n_ ERROR collecting testcases/基线/代理策略/SOCKS二级代理迭代二/在线用户/在线用户更新/上线用户/test_socks_user_011.py _\r\n/usr/local/lib/python3.8/site-packages/_pytest/runner.py:341: in from_call\r\n    result: Optional[TResult] = func()\r\n/usr/local/lib/python3.8/site-packages/_pytest/runner.py:372: in <lambda>\r\n    call = CallInfo.from_call(lambda: list(collector.collect()), \"collect\")\r\n/usr/local/lib/python3.8/site-packages/_pytest/python.py:531: in collect\r\n    self._inject_setup_module_fixture()\r\n/usr/local/lib/python3.8/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture\r\n    self.obj, (\"setUpModule\", \"setup_module\")\r\n/usr/local/lib/python3.8/site-packages/_pytest/python.py:310: in obj\r\n    self._obj = obj = self._getobj()\r\n/usr/local/lib/python3.8/site-packages/_pytest/python.py:528: in _getobj\r\n    return self._importtestmodule()\r\n/usr/local/lib/python3.8/site-packages/_pytest/python.py:617: in _importtestmodule\r\n    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)\r\n/usr/local/lib/python3.8/site-packages/_pytest/pathlib.py:565: in import_path\r\n    importlib.import_module(module_name)\r\n/usr/local/lib/python3.8/importlib/__init__.py:127: in import_module\r\n    return _bootstrap._gcd_import(name[level:], package, level)\r\n<frozen importlib._bootstrap>:1014: in _gcd_import\r\n    ???\r\n<frozen importlib._bootstrap>:991: in _find_and_load\r\n    ???\r\n<frozen importlib._bootstrap>:975: in _find_and_load_unlocked\r\n    ???\r\n<frozen importlib._bootstrap>:671: in _load_unlocked\r\n    ???\r\n/usr/local/lib/python3.8/site-packages/_pytest/assertion/rewrite.py:169: in exec_module\r\n    source_stat, co = _rewrite_test(fn, self.config)\r\n/usr/local/lib/python3.8/site-packages/_pytest/assertion/rewrite.py:352: in _rewrite_test\r\n    rewrite_asserts(tree, source, strfn, config)\r\n/usr/local/lib/python3.8/site-packages/_pytest/assertion/rewrite.py:413: in rewrite_asserts\r\n    AssertionRewriter(module_path, config, source).run(mod)\r\n/usr/local/lib/python3.8/site-packages/_pytest/assertion/rewrite.py:695: in run\r\n    if self.is_rewrite_disabled(doc):\r\n/usr/local/lib/python3.8/site-packages/_pytest/assertion/rewrite.py:760: in is_rewrite_disabled\r\n    return \"PYTEST_DONT_REWRITE\" in docstring\r\nE   TypeError: argument of type 'int' is not iterable\r\n```\n", "hints_text": "more details are needed - based on the exception, the docstring is a integer, that seems completely wrong\nI run it pass lasttime in 2023-6-20 17:07:23. it run in docker and install newest pytest before run testcase everytime . maybe some commit cause it recently. \r\nI run it can pass in 7.2.0 a few minutes ago.\r\n\r\n`pytest ini`\r\n```\r\n[pytest]\r\nlog_cli = false\r\nlog_cli_level = debug\r\nlog_cli_format = %(asctime)s %(levelname)s %(message)s\r\nlog_cli_date_format = %Y-%m-%d %H:%M:%S\r\n\r\naddopts = -v -s\r\n\r\nfilterwarnings =\r\n    ignore::UserWarning\r\n\r\nmarkers=\r\n    case_id: mark test id to upload on tp\r\n    case_level_bvt: testcase level bvt\r\n    case_level_1: testcase level level 1\r\n    case_level_2: testcase level level 2\r\n    case_level_3: testcase level level 3\r\n    case_status_pass: mark case as PASS\r\n    case_status_fail: mark case as FAILED\r\n    case_status_not_finish: mark case as CODEING\r\n    case_status_not_run: mark case as FINISH\r\n    case_not_run: mark case as DONT RUN\r\n    run_env: mark run this case on which environment\r\n ```\r\n    \r\n`testcase:`\r\n```\r\****************(autouse=True)\r\ndef default_setup_teardown():\r\n    xxxx\r\n\r\****************(\"初始状态\")\r\nclass TestDefauleName:\r\n    @allure.title(\"上线一个域用户，用户名和组名正确\")\r\n    @pytest.mark.case_level_1\r\n    @pytest.mark.case_id(\"tc_proxyheard_insert_011\")\r\n    def test_tc_proxyheard_insert_011(self):\r\n        xxxx\r\n        ```\nthanks for the update\r\n\r\ni took the liberty to edit your comments to use markdown code blocks for ease of reading\r\n\r\nfrom the given information the problem is still unclear\r\n\r\nplease try running with `--assert=plain` for verification\r\n\r\nthe error indicates that the python ast parser somehow ends up with a integer as the docstring for `test_socks_user_011.py` the reason is still unclear based on the redacted information\nI run with --assert=plain and it has passed\r\n\r\npython3 -m pytest -k helloworld --assert=plain\r\n```\r\ntestcases/smoke_testcase/test_helloworld.py::TestGuardProcess::test_hello_world 2023-06-25 08:54:17.659 | INFO     | NAC_AIO.testcases.smoke_testcase.test_helloworld:test_hello_world:15 - Great! Frame Work is working\r\nPASSED\r\ntotal: 1648\r\npassed: 1\r\nfailed: 0\r\nerror: 0\r\npass_rate 100.00%\r\n\r\n================================================================================= 1 passed, 1647 deselected in 12.28s =================================================================================\r\n```\nIt seems to me that we have a potential bug in the ast transformer where's in case the first expression of a file is a integer, we mistake it as a docstring\n\nCan you verify the first expression in the file that fails?\nyou are right this file first expression is a 0 . It can pass after I delete it \r\nthank you!\nMinimal reproducer:\r\n\r\n```python\r\n0\r\n```\r\n\r\n(yes, just that, in a .py file)", "created_at": "2023-06-26T06:44:43Z", "version": "8.0", "FAIL_TO_PASS": ["testing/test_assertrewrite.py::TestIssue11140::test_constant_not_picked_as_module_docstring"], "PASS_TO_PASS": ["testing/test_assertrewrite.py::TestAssertionPass::test_hook_call", "testing/test_assertrewrite.py::TestAssertionPass::test_hook_call_with_parens", "testing/test_assertrewrite.py::TestAssertionPass::test_hook_not_called_without_cmd_option", "testing/test_assertrewrite.py::TestAssertionPass::test_hook_not_called_without_hookimpl", "testing/test_assertrewrite.py::TestAssertionPass::test_option_default", "testing/test_assertrewrite.py::TestAssertionRewrite::test_assert_already_has_message", "testing/test_assertrewrite.py::TestAssertionRewrite::test_assert_raising__bool__in_comparison", "testing/test_assertrewrite.py::TestAssertionRewrite::test_assertion_message", "testing/test_assertrewrite.py::TestAssertionRewrite::test_assertion_message_escape", "testing/test_assertrewrite.py::TestAssertionRewrite::test_assertion_message_expr", "testing/test_assertrewrite.py::TestAssertionRewrite::test_assertion_message_multiline", "testing/test_assertrewrite.py::TestAssertionRewrite::test_assertion_message_tuple", "testing/test_assertrewrite.py::TestAssertionRewrite::test_assertion_messages_bytes", "testing/test_assertrewrite.py::TestAssertionRewrite::test_assertrepr_compare_same_width", "testing/test_assertrewrite.py::TestAssertionRewrite::test_at_operator_issue1290", "testing/test_assertrewrite.py::TestAssertionRewrite::test_attribute", "testing/test_assertrewrite.py::TestAssertionRewrite::test_binary_op", "testing/test_assertrewrite.py::TestAssertionRewrite::test_boolop", "testing/test_assertrewrite.py::TestAssertionRewrite::test_boolop_percent", "testing/test_assertrewrite.py::TestAssertionRewrite::test_call", "testing/test_assertrewrite.py::TestAssertionRewrite::test_comparisons", "testing/test_assertrewrite.py::TestAssertionRewrite::test_custom_repr", "testing/test_assertrewrite.py::TestAssertionRewrite::test_custom_repr_non_ascii", "testing/test_assertrewrite.py::TestAssertionRewrite::test_custom_reprcompare", "testing/test_assertrewrite.py::TestAssertionRewrite::test_dont_rewrite", "testing/test_assertrewrite.py::TestAssertionRewrite::test_dont_rewrite_if_hasattr_fails", "testing/test_assertrewrite.py::TestAssertionRewrite::test_dont_rewrite_plugin", "testing/test_assertrewrite.py::TestAssertionRewrite::test_formatchar", "testing/test_assertrewrite.py::TestAssertionRewrite::test_honors_pep_235", "testing/test_assertrewrite.py::TestAssertionRewrite::test_len", "testing/test_assertrewrite.py::TestAssertionRewrite::test_location_is_set", "testing/test_assertrewrite.py::TestAssertionRewrite::test_name", "testing/test_assertrewrite.py::TestAssertionRewrite::test_place_initial_imports", "testing/test_assertrewrite.py::TestAssertionRewrite::test_rewrites_plugin_as_a_package", "testing/test_assertrewrite.py::TestAssertionRewrite::test_short_circuit_evaluation", "testing/test_assertrewrite.py::TestAssertionRewrite::test_starred_with_side_effect", "testing/test_assertrewrite.py::TestAssertionRewrite::test_unary_op", "testing/test_assertrewrite.py::TestAssertionRewriteHookDetails::test_get_data_support", "testing/test_assertrewrite.py::TestAssertionRewriteHookDetails::test_read_pyc", "testing/test_assertrewrite.py::TestAssertionRewriteHookDetails::test_read_pyc_more_invalid", "testing/test_assertrewrite.py::TestAssertionRewriteHookDetails::test_read_pyc_success", "testing/test_assertrewrite.py::TestAssertionRewriteHookDetails::test_reload_is_same_and_reloads", "testing/test_assertrewrite.py::TestAssertionRewriteHookDetails::test_resources_provider_for_loader", "testing/test_assertrewrite.py::TestAssertionRewriteHookDetails::test_sys_meta_path_munged", "testing/test_assertrewrite.py::TestAssertionRewriteHookDetails::test_write_pyc", "testing/test_assertrewrite.py::TestEarlyRewriteBailout::test_basic", "testing/test_assertrewrite.py::TestEarlyRewriteBailout::test_cwd_changed", "testing/test_assertrewrite.py::TestEarlyRewriteBailout::test_pattern_contains_subdirectories", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_inline_walrus_operator", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_inline_walrus_operator_reverse", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_walrus_no_variable_name_conflict", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_walrus_operator", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_walrus_operator_boolean_composite", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_walrus_operator_boolean_none_fails", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_walrus_operator_compare_boolean_fails", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_walrus_operator_dont_rewrite", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_walrus_operator_fail_assertion", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_walrus_operator_true_assertion_and_changes_variable_value", "testing/test_assertrewrite.py::TestIssue10743::test_assertion_walrus_operator_value_changes_cleared_after_each_test", "testing/test_assertrewrite.py::TestIssue11028::test_assertion_walrus_operator_equals_operand_function", "testing/test_assertrewrite.py::TestIssue11028::test_assertion_walrus_operator_equals_operand_function_arg_as_function", "testing/test_assertrewrite.py::TestIssue11028::test_assertion_walrus_operator_equals_operand_function_keyword_arg", "testing/test_assertrewrite.py::TestIssue11028::test_assertion_walrus_operator_gt_operand_function", "testing/test_assertrewrite.py::TestIssue11028::test_assertion_walrus_operator_in_operand", "testing/test_assertrewrite.py::TestIssue11028::test_assertion_walrus_operator_in_operand_json_dumps", "testing/test_assertrewrite.py::TestIssue2121::test_rewrite_python_files_contain_subdirs", "testing/test_assertrewrite.py::TestIssue925::test_long_case", "testing/test_assertrewrite.py::TestIssue925::test_many_brackets", "testing/test_assertrewrite.py::TestIssue925::test_simple_case", "testing/test_assertrewrite.py::TestPyCacheDir::test_get_cache_dir[/tmp/pycs-/home/<USER>/src/foo.py-/tmp/pycs/home/<USER>/src]", "testing/test_assertrewrite.py::TestPyCacheDir::test_get_cache_dir[None-/home/<USER>/src/foo.py-/home/<USER>/src/__pycache__]", "testing/test_assertrewrite.py::TestPyCacheDir::test_get_cache_dir[None-d:/projects/src/foo.py-d:/projects/src/__pycache__]", "testing/test_assertrewrite.py::TestPyCacheDir::test_get_cache_dir[c:/tmp/pycs-d:/projects/src/foo.py-c:/tmp/pycs/projects/src]", "testing/test_assertrewrite.py::TestPyCacheDir::test_sys_pycache_prefix_integration", "testing/test_assertrewrite.py::TestReprSizeVerbosity::test_default_verbosity", "testing/test_assertrewrite.py::TestReprSizeVerbosity::test_get_maxsize_for_saferepr[0-240]", "testing/test_assertrewrite.py::TestReprSizeVerbosity::test_get_maxsize_for_saferepr[1-2400]", "testing/test_assertrewrite.py::TestReprSizeVerbosity::test_get_maxsize_for_saferepr[2-None]", "testing/test_assertrewrite.py::TestReprSizeVerbosity::test_get_maxsize_for_saferepr[3-None]", "testing/test_assertrewrite.py::TestReprSizeVerbosity::test_increased_verbosity", "testing/test_assertrewrite.py::TestReprSizeVerbosity::test_max_increased_verbosity", "testing/test_assertrewrite.py::TestRewriteOnImport::test_cached_pyc_includes_pytest_version", "testing/test_assertrewrite.py::TestRewriteOnImport::test_dont_write_bytecode", "testing/test_assertrewrite.py::TestRewriteOnImport::test_load_resource_via_files_with_rewrite", "testing/test_assertrewrite.py::TestRewriteOnImport::test_orphaned_pyc_file", "testing/test_assertrewrite.py::TestRewriteOnImport::test_package", "testing/test_assertrewrite.py::TestRewriteOnImport::test_package_without__init__py", "testing/test_assertrewrite.py::TestRewriteOnImport::test_pyc_vs_pyo", "testing/test_assertrewrite.py::TestRewriteOnImport::test_pycache_is_a_file", "testing/test_assertrewrite.py::TestRewriteOnImport::test_pycache_is_readonly", "testing/test_assertrewrite.py::TestRewriteOnImport::test_readonly", "testing/test_assertrewrite.py::TestRewriteOnImport::test_remember_rewritten_modules", "testing/test_assertrewrite.py::TestRewriteOnImport::test_rewrite_module_imported_from_conftest", "testing/test_assertrewrite.py::TestRewriteOnImport::test_rewrite_warning", "testing/test_assertrewrite.py::TestRewriteOnImport::test_rewrite_warning_using_pytest_plugins", "testing/test_assertrewrite.py::TestRewriteOnImport::test_rewrite_warning_using_pytest_plugins_env_var", "testing/test_assertrewrite.py::TestRewriteOnImport::test_translate_newlines", "testing/test_assertrewrite.py::TestRewriteOnImport::test_zipfile", "testing/test_assertrewrite.py::test_get_assertion_exprs[assert", "testing/test_assertrewrite.py::test_get_assertion_exprs[assertion", "testing/test_assertrewrite.py::test_get_assertion_exprs[backslash", "testing/test_assertrewrite.py::test_get_assertion_exprs[escaped", "testing/test_assertrewrite.py::test_get_assertion_exprs[latin1", "testing/test_assertrewrite.py::test_get_assertion_exprs[multi", "testing/test_assertrewrite.py::test_get_assertion_exprs[multi-line", "testing/test_assertrewrite.py::test_get_assertion_exprs[multiple", "testing/test_assertrewrite.py::test_get_assertion_exprs[no", "testing/test_assertrewrite.py::test_get_assertion_exprs[trivial]", "testing/test_assertrewrite.py::test_get_assertion_exprs[utf-8", "testing/test_assertrewrite.py::test_issue731", "testing/test_assertrewrite.py::test_rewrite_infinite_recursion", "testing/test_assertrewrite.py::test_source_mtime_long_long[-1]", "testing/test_assertrewrite.py::test_source_mtime_long_long[1]", "testing/test_assertrewrite.py::test_try_makedirs"], "environment_setup_commit": "10056865d2a4784934ce043908a0e78d0578f677"}, {"repo": "scikit-learn/scikit-learn", "instance_id": "scikit-learn__scikit-learn-13584", "base_commit": "0e3c1879b06d839171b7d0a607d71bbb19a966a9", "patch": "diff --git a/sklearn/utils/_pprint.py b/sklearn/utils/_pprint.py\n--- a/sklearn/utils/_pprint.py\n+++ b/sklearn/utils/_pprint.py\n@@ -95,7 +95,7 @@ def _changed_params(estimator):\n     init_params = signature(init_func).parameters\n     init_params = {name: param.default for name, param in init_params.items()}\n     for k, v in params.items():\n-        if (v != init_params[k] and\n+        if (repr(v) != repr(init_params[k]) and\n                 not (is_scalar_nan(init_params[k]) and is_scalar_nan(v))):\n             filtered_params[k] = v\n     return filtered_params\n", "test_patch": "diff --git a/sklearn/utils/tests/test_pprint.py b/sklearn/utils/tests/test_pprint.py\n--- a/sklearn/utils/tests/test_pprint.py\n+++ b/sklearn/utils/tests/test_pprint.py\n@@ -4,6 +4,7 @@\n import numpy as np\n \n from sklearn.utils._pprint import _EstimatorPrettyPrinter\n+from sklearn.linear_model import LogisticRegressionCV\n from sklearn.pipeline import make_pipeline\n from sklearn.base import BaseEstimator, TransformerMixin\n from sklearn.feature_selection import SelectKBest, chi2\n@@ -212,6 +213,9 @@ def test_changed_only():\n     expected = \"\"\"SimpleImputer()\"\"\"\n     assert imputer.__repr__() == expected\n \n+    # make sure array parameters don't throw error (see #13583)\n+    repr(LogisticRegressionCV(Cs=np.array([0.1, 1])))\n+\n     set_config(print_changed_only=False)\n \n \n", "problem_statement": "bug in print_changed_only in new repr: vector values\n```python\r\nimport sklearn\r\nimport numpy as np\r\nfrom sklearn.linear_model import LogisticRegressionCV\r\nsklearn.set_config(print_changed_only=True)\r\nprint(LogisticRegressionCV(Cs=np.array([0.1, 1])))\r\n```\r\n> ValueError: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()\r\n\r\nping @NicolasHug \r\n\n", "hints_text": "", "created_at": "2019-04-05T23:09:48Z", "version": "0.21", "FAIL_TO_PASS": ["sklearn/utils/tests/test_pprint.py::test_changed_only", "sklearn/utils/tests/test_pprint.py::test_deeply_nested", "sklearn/utils/tests/test_pprint.py::test_gridsearch", "sklearn/utils/tests/test_pprint.py::test_gridsearch_pipeline", "sklearn/utils/tests/test_pprint.py::test_n_max_elements_to_show", "sklearn/utils/tests/test_pprint.py::test_pipeline"], "PASS_TO_PASS": ["sklearn/utils/tests/test_pprint.py::test_basic", "sklearn/utils/tests/test_pprint.py::test_builtin_prettyprinter", "sklearn/utils/tests/test_pprint.py::test_length_constraint"], "environment_setup_commit": "7813f7efb5b2012412888b69e73d76f2df2b50b6"}, {"repo": "django/django", "instance_id": "django__django-15781", "base_commit": "8d160f154f0240a423e83ffe0690e472f837373c", "patch": "diff --git a/django/core/management/base.py b/django/core/management/base.py\n--- a/django/core/management/base.py\n+++ b/django/core/management/base.py\n@@ -286,10 +286,10 @@ def create_parser(self, prog_name, subcommand, **kwargs):\n         Create and return the ``ArgumentParser`` which will be used to\n         parse the arguments to this command.\n         \"\"\"\n+        kwargs.setdefault(\"formatter_class\", DjangoHelpFormatter)\n         parser = CommandParser(\n             prog=\"%s %s\" % (os.path.basename(prog_name), subcommand),\n             description=self.help or None,\n-            formatter_class=DjangoHelpFormatter,\n             missing_args_message=getattr(self, \"missing_args_message\", None),\n             called_from_command_line=getattr(self, \"_called_from_command_line\", None),\n             **kwargs,\n", "test_patch": "diff --git a/tests/user_commands/tests.py b/tests/user_commands/tests.py\n--- a/tests/user_commands/tests.py\n+++ b/tests/user_commands/tests.py\n@@ -1,4 +1,5 @@\n import os\n+from argparse import ArgumentDefaultsHelpFormatter\n from io import String<PERSON>\n from unittest import mock\n \n@@ -408,8 +409,14 @@ def test_subparser_invalid_option(self):\n     def test_create_parser_kwargs(self):\n         \"\"\"BaseCommand.create_parser() passes kwargs to CommandParser.\"\"\"\n         epilog = \"some epilog text\"\n-        parser = BaseCommand().create_parser(\"prog_name\", \"subcommand\", epilog=epilog)\n+        parser = BaseCommand().create_parser(\n+            \"prog_name\",\n+            \"subcommand\",\n+            epilog=epilog,\n+            formatter_class=ArgumentDefaultsHelpFormatter,\n+        )\n         self.assertEqual(parser.epilog, epilog)\n+        self.assertEqual(parser.formatter_class, ArgumentDefaultsHelpFormatter)\n \n     def test_outputwrapper_flush(self):\n         out = StringIO()\n", "problem_statement": "Customizable management command formatters.\nDescription\n\t\nWith code like:\nclass Command(BaseCommand):\n\thelp = '''\n\tImport a contract from tzkt.\n\tExample usage:\n\t\t./manage.py tzkt_import 'Tezos Mainnet' KT1HTDtMBRCKoNHjfWEEvXneGQpCfPAt6BRe\n\t'''\nHelp output is:\n$ ./manage.py help tzkt_import\nusage: manage.py tzkt_import [-h] [--api API] [--version] [-v {0,1,2,3}] [--settings SETTINGS]\n\t\t\t\t\t\t\t [--pythonpath PYTHONPATH] [--traceback] [--no-color] [--force-color]\n\t\t\t\t\t\t\t [--skip-checks]\n\t\t\t\t\t\t\t blockchain target\nImport a contract from tzkt Example usage: ./manage.py tzkt_import 'Tezos Mainnet'\nKT1HTDtMBRCKoNHjfWEEvXneGQpCfPAt6BRe\npositional arguments:\n blockchain\t\t\tName of the blockchain to import into\n target\t\t\t\tId of the contract to import\nWhen that was expected:\n$ ./manage.py help tzkt_import\nusage: manage.py tzkt_import [-h] [--api API] [--version] [-v {0,1,2,3}] [--settings SETTINGS]\n\t\t\t\t\t\t\t [--pythonpath PYTHONPATH] [--traceback] [--no-color] [--force-color]\n\t\t\t\t\t\t\t [--skip-checks]\n\t\t\t\t\t\t\t blockchain target\nImport a contract from tzkt \nExample usage: \n\t./manage.py tzkt_import 'Tezos Mainnet' KT1HTDtMBRCKoNHjfWEEvXneGQpCfPAt6BRe\npositional arguments:\n blockchain\t\t\tName of the blockchain to import into\n target\t\t\t\tId of the contract to import\n", "hints_text": "This seems no fault of Django but is rather ​the default behavior of ArgumentParser (\"By default, ArgumentParser objects line-wrap the description and epilog texts in command-line help messages\"). This can be changed by using a custom ​formatter_class, though Django already specifies a custom one (​DjangoHelpFormatter).\nIt seems reasonable, to make it customizable by passing via kwargs to the ​BaseCommand.create_parser() (as documented): django/core/management/base.py diff --git a/django/core/management/base.py b/django/core/management/base.py index f0e711ac76..52407807d8 100644 a b class BaseCommand: 286286 Create and return the ``ArgumentParser`` which will be used to 287287 parse the arguments to this command. 288288 \"\"\" 289 kwargs.setdefault(\"formatter_class\", DjangoHelpFormatter) 289290 parser = CommandParser( 290291 prog=\"%s %s\" % (os.path.basename(prog_name), subcommand), 291292 description=self.help or None, 292 formatter_class=DjangoHelpFormatter, 293293 missing_args_message=getattr(self, \"missing_args_message\", None), 294294 called_from_command_line=getattr(self, \"_called_from_command_line\", None), 295295 **kwargs, What do you think?\nLooks good but I don't see a reason for keeping a default that swallows newlines because PEP257 forbids having a multiline sentence on the first line anyway: Multi-line docstrings consist of a summary line just like a one-line docstring, followed by a blank line, followed by a more elaborate description. As such, the default formater which purpose is to unwrap the first sentence encourages breaking PEP 257. And users who are naturally complying with PEP257 will have to override the formatter, it should be the other way around.\nAlso, the not-unwraping formater will also look fine with existing docstrings, it will work for both use cases, while the current one only works for one use case and breaks the other. The default formater should work for both\nReplying to James Pic: Also, the not-unwraping formater will also look fine with existing docstrings, it will work for both use cases, while the current one only works for one use case and breaks the other. The default formater should work for both It seems you think that Python's (not Django's) default behavior should be changed according to PEP 257. I'd recommend to start a discussion in Python's bugtracker. As far as I'm aware the proposed solution will allow users to freely change a formatter, which should be enough from the Django point of view.\nNo, I think that Django's default behavior should match Python's PEP 257, and at the same time, have a default that works in all use cases. I think my report and comments are pretty clear, I fail to understand how you could get my comment completely backward, so, unless you have any specific question about this statement, I'm going to give up on this.\nSo as part of this issue, do we make changes to allow a user to override the formatter through kwargs and also keep DjangoHelpFormatter as the default?\nReplying to Subhankar Hotta: So as part of this issue, do we make changes to allow a user to override the formatter through kwargs and also keep DjangoHelpFormatter as the default? Yes, see comment.", "created_at": "2022-06-18T19:39:34Z", "version": "4.2", "FAIL_TO_PASS": ["BaseCommand.create_parser() passes kwargs to CommandParser."], "PASS_TO_PASS": ["An unknown command raises CommandError", "By default, call_command should not trigger the check framework, unless", "Exception raised in a command should raise CommandError with", "It should be possible to pass non-string arguments to call_command.", "Management commands can also be loaded from Python eggs.", "To avoid conflicts with custom options, commands don't allow", "When passing the long option name to call_command, the available option", "When the Command handle method is decorated with @no_translations,", "find_command should still work when the PATH environment variable", "test_call_command_unrecognized_option (user_commands.tests.CommandTests)", "test_call_command_with_required_parameters_in_mixed_options (user_commands.tests.CommandTests)", "test_call_command_with_required_parameters_in_options (user_commands.tests.CommandTests)", "test_calling_a_command_with_no_app_labels_and_parameters_raise_command_error (user_commands.tests.CommandTests)", "test_calling_a_command_with_only_empty_parameter_should_ends_gracefully (user_commands.tests.CommandTests)", "test_calling_command_with_app_labels_and_parameters_should_be_ok (user_commands.tests.CommandTests)", "test_calling_command_with_parameters_and_app_labels_at_the_end_should_be_ok (user_commands.tests.CommandTests)", "test_check_migrations (user_commands.tests.CommandTests)", "test_command (user_commands.tests.CommandTests)", "test_command_add_arguments_after_common_arguments (user_commands.tests.CommandTests)", "test_command_style (user_commands.tests.CommandTests)", "test_get_random_secret_key (user_commands.tests.UtilsTests)", "test_is_ignored_path_false (user_commands.tests.UtilsTests)", "test_is_ignored_path_true (user_commands.tests.UtilsTests)", "test_language_preserved (user_commands.tests.CommandTests)", "test_mutually_exclusive_group_required_const_options (user_commands.tests.CommandTests)", "test_mutually_exclusive_group_required_options (user_commands.tests.CommandTests)", "test_mutually_exclusive_group_required_with_same_dest_args (user_commands.tests.CommandTests)", "test_mutually_exclusive_group_required_with_same_dest_options (user_commands.tests.CommandTests)", "test_no_existent_external_program (user_commands.tests.UtilsTests)", "test_normalize_path_patterns_truncates_wildcard_base (user_commands.tests.UtilsTests)", "test_output_transaction (user_commands.tests.CommandTests)", "test_outputwrapper_flush (user_commands.tests.CommandTests)", "test_required_const_options (user_commands.tests.CommandTests)", "test_required_list_option (user_commands.tests.CommandTests)", "test_requires_system_checks_empty (user_commands.tests.CommandTests)", "test_requires_system_checks_invalid (user_commands.tests.CommandTests)", "test_requires_system_checks_specific (user_commands.tests.CommandTests)", "test_script_prefix_set_in_commands (user_commands.tests.CommandRunTests)", "test_skip_checks (user_commands.tests.CommandRunTests)", "test_subparser (user_commands.tests.CommandTests)", "test_subparser_dest_args (user_commands.tests.CommandTests)", "test_subparser_dest_required_args (user_commands.tests.CommandTests)", "test_subparser_invalid_option (user_commands.tests.CommandTests)"], "environment_setup_commit": "0fbdb9784da915fce5dcc1fe82bac9b4785749e5"}]