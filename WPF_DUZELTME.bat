@echo off
echo ========================================
echo DonatPlus WPF Uyumluluk Düzeltme
echo ========================================
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

echo.
echo 1. WPF bağımlılıkları kontrol ediliyor...

REM .NET Framework 4.8 kontrolü
echo Checking .NET Framework 4.8...
reg query "HKLM\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release | findstr "528040" >nul
if %errorLevel% neq 0 (
    echo UYARI: .NET Framework 4.8 bulunamadı!
    echo .NET Framework 4.8 indiriliyor...
    
    powershell -Command "& {Invoke-WebRequest -Uri 'https://download.microsoft.com/download/2/d/1/2d1c5e93-6372-4118-b259-1c141d5524d9/ndp48-web.exe' -OutFile '%TEMP%\ndp48-web.exe'}"
    
    if exist "%TEMP%\ndp48-web.exe" (
        echo .NET Framework 4.8 kuruluyor...
        "%TEMP%\ndp48-web.exe" /quiet /norestart
        timeout /t 60 /nobreak >nul
        del "%TEMP%\ndp48-web.exe"
        echo .NET Framework 4.8 kuruldu.
    )
) else (
    echo ✓ .NET Framework 4.8 mevcut
)

REM Windows Desktop Experience kontrolü
echo Checking Windows Desktop Experience...
dism /online /get-features | findstr "Microsoft-Windows-DesktopExperience" | findstr "Enabled" >nul
if %errorLevel% neq 0 (
    echo UYARI: Windows Desktop Experience etkin değil!
    echo Windows Desktop Experience etkinleştiriliyor...
    dism /online /enable-feature /featurename:Microsoft-Windows-DesktopExperience /all /norestart
) else (
    echo ✓ Windows Desktop Experience etkin
)

echo.
echo 2. WPF runtime bileşenleri kontrol ediliyor...

REM PresentationFramework kontrolü
if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\PresentationFramework.dll" (
    echo ✓ PresentationFramework.dll mevcut
) else (
    echo HATA: PresentationFramework.dll bulunamadı!
)

REM WindowsBase kontrolü
if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\WindowsBase.dll" (
    echo ✓ WindowsBase.dll mevcut
) else (
    echo HATA: WindowsBase.dll bulunamadı!
)

echo.
echo 3. GAC (Global Assembly Cache) temizleniyor...
REM GAC temizleme
gacutil /l | findstr "DonatPlus" >nul
if %errorLevel% == 0 (
    echo DonatPlus assemblies GAC'den kaldırılıyor...
    for /f "tokens=1" %%i in ('gacutil /l ^| findstr "DonatPlus"') do (
        gacutil /u %%i >nul 2>&1
    )
)

echo.
echo 4. Temp dosyaları temizleniyor...
REM .NET temp dosyalarını temizle
del /q /s "%TEMP%\*.tmp" >nul 2>&1
del /q /s "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\Temporary ASP.NET Files\*" >nul 2>&1
del /q /s "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\Temporary ASP.NET Files\*" >nul 2>&1

echo.
echo 5. Registry düzeltmeleri yapılıyor...

REM WPF registry ayarları
reg add "HKLM\SOFTWARE\Microsoft\.NETFramework\v4.0.30319" /v SchUseStrongCrypto /t REG_DWORD /d 1 /f >nul
reg add "HKLM\SOFTWARE\Wow6432Node\Microsoft\.NETFramework\v4.0.30319" /v SchUseStrongCrypto /t REG_DWORD /d 1 /f >nul

REM BAML cache temizleme
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\FileExts\.baml" /f >nul 2>&1

echo.
echo 6. Özel WPF çalıştırma scripti oluşturuluyor...

REM Özel çalıştırma scripti
echo @echo off > "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo echo DonatPlus WPF Launcher >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo echo. >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo REM WPF uyumluluk ayarları >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo set DOTNET_EnableWriteXorExecute=0 >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo set DOTNET_DefaultDiagnosticPortSuspend=0 >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo set COMPlus_EnableDiagnostics=0 >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo. >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo REM Program çalıştır >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo cd /d "C:\Program Files\DonatPlus" >> "%TEMP%\DonatPlus_WPF_Launcher.bat"
echo start "" "DonatPlus.UI.exe" >> "%TEMP%\DonatPlus_WPF_Launcher.bat"

copy "%TEMP%\DonatPlus_WPF_Launcher.bat" "C:\Program Files\DonatPlus\DonatPlus_Launcher.bat" >nul
del "%TEMP%\DonatPlus_WPF_Launcher.bat"

echo.
echo 7. Yeni masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop

REM Eski kısayolu sil
if exist "%DESKTOP%\DonatPlus.lnk" del "%DESKTOP%\DonatPlus.lnk"

REM Yeni kısayol oluştur (launcher ile)
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "C:\Program Files\DonatPlus\DonatPlus_Launcher.bat" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "C:\Program Files\DonatPlus" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "DonatPlus - Betonarme Donatı Metraj Hesaplama (WPF Uyumlu)" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WindowStyle = 7 >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"
cscript "%TEMP%\CreateShortcut.vbs" >nul
del "%TEMP%\CreateShortcut.vbs"

echo.
echo ========================================
echo WPF DÜZELTME TAMAMLANDI!
echo ========================================
echo.
echo ✓ .NET Framework 4.8 kontrol edildi
echo ✓ Windows Desktop Experience kontrol edildi
echo ✓ WPF runtime bileşenleri kontrol edildi
echo ✓ GAC temizlendi
echo ✓ Temp dosyaları temizlendi
echo ✓ Registry düzeltmeleri yapıldı
echo ✓ Özel WPF launcher oluşturuldu
echo ✓ Yeni masaüstü kısayolu oluşturuldu
echo.
echo Şimdi masaüstündeki DonatPlus kısayolunu deneyin!
echo.
echo Eğer hala sorun yaşıyorsanız:
echo 1. Bilgisayarı yeniden başlatın
echo 2. Windows Update'leri kontrol edin
echo 3. Antivirus yazılımını geçici olarak kapatın
echo.
pause
