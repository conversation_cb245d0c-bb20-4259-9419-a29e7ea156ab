using DonatPlus.Database.Models;
using System;
using System.Windows;

namespace DonatPlus.UI.Views
{
    /// <summary>
    /// NewProjectDialog.xaml etkile<PERSON>im mantı<PERSON>ı
    /// </summary>
    public partial class NewProjectDialog : Window
    {
        public Project? Project { get; private set; }
        
        public NewProjectDialog()
        {
            InitializeComponent();
            
            // Varsayılan değerleri ayarla
            TxtEngineer.Text = Environment.UserName;
        }
        
        private void BtnCreate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validasyon
                if (string.IsNullOrWhiteSpace(TxtProjectName.Text))
                {
                    MessageBox.Show("Proje adı zorunludur.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtProjectName.Focus();
                    return;
                }
                
                // Proje oluştur
                Project = new Project
                {
                    Name = TxtProjectName.Text.Trim(),
                    ProjectNumber = TxtProjectNumber.Text.Trim(),
                    ClientName = TxtClientName.Text.Trim(),
                    Location = TxtLocation.Text.Trim(),
                    Engineer = TxtEngineer.Text.Trim(),
                    Description = TxtDescription.Text.Trim(),
                    ConcreteClass = ((System.Windows.Controls.ComboBoxItem)CmbConcreteClass.SelectedItem)?.Content?.ToString() ?? "C25",
                    SteelGrade = ((System.Windows.Controls.ComboBoxItem)CmbSteelGrade.SelectedItem)?.Content?.ToString() ?? "S420",
                    IsActive = ChkIsActive.IsChecked ?? true,
                    CreatedDate = DateTime.Now
                };
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Proje oluşturma hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
