using DonatPlus.Core.Services;
using DonatPlus.Database.DbContext;
using DonatPlus.Reports.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Windows;

namespace DonatPlus.UI
{
    /// <summary>
    /// App.xaml etkileşim mantığı
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;
        
        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // Host builder oluştur
                var hostBuilder = Host.CreateDefaultBuilder()
                    .ConfigureServices(ConfigureServices)
                    .ConfigureLogging(logging =>
                    {
                        logging.ClearProviders();
                        logging.AddConsole();
                        logging.AddDebug();
                        logging.SetMinimumLevel(LogLevel.Information);
                    });
                
                _host = hostBuilder.Build();
                
                // Servisleri başlat
                await _host.StartAsync();
                
                // Veritabanını başlat
                await InitializeDatabase();
                
                // Ana pencereyi oluştur ve göster
                var mainWindow = _host.Services.GetRequiredService<MainWindow>();
                mainWindow.Show();
                
                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Uygulama başlatma hatası: {ex.Message}", "Kritik Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }
        
        private void ConfigureServices(IServiceCollection services)
        {
            // Logging
            services.AddLogging();
            
            // Database
            services.AddDbContext<DonatPlusContext>(options =>
            {
                var dbPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "DonatPlus",
                    "donatplus.db"
                );
                
                // Klasör yoksa oluştur
                var directory = Path.GetDirectoryName(dbPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                options.UseSqlite($"Data Source={dbPath}");
            });
            
            // Core Services
            services.AddTransient<RebarDetectionService>();
            services.AddTransient<GeometryCalculationService>();
            
            // Report Services
            services.AddTransient<ReportGenerationService>();
            
            // UI Services
            services.AddTransient<MainWindow>();
            
            // Views (eğer dependency injection kullanılacaksa)
            services.AddTransient<Views.NewProjectDialog>();
        }
        
        private async System.Threading.Tasks.Task InitializeDatabase()
        {
            try
            {
                using var scope = _host!.Services.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<DonatPlusContext>();
                
                // Veritabanını oluştur
                await dbContext.Database.EnsureCreatedAsync();
                
                // Migrasyonları uygula (eğer varsa)
                var pendingMigrations = await dbContext.Database.GetPendingMigrationsAsync();
                if (pendingMigrations.Any())
                {
                    await dbContext.Database.MigrateAsync();
                }
                
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<App>>();
                logger.LogInformation("Veritabanı başarıyla başlatıldı");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Veritabanı başlatma hatası: {ex.Message}", "Veritabanı Hatası", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }
        
        protected override async void OnExit(ExitEventArgs e)
        {
            try
            {
                if (_host != null)
                {
                    await _host.StopAsync();
                    _host.Dispose();
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't prevent shutdown
                System.Diagnostics.Debug.WriteLine($"Shutdown error: {ex.Message}");
            }
            
            base.OnExit(e);
        }
        
        private void Application_DispatcherUnhandledException(object sender, 
            System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                var logger = _host?.Services.GetService<ILogger<App>>();
                logger?.LogError(e.Exception, "İşlenmeyen uygulama hatası");
                
                MessageBox.Show($"Beklenmeyen bir hata oluştu:\n\n{e.Exception.Message}", 
                    "Uygulama Hatası", MessageBoxButton.OK, MessageBoxImage.Error);
                
                e.Handled = true;
            }
            catch
            {
                // Son çare - basit hata mesajı
                MessageBox.Show("Kritik bir hata oluştu. Uygulama kapatılacak.", 
                    "Kritik Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }
        
        private void Application_Startup(object sender, StartupEventArgs e)
        {
            // Komut satırı argümanlarını işle
            if (e.Args.Length > 0)
            {
                // Örnek: donatplus.exe --project "C:\path\to\project.dwg"
                for (int i = 0; i < e.Args.Length; i++)
                {
                    switch (e.Args[i].ToLowerInvariant())
                    {
                        case "--project":
                        case "-p":
                            if (i + 1 < e.Args.Length)
                            {
                                // Proje dosyası yolu
                                var projectPath = e.Args[i + 1];
                                // TODO: Proje dosyasını aç
                            }
                            break;
                            
                        case "--help":
                        case "-h":
                            MessageBox.Show(
                                "DonatPlus - Betonarme Donatı Metraj Hesaplama\n\n" +
                                "Kullanım:\n" +
                                "donatplus.exe [seçenekler]\n\n" +
                                "Seçenekler:\n" +
                                "--project, -p <dosya>  Proje dosyasını aç\n" +
                                "--help, -h             Bu yardım mesajını göster\n" +
                                "--version, -v          Sürüm bilgisini göster",
                                "DonatPlus Yardım", MessageBoxButton.OK, MessageBoxImage.Information);
                            Shutdown(0);
                            return;
                            
                        case "--version":
                        case "-v":
                            var version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version;
                            MessageBox.Show($"DonatPlus v{version}", "Sürüm Bilgisi", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            Shutdown(0);
                            return;
                    }
                }
            }
        }
    }
}
