using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace DonatPlus.Launcher
{
    class Program
    {
        [DllImport("kernel32.dll")]
        static extern IntPtr GetConsoleWindow();

        [DllImport("user32.dll")]
        static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        const int SW_HIDE = 0;

        [STAThread]
        static void Main(string[] args)
        {
            // Console penceresini gizle
            var handle = GetConsoleWindow();
            ShowWindow(handle, SW_HIDE);

            try
            {
                Console.WriteLine("DonatPlus Launcher başlatılıyor...");
                
                // Çalışma dizinini ayarla
                var exeDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                if (!string.IsNullOrEmpty(exeDir))
                {
                    Directory.SetCurrentDirectory(exeDir);
                }

                // WPF uyumluluk ayarları
                Environment.SetEnvironmentVariable("DOTNET_EnableWriteXorExecute", "0");
                Environment.SetEnvironmentVariable("DOTNET_DefaultDiagnosticPortSuspend", "0");
                Environment.SetEnvironmentVariable("COMPlus_EnableDiagnostics", "0");
                Environment.SetEnvironmentVariable("DOTNET_SYSTEM_GLOBALIZATION_INVARIANT", "false");

                // Ana programın yolunu bul
                string mainExePath = Path.Combine(exeDir ?? "", "DonatPlus.UI.exe");
                
                if (!File.Exists(mainExePath))
                {
                    ShowError($"Ana program bulunamadı: {mainExePath}");
                    return;
                }

                Console.WriteLine($"Ana program başlatılıyor: {mainExePath}");

                // Ana programı başlat
                var startInfo = new ProcessStartInfo
                {
                    FileName = mainExePath,
                    UseShellExecute = true,
                    WorkingDirectory = exeDir,
                    WindowStyle = ProcessWindowStyle.Normal
                };

                // Komut satırı argümanlarını aktar
                if (args.Length > 0)
                {
                    startInfo.Arguments = string.Join(" ", args);
                }

                var process = Process.Start(startInfo);
                
                if (process == null)
                {
                    ShowError("Ana program başlatılamadı!");
                    return;
                }

                Console.WriteLine("Ana program başarıyla başlatıldı.");
                
                // Ana programın başlamasını bekle
                Thread.Sleep(2000);
                
                // Ana program çalışıyor mu kontrol et
                if (process.HasExited)
                {
                    ShowError($"Ana program beklenmedik şekilde kapandı. Çıkış kodu: {process.ExitCode}");
                    return;
                }

                Console.WriteLine("Launcher görevi tamamlandı.");
            }
            catch (Exception ex)
            {
                ShowError($"Launcher hatası: {ex.Message}\n\nDetay:\n{ex}");
            }
        }

        private static void ShowError(string message)
        {
            Console.WriteLine($"HATA: {message}");
            
            try
            {
                MessageBox.Show(
                    $"{message}\n\nSorun giderme önerileri:\n" +
                    "1. .NET 6.0 Desktop Runtime yüklü olduğundan emin olun\n" +
                    "2. Windows Update'leri kontrol edin\n" +
                    "3. Antivirus yazılımını geçici olarak kapatın\n" +
                    "4. Programı yönetici olarak çalıştırmayı deneyin\n\n" +
                    "Teknik destek için log dosyalarını kontrol edin.",
                    "DonatPlus Launcher Hatası",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
            catch
            {
                // MessageBox gösterilemezse console'a yaz
                Console.WriteLine("MessageBox gösterilemedi.");
            }
        }
    }
}
