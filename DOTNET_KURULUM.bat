@echo off
echo ========================================
echo .NET 6.0 Desktop Runtime Kurulum
echo ========================================
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

echo.
echo 1. Mevcut .NET versiyonları kontrol ediliyor...
dotnet --list-runtimes

echo.
echo 2. .NET 6.0 Desktop Runtime kontrol ediliyor...
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 6." >nul
if %errorLevel% == 0 (
    echo ✓ .NET 6.0 Desktop Runtime zaten yüklü!
    echo.
    echo Yüklü versiyon:
    dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 6."
    echo.
    echo Program çalışmıyorsa başka bir sorun var.
    pause
    exit /b 0
)

echo UYARI: .NET 6.0 Desktop Runtime bulunamadı!
echo.

echo 3. .NET 6.0 Desktop Runtime indiriliyor...
echo Lütfen bekleyin...

REM .NET 6.0 Desktop Runtime indir
powershell -Command "try { Write-Host 'İndirme başlıyor...'; Invoke-WebRequest -Uri 'https://download.microsoft.com/download/6/6/4/664414c2-06ab-4b5b-8a82-e74a78b27a9e/windowsdesktop-runtime-6.0.25-win-x64.exe' -OutFile '%TEMP%\dotnet6-desktop.exe' -UseBasicParsing; Write-Host 'İndirme tamamlandı!' } catch { Write-Host 'İndirme hatası: ' + $_.Exception.Message }"

if exist "%TEMP%\dotnet6-desktop.exe" (
    echo ✓ İndirme başarılı!
    echo.
    echo 4. .NET 6.0 Desktop Runtime kuruluyor...
    echo Bu işlem birkaç dakika sürebilir...
    
    "%TEMP%\dotnet6-desktop.exe" /quiet /norestart
    
    echo.
    echo 5. Kurulum kontrol ediliyor...
    timeout /t 10 /nobreak >nul
    
    dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 6." >nul
    if %errorLevel% == 0 (
        echo ✓ .NET 6.0 Desktop Runtime başarıyla kuruldu!
        echo.
        echo Yüklü versiyon:
        dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 6."
        echo.
        echo Şimdi DonatPlus programını tekrar deneyin!
    ) else (
        echo UYARI: Kurulum doğrulanamadı.
        echo Bilgisayarı yeniden başlatıp tekrar deneyin.
    )
    
    REM Geçici dosyayı sil
    del "%TEMP%\dotnet6-desktop.exe" >nul 2>&1
    
) else (
    echo HATA: İndirme başarısız!
    echo.
    echo Manuel kurulum için:
    echo 1. https://dotnet.microsoft.com/download/dotnet/6.0 adresine gidin
    echo 2. "Desktop Runtime x64" seçeneğini indirin
    echo 3. İndirilen dosyayı çalıştırın
    echo.
)

echo.
echo 6. Visual C++ Redistributable kontrol ediliyor...
reg query "HKLM\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Visual C++ Redistributable mevcut
) else (
    echo UYARI: Visual C++ Redistributable bulunamadı!
    echo.
    echo Visual C++ Redistributable indiriliyor...
    powershell -Command "try { Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x64.exe' -OutFile '%TEMP%\vc_redist.exe' -UseBasicParsing } catch { Write-Host 'İndirme hatası' }"
    
    if exist "%TEMP%\vc_redist.exe" (
        echo Visual C++ Redistributable kuruluyor...
        "%TEMP%\vc_redist.exe" /quiet /norestart
        timeout /t 20 /nobreak >nul
        del "%TEMP%\vc_redist.exe"
        echo ✓ Visual C++ Redistributable kuruldu
    )
)

echo.
echo ========================================
echo KURULUM TAMAMLANDI!
echo ========================================
echo.
echo Şimdi DonatPlus programını tekrar deneyin:
echo 1. Masaüstündeki DonatPlus kısayoluna çift tıklayın
echo 2. Veya HATA_TESPITI.bat çalıştırarak detaylı test yapın
echo.
pause
