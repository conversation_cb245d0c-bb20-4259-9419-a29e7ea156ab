@echo off
echo DonatPlus Test Script
echo ====================

:: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
set SOLUTION_FILE=DonatPlus.sln
set TEST_PROJECT=DonatPlus.Tests\DonatPlus.Tests.csproj

echo .NET SDK versiyonu kontrol ediliyor...
dotnet --version
if errorlevel 1 (
    echo HATA: .NET SDK bulunamadı!
    echo Lütfen https://dotnet.microsoft.com/download adresinden .NET 6.0 SDK'yı indirin.
    pause
    exit /b 1
)

echo.
echo NuGet paketleri geri yükleniyor...
dotnet restore %SOLUTION_FILE%
if errorlevel 1 (
    echo HATA: NuGet restore başarısız!
    pause
    exit /b 1
)

echo.
echo Proje derleniyor...
dotnet build %SOLUTION_FILE% --configuration Debug --no-restore
if errorlevel 1 (
    echo HATA: Build başarısız!
    pause
    exit /b 1
)

echo.
echo Unit testler çalıştırılıyor...
dotnet test %TEST_PROJECT% --configuration Debug --no-build --verbosity normal --logger "console;verbosity=detailed"
if errorlevel 1 (
    echo UYARI: Bazı testler başarısız!
    echo Detaylar için yukarıdaki çıktıyı kontrol edin.
) else (
    echo Tüm testler başarıyla geçti!
)

echo.
echo Test kapsamı raporu oluşturuluyor...
dotnet test %TEST_PROJECT% --configuration Debug --no-build --collect:"XPlat Code Coverage" --results-directory TestResults
if errorlevel 1 (
    echo UYARI: Test kapsamı raporu oluşturulamadı!
) else (
    echo Test kapsamı raporu TestResults klasöründe oluşturuldu.
)

echo.
echo UI projesi test ediliyor...
echo UI projesinin çalışıp çalışmadığını kontrol etmek için kısa bir süre çalıştırılacak...
timeout /t 3 /nobreak >nul

start /wait /b dotnet run --project DonatPlus.UI\DonatPlus.UI.csproj --configuration Debug --no-build &
set UI_PID=%!

:: 10 saniye bekle
timeout /t 10 /nobreak >nul

:: UI uygulamasını kapat (eğer hala çalışıyorsa)
taskkill /f /im DonatPlus.UI.exe 2>nul

echo.
echo ==================
echo Test tamamlandı!
echo ==================
echo.
echo Sonuçlar:
echo - Build: Başarılı
echo - Unit Testler: Yukarıdaki çıktıyı kontrol edin
echo - UI Test: 10 saniye çalıştırıldı
echo.
echo Detaylı test sonuçları için TestResults klasörünü kontrol edin.
echo.
pause
