<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <AssemblyTitle>DonatPlus UI</AssemblyTitle>
    <AssemblyDescription>DonatPlus kullanıcı arayüzü</AssemblyDescription>
    <AssemblyCompany>DonatPlus</AssemblyCompany>
    <AssemblyProduct>DonatPlus</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <!-- <ApplicationIcon>icon.ico</ApplicationIcon> -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.11" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DonatPlus.Core\DonatPlus.Core.csproj" />
    <ProjectReference Include="..\DonatPlus.Database\DonatPlus.Database.csproj" />
    <ProjectReference Include="..\DonatPlus.Reports\DonatPlus.Reports.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**" />
  </ItemGroup>

</Project>
