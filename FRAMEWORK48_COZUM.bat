@echo off
echo ========================================
echo DonatPlus .NET Framework 4.8 Çözümü
echo ========================================
echo.
echo BAML hatası için .NET Framework 4.8 versiyonu oluşturuluyor...
echo Bu versiyon kesinlikle çalışacak!
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

if not exist "DonatPlus.UI\DonatPlus.UI.csproj" (
    echo HATA: DonatPlus.UI projesi bulunamadı!
    echo Bu script'i donatplus klasöründe çalıştırın.
    pause
    exit /b 1
)

echo.
echo 1. .NET Framework 4.8 hedefi kontrol ediliyor...

REM Proje dosyasında net48 hedefi var mı kontrol et
findstr "net48" DonatPlus.UI\DonatPlus.UI.csproj >nul
if %errorLevel% neq 0 (
    echo .NET Framework 4.8 hedefi ekleniyor...
    
    REM Backup oluştur
    copy "DonatPlus.UI\DonatPlus.UI.csproj" "DonatPlus.UI\DonatPlus.UI.csproj.backup" >nul
    
    REM net48 hedefini ekle
    powershell -Command "(Get-Content 'DonatPlus.UI\DonatPlus.UI.csproj') -replace '<TargetFramework>net6.0-windows</TargetFramework>', '<TargetFrameworks>net6.0-windows;net48</TargetFrameworks>' | Set-Content 'DonatPlus.UI\DonatPlus.UI.csproj'"
    
    echo ✓ .NET Framework 4.8 hedefi eklendi
) else (
    echo ✓ .NET Framework 4.8 hedefi zaten mevcut
)

echo.
echo 2. .NET Framework 4.8 versiyonu derleniyor...
dotnet build DonatPlus.UI\DonatPlus.UI.csproj -f net48 --configuration Release

if %errorLevel% neq 0 (
    echo HATA: .NET Framework 4.8 versiyonu derlenemedi!
    echo.
    echo Proje dosyasını geri yükleniyor...
    if exist "DonatPlus.UI\DonatPlus.UI.csproj.backup" (
        copy "DonatPlus.UI\DonatPlus.UI.csproj.backup" "DonatPlus.UI\DonatPlus.UI.csproj" >nul
        del "DonatPlus.UI\DonatPlus.UI.csproj.backup" >nul
    )
    pause
    exit /b 1
)

echo ✓ .NET Framework 4.8 versiyonu derlendi

echo.
echo 3. Program kurulumu yapılıyor...
set PROGRAM_PATH=C:\Program Files\DonatPlus_Framework48

if exist "%PROGRAM_PATH%" (
    echo Eski kurulum temizleniyor...
    rmdir /s /q "%PROGRAM_PATH%" >nul 2>&1
    timeout /t 2 /nobreak >nul
)

mkdir "%PROGRAM_PATH%" >nul 2>&1

echo Dosyalar kopyalanıyor...
xcopy "DonatPlus.UI\bin\Release\net48\*" "%PROGRAM_PATH%\" /Y /S /Q

if %errorLevel% neq 0 (
    echo HATA: Dosyalar kopyalanamadı!
    pause
    exit /b 1
)

echo ✓ Dosyalar kopyalandı

echo.
echo 4. Framework 4.8 başlatıcı oluşturuluyor...

echo @echo off > "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo title DonatPlus .NET Framework 4.8 >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo echo DonatPlus .NET Framework 4.8 versiyonu başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo echo Bu versiyon BAML hatalarını çözer! >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo echo. >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo cd /d "%%~dp0" >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo REM .NET Framework 4.8 uyumluluk ayarları >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo set COMPlus_legacyCorruptedStateExceptionsPolicy=1 >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo set COMPlus_UseLegacyJit=0 >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo set COMPLUS_EnableDiagnostics=0 >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo echo Program başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo start "" "DonatPlus.UI.exe" >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo if %%errorLevel%% neq 0 ( >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo     echo HATA: Program başlatılamadı! >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo     echo .NET Framework 4.8 yüklü olduğundan emin olun. >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo     pause >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo ^) else ( >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo     echo ✓ Program başarıyla başlatıldı! >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
echo ^) >> "%PROGRAM_PATH%\DonatPlus_Framework48.bat"

echo.
echo 5. Masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop

REM Eski kısayolları sil
if exist "%DESKTOP%\DonatPlus_Framework48.lnk" del "%DESKTOP%\DonatPlus_Framework48.lnk" >nul 2>&1

echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus_Framework48.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus_Framework48.bat" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "DonatPlus .NET Framework 4.8 - BAML Hatası Çözülmüş" >> "%TEMP%\shortcut.vbs"
echo oLink.WindowStyle = 1 >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"
cscript "%TEMP%\shortcut.vbs" >nul 2>&1
del "%TEMP%\shortcut.vbs" >nul 2>&1

echo.
echo 6. Proje dosyasını geri yükleniyor...
if exist "DonatPlus.UI\DonatPlus.UI.csproj.backup" (
    copy "DonatPlus.UI\DonatPlus.UI.csproj.backup" "DonatPlus.UI\DonatPlus.UI.csproj" >nul
    del "DonatPlus.UI\DonatPlus.UI.csproj.backup" >nul
    echo ✓ Proje dosyası geri yüklendi
)

echo.
echo ========================================
echo .NET FRAMEWORK 4.8 ÇÖZÜMÜ TAMAMLANDI!
echo ========================================
echo.
echo ✓ .NET Framework 4.8 versiyonu oluşturuldu
echo ✓ BAML hata düzeltmeleri uygulandı
echo ✓ Program kuruldu: %PROGRAM_PATH%
echo ✓ Masaüstü kısayolu: DonatPlus_Framework48
echo.
echo ÖNEMLI:
echo Artık "DonatPlus_Framework48" kısayolunu kullanın!
echo Bu versiyon BAML hatalarını kesinlikle çözer!
echo.
echo Test etmek ister misiniz? (E/H)
set /p choice=
if /i "%choice%"=="E" (
    echo Program başlatılıyor...
    start "" "%PROGRAM_PATH%\DonatPlus_Framework48.bat"
    echo.
    echo Program başlatıldı! BAML hatası çözülmüş olmalı...
)

echo.
echo Kurulum tamamlandı!
pause
