ci:
  autoupdate_commit_msg: "chore: update pre-commit hooks"

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: "v5.0.0"
    hooks:
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: mixed-line-ending
      - id: detect-private-key
      - id: check-ast
      - id: trailing-whitespace

  - repo: https://github.com/crate-ci/typos
    rev: v1
    hooks:
      - id: typos
        files: \.(py|md|rst|yaml|toml)
        exclude: pyproject.toml

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.11
    hooks:
      # Run the linter.
      - id: ruff
        args: ["--fix"]
      # Run the formatter.
      - id: ruff-format

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: "v4.0.0-alpha.8" # Use the sha or tag you want to point at
    hooks:
      - id: prettier
        types_or: ["javascript", "css"]

exclude: "^tests/test_data/|^tools/.*/.*.py"