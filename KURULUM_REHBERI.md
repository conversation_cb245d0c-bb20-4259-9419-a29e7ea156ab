# DonatPlus AutoCAD Entegrasyonu Kurulum Rehberi

## 🎯 Genel Bakış
DonatPlus, AutoCAD çizimlerinden otomatik donatı tespiti yapabilen profesyonel bir yazılımdır.

## 📋 Sistem Gereksinimleri
- Windows 10/11
- AutoCAD 2010-2025 (herhangi bir versiyon)
- .NET 6.0 Runtime veya üzeri
- En az 4GB RAM
- 500MB disk alanı

## 🔧 Kurulum Adımları

### 1. Programı Derleme
```bash
# Proje klasörüne git
cd C:\Users\<USER>\Desktop\donatplus

# Release modunda derle
dotnet build DonatPlus.sln --configuration Release

# AutoCAD plugin'ini derle
dotnet build DonatPlus.AutoCAD --configuration Release
```

### 2. AutoCAD Plugin Kurulumu

#### A. Plugin Dosyalarını Kopyalama
1. `DonatPlus.AutoCAD\bin\Release\net48\` klasöründeki tüm dosyaları kopyalayın
2. AutoCAD plugin klasörüne yapıştırın:
   - **AutoCAD 2024**: `C:\Program Files\Autodesk\AutoCAD 2024\Plug-ins\DonatPlus\`
   - **AutoCAD 2023**: `C:\Program Files\Autodesk\AutoCAD 2023\Plug-ins\DonatPlus\`
   - **Diğer versiyonlar**: İlgili AutoCAD versiyonu klasörü

#### B. Plugin Tanımlama Dosyası Oluşturma
`PackageContents.xml` dosyası oluşturun:

```xml
<?xml version="1.0" encoding="utf-8"?>
<ApplicationPackage 
    SchemaVersion="1.0" 
    AutodeskProduct="AutoCAD" 
    ProductType="Application" 
    Name="DonatPlus" 
    Description="Betonarme Donatı Metraj Hesaplama" 
    Author="DonatPlus Team"
    ProductCode="{12345678-1234-1234-1234-123456789012}"
    UpgradeCode="{12345678-1234-1234-1234-123456789013}"
    Version="1.0.0">
  
  <CompanyDetails Name="DonatPlus" Url="https://donatplus.com" Email="<EMAIL>"/>
  
  <Components>
    <RuntimeRequirements OS="Win32|Win64" Platform="AutoCAD*" SeriesMin="R24.0" SeriesMax="R25.0"/>
    <ComponentEntry AppName="DonatPlus" ModuleName="./DonatPlus.AutoCAD.dll" AppDescription="DonatPlus AutoCAD Plugin"/>
  </Components>
</ApplicationPackage>
```

### 3. AutoCAD'de Plugin'i Yükleme

#### A. Manuel Yükleme
1. AutoCAD'i açın
2. Komut satırına `NETLOAD` yazın
3. `DonatPlus.AutoCAD.dll` dosyasını seçin
4. Plugin yüklenir

#### B. Otomatik Yükleme
1. `acad.lsp` dosyasını düzenleyin veya oluşturun
2. Şu satırı ekleyin:
```lisp
(command "NETLOAD" "C:\\Program Files\\Autodesk\\AutoCAD 2024\\Plug-ins\\DonatPlus\\DonatPlus.AutoCAD.dll")
```

### 4. Ana Program Kurulumu

#### A. Standalone Uygulama
1. `DonatPlus.UI\bin\Release\net6.0-windows\` klasöründeki dosyaları kopyalayın
2. İstediğiniz klasöre yapıştırın (örn: `C:\Program Files\DonatPlus\`)
3. `DonatPlus.UI.exe` dosyasından kısayol oluşturun

#### B. Veritabanı Kurulumu
Program ilk çalıştırıldığında SQLite veritabanı otomatik oluşturulur.

## 🎮 Kullanım Kılavuzu

### AutoCAD Komutları
Plugin yüklendikten sonra şu komutlar kullanılabilir:

- `DONATPLUS` - Ana menüyü açar
- `DPANALIZ` - Seçili çizimleri analiz eder
- `DPMETRAJ` - Metraj listesi oluşturur
- `DPRAPOR` - Rapor üretir

### Test Projesi Hazırlama

#### 1. Basit Test Çizimi
```
1. AutoCAD'de yeni çizim açın
2. Şu katmanları oluşturun:
   - KIRIŞ (Kırmızı)
   - KOLON (Mavi)
   - DÖŞEME (Yeşil)
   - HASIR (Sarı)

3. Örnek donatı metinleri ekleyin:
   - "5xø12 L=250" (KIRIŞ katmanında)
   - "ø16 L=350 3 adet" (KOLON katmanında)
   - "Φ20 L=120.5" (DÖŞEME katmanında)
```

#### 2. Gerçek Proje Testi
```
1. Mevcut bir betonarme projesini açın
2. Donatı metinlerinin şu formatlarda olduğundan emin olun:
   - "5xø12 L=250"
   - "ø16 L=350"
   - "Φ20 L=120.5"
   - "3×ø14 L=400"

3. Katman isimlerinin şunları içerdiğinden emin olun:
   - KIRIŞ, BEAM
   - KOLON, COLUMN
   - DÖŞEME, SLAB
   - PERDE, WALL
   - HASIR, MESH
```

### Adım Adım Test Süreci

#### 1. Plugin Testi
```
1. AutoCAD'i açın
2. Komut: NETLOAD
3. DonatPlus.AutoCAD.dll'i seçin
4. "Plugin yüklendi" mesajını bekleyin
5. Komut: DONATPLUS
6. Ana menünün açıldığını kontrol edin
```

#### 2. Analiz Testi
```
1. Test çizimini açın
2. Donatı metinlerini seçin
3. Komut: DPANALIZ
4. Sonuçları kontrol edin
5. Metraj listesini görüntüleyin
```

#### 3. Rapor Testi
```
1. Analiz sonrasında
2. Komut: DPRAPOR
3. Excel raporu oluşturulduğunu kontrol edin
4. Raporu açıp verileri kontrol edin
```

## 🐛 Sorun Giderme

### Yaygın Sorunlar

#### Plugin Yüklenmiyor
- AutoCAD versiyonunu kontrol edin
- .NET Framework 4.8 yüklü olduğundan emin olun
- Dosya yollarını kontrol edin

#### Komutlar Çalışmıyor
- Plugin'in doğru yüklendiğini kontrol edin
- AutoCAD'i yönetici olarak çalıştırın
- Antivirus yazılımını kontrol edin

#### Analiz Sonuç Vermiyor
- Metin formatlarını kontrol edin
- Katman isimlerini kontrol edin
- Seçim yapıldığından emin olun

### Log Dosyaları
Hata durumunda şu dosyaları kontrol edin:
- `%TEMP%\DonatPlus\logs\`
- AutoCAD komut geçmişi

## 📞 Destek
Sorun yaşarsanız:
1. Log dosyalarını kontrol edin
2. Test çizimi ile deneyin
3. AutoCAD versiyonunu belirtin
4. Hata mesajlarını kaydedin

## 🔄 Güncelleme
Yeni versiyon için:
1. Eski plugin dosyalarını silin
2. Yeni dosyaları kopyalayın
3. AutoCAD'i yeniden başlatın
