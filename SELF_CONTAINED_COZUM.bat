@echo off
echo ========================================
echo DonatPlus Self-Contained Çözüm
echo ========================================
echo.
echo Bu çözüm .NET runtime gerektirmez!
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

echo.
echo Mevcut dizin: %CD%

if not exist "DonatPlus.UI\DonatPlus.UI.csproj" (
    echo HATA: DonatPlus.UI projesi bulunamadı!
    echo Bu script'i donatplus klasöründe çalıştırın.
    pause
    exit /b 1
)

echo.
echo 1. Self-contained deployment oluşturuluyor...
echo Bu işlem biraz zaman alabilir...

dotnet publish DonatPlus.UI\DonatPlus.UI.csproj -c Release -f net6.0-windows --self-contained true -r win-x64 -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true --output "self_contained_output"

if %errorLevel% neq 0 (
    echo HATA: Self-contained deployment oluşturulamadı!
    echo.
    echo Alternatif deneniyor...
    dotnet publish DonatPlus.UI\DonatPlus.UI.csproj -c Release -f net6.0-windows --self-contained true -r win-x64 --output "self_contained_output"
    
    if %errorLevel% neq 0 (
        echo HATA: Alternatif de başarısız!
        pause
        exit /b 1
    )
)

echo ✓ Self-contained deployment oluşturuldu

echo.
echo 2. Program kurulumu yapılıyor...
set PROGRAM_PATH=C:\Program Files\DonatPlus_SelfContained

if exist "%PROGRAM_PATH%" (
    echo Eski kurulum temizleniyor...
    rmdir /s /q "%PROGRAM_PATH%" >nul 2>&1
    timeout /t 2 /nobreak >nul
)

mkdir "%PROGRAM_PATH%" >nul 2>&1

echo Dosyalar kopyalanıyor...
xcopy "self_contained_output\*" "%PROGRAM_PATH%\" /Y /S /Q

if %errorLevel% neq 0 (
    echo HATA: Dosyalar kopyalanamadı!
    pause
    exit /b 1
)

echo ✓ Dosyalar kopyalandı

echo.
echo 3. Başlatıcı oluşturuluyor...

echo @echo off > "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo title DonatPlus Self-Contained >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo DonatPlus başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo Bu versiyon .NET runtime gerektirmez! >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo cd /d "%%~dp0" >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo Program başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo start "" "DonatPlus.UI.exe" >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo if %%errorLevel%% neq 0 ( >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo HATA: Program başlatılamadı! >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo Bu self-contained versiyon .NET gerektirmez. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo Antivirus yazılımını kontrol edin. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     pause >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo ^) >> "%PROGRAM_PATH%\DonatPlus_Start.bat"

echo.
echo 4. Masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop

REM Eski kısayolu sil
if exist "%DESKTOP%\DonatPlus.lnk" del "%DESKTOP%\DonatPlus.lnk" >nul 2>&1
if exist "%DESKTOP%\DonatPlus_SelfContained.lnk" del "%DESKTOP%\DonatPlus_SelfContained.lnk" >nul 2>&1

echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus_SelfContained.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus_Start.bat" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "DonatPlus Self-Contained - .NET Runtime Gerektirmez" >> "%TEMP%\shortcut.vbs"
echo oLink.WindowStyle = 1 >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"
cscript "%TEMP%\shortcut.vbs" >nul 2>&1
del "%TEMP%\shortcut.vbs" >nul 2>&1

echo.
echo 5. Temizlik yapılıyor...
if exist "self_contained_output" rmdir /s /q "self_contained_output" >nul 2>&1

echo.
echo ========================================
echo SELF-CONTAINED KURULUM TAMAMLANDI!
echo ========================================
echo.
echo ✓ Self-contained deployment oluşturuldu
echo ✓ Program kuruldu: %PROGRAM_PATH%
echo ✓ Masaüstü kısayolu oluşturuldu
echo.
echo ÖNEMLI:
echo Bu versiyon .NET runtime gerektirmez!
echo Masaüstündeki "DonatPlus_SelfContained" kısayolunu kullanın!
echo.
echo Test etmek ister misiniz? (E/H)
set /p choice=
if /i "%choice%"=="E" (
    echo Program başlatılıyor...
    start "" "%PROGRAM_PATH%\DonatPlus_Start.bat"
)

echo.
pause
