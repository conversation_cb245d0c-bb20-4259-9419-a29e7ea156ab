.mac-window-top-bar {
  background-color: #f0f0f0;
  background-image: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
  border-top-left-radius: 0.5em;
  border-top-right-radius: 0.5em;
  height: 2em;
  position: relative;
  display: flex;
  align-items: center;
}

.mac-window-top-bar.dark {
  background-color: #f0f0f0;
  background-image: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.6),
    rgba(0, 0, 0, 0.9)
  );
  /* messageStyle={{
    color: "white",
    fontSize: "smaller",
    marginBottom: "0.1em",
  }} */
}

.label {
  align-items: center;
  display: flex;
  margin: 1em 1em 1em 0.5em;

  img {
    height: 1em;
    margin-right: 0.4em;
  }

  span {
    color: #888;
    font-size: smaller;
  }
}

.dark .label span {
  color: #eae5e5;
}
