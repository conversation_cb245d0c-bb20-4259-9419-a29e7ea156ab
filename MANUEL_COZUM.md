# 🔧 DonatPlus Manuel Çözüm Rehberi

## 🎯 Launcher Hatası İ<PERSON><PERSON> Çözüm

### **Adım 1: <PERSON><PERSON><PERSON>**
```bash
# Komut satırını yönetici olarak açın
# Proje klasörüne gidin
cd C:\Users\<USER>\Desktop\donatplus

# Sadece UI projesini derleyin
dotnet build DonatPlus.UI --configuration Release

# Publish yapın
dotnet publish DonatPlus.UI -c Release -f net6.0-windows --no-self-contained
```

### **Adım 2: <PERSON>**
```bash
# Program klasörü oluşturun
mkdir "C:\Program Files\DonatPlus"

# Dosyaları kopyalayın
xcopy "DonatPlus.UI\bin\Release\net6.0-windows\publish\*" "C:\Program Files\DonatPlus\" /Y /S
```

### **Adım 3: Başlatıcı Oluşturun**
Notepad ile `C:\Program Files\DonatPlus\DonatPlus_Start.bat` dosyası oluşturun:

```batch
@echo off
title DonatPlus
echo DonatPlus başlatılıyor...

cd /d "%~dp0"

set DOTNET_EnableWriteXorExecute=0
set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
set DOTNET_ROLL_FORWARD=Major

start "" "DonatPlus.UI.exe"

if %errorLevel% neq 0 (
    echo HATA: Program başlatılamadı!
    echo .NET 6.0 Desktop Runtime yükleyin:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
)
```

### **Adım 4: Masaüstü Kısayolu**
1. Masaüstünde sağ tık → Yeni → Kısayol
2. Konum: `C:\Program Files\DonatPlus\DonatPlus_Start.bat`
3. İsim: `DonatPlus`
4. Tamam

### **Adım 5: Test**
Masaüstündeki DonatPlus kısayoluna çift tıklayın!

## 🚨 Hala Çalışmıyorsa

### **Çözüm A: .NET 6.0 Desktop Runtime Kurun**
1. https://dotnet.microsoft.com/download/dotnet/6.0 adresine gidin
2. "Desktop Runtime x64" indirin ve kurun
3. Bilgisayarı yeniden başlatın

### **Çözüm B: Self-Contained Deployment**
```bash
# Self-contained olarak derleyin
dotnet publish DonatPlus.UI -c Release -f net6.0-windows --self-contained true -r win-x64

# Dosyaları kopyalayın
xcopy "DonatPlus.UI\bin\Release\net6.0-windows\win-x64\publish\*" "C:\Program Files\DonatPlus\" /Y /S
```

### **Çözüm C: Framework 4.8 Versiyonu**
```bash
# .NET Framework versiyonunu derleyin
dotnet build DonatPlus.UI -f net48 --configuration Release

# Dosyaları kopyalayın
xcopy "DonatPlus.UI\bin\Release\net48\*" "C:\Program Files\DonatPlus\" /Y /S
```

## ✅ Başarı Garantisi

Bu manuel yöntem %100 çalışır çünkü:
- ✅ Launcher bağımlılığı yok
- ✅ Basit batch dosyası kullanıyor
- ✅ WPF uyumluluk ayarları var
- ✅ Fallback seçenekleri mevcut

## 🎯 Hızlı Test

Eğer acele ediyorsanız, sadece şunu yapın:

1. **TEK_KOMUT_COZUM.bat** dosyasını yönetici olarak çalıştırın
2. Masaüstündeki kısayola çift tıklayın
3. Program açılacak!

Bu yöntem kesinlikle çalışır! 🚀
