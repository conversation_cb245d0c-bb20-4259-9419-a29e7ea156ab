@echo off
echo ========================================
echo DonatPlus Süper Basit Çözüm
echo ========================================
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    echo Sağ tık yapıp "Yönetici olarak çalıştır" seçin.
    pause
    exit /b 1
)

echo.
echo Mevcut dizin: %CD%
echo.

REM Proje dosyası kontrolü
if not exist "DonatPlus.UI\DonatPlus.UI.csproj" (
    echo HATA: DonatPlus.UI projesi bulunamadı!
    echo.
    echo Bu script'i şu klasörde çalıştırın:
    echo C:\Users\<USER>\Desktop\donatplus
    echo.
    echo Şu anda bulunduğunuz yer: %CD%
    echo.
    pause
    exit /b 1
)

echo ✓ Proje dosyası bulundu: DonatPlus.UI\DonatPlus.UI.csproj
echo.

echo 1. Proje temizleniyor...
dotnet clean DonatPlus.UI\DonatPlus.UI.csproj >nul 2>&1
echo ✓ Temizleme tamamlandı

echo.
echo 2. Bağımlılıklar geri yükleniyor...
dotnet restore DonatPlus.UI\DonatPlus.UI.csproj
if %errorLevel% neq 0 (
    echo HATA: Restore başarısız!
    pause
    exit /b 1
)
echo ✓ Restore tamamlandı

echo.
echo 3. Proje derleniyor...
dotnet build DonatPlus.UI\DonatPlus.UI.csproj --configuration Release --no-restore
if %errorLevel% neq 0 (
    echo HATA: Derleme başarısız!
    echo.
    echo Detaylı hata görmek için şu komutu çalıştırın:
    echo dotnet build DonatPlus.UI\DonatPlus.UI.csproj --configuration Release --verbosity detailed
    echo.
    pause
    exit /b 1
)
echo ✓ Derleme tamamlandı

echo.
echo 4. Publish yapılıyor...
dotnet publish DonatPlus.UI\DonatPlus.UI.csproj --configuration Release --framework net6.0-windows --no-self-contained --output "publish_output"
if %errorLevel% neq 0 (
    echo HATA: Publish başarısız!
    echo.
    echo Detaylı hata görmek için şu komutu çalıştırın:
    echo dotnet publish DonatPlus.UI\DonatPlus.UI.csproj --configuration Release --framework net6.0-windows --verbosity detailed
    echo.
    pause
    exit /b 1
)
echo ✓ Publish tamamlandı

echo.
echo 5. Program klasörü hazırlanıyor...
set PROGRAM_PATH=C:\Program Files\DonatPlus

if exist "%PROGRAM_PATH%" (
    echo Eski kurulum temizleniyor...
    rmdir /s /q "%PROGRAM_PATH%" >nul 2>&1
    timeout /t 2 /nobreak >nul
)

mkdir "%PROGRAM_PATH%" >nul 2>&1
echo ✓ Program klasörü oluşturuldu

echo.
echo 6. Dosyalar kopyalanıyor...
xcopy "publish_output\*" "%PROGRAM_PATH%\" /Y /S /Q
if %errorLevel% neq 0 (
    echo HATA: Dosyalar kopyalanamadı!
    echo Hedef klasör: %PROGRAM_PATH%
    pause
    exit /b 1
)
echo ✓ Dosyalar kopyalandı

echo.
echo 7. Başlatıcı oluşturuluyor...

REM Basit batch başlatıcı
echo @echo off > "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo title DonatPlus >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo DonatPlus başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo cd /d "%%~dp0" >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo REM WPF uyumluluk ayarları >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo set DOTNET_EnableWriteXorExecute=0 >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo set DOTNET_ROLL_FORWARD=Major >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo echo Program başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo start "" "DonatPlus.UI.exe" >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo if %%errorLevel%% neq 0 ( >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo HATA: Program başlatılamadı! >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo Çözüm önerileri: >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo 1. .NET 6.0 Desktop Runtime yükleyin >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo    https://dotnet.microsoft.com/download/dotnet/6.0 >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo 2. Windows Update yapın >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo 3. Antivirus yazılımını kontrol edin >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     echo. >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo     pause >> "%PROGRAM_PATH%\DonatPlus_Start.bat"
echo ^) >> "%PROGRAM_PATH%\DonatPlus_Start.bat"

echo ✓ Başlatıcı oluşturuldu

echo.
echo 8. Masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop

REM Eski kısayolu sil
if exist "%DESKTOP%\DonatPlus.lnk" del "%DESKTOP%\DonatPlus.lnk" >nul 2>&1

REM Yeni kısayol oluştur
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus_Start.bat" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "DonatPlus - Betonarme Donatı Metraj Hesaplama" >> "%TEMP%\shortcut.vbs"
echo oLink.WindowStyle = 1 >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"
cscript "%TEMP%\shortcut.vbs" >nul 2>&1
del "%TEMP%\shortcut.vbs" >nul 2>&1

echo ✓ Masaüstü kısayolu oluşturuldu

echo.
echo 9. Temizlik yapılıyor...
if exist "publish_output" rmdir /s /q "publish_output" >nul 2>&1
echo ✓ Geçici dosyalar temizlendi

echo.
echo ========================================
echo KURULUM BAŞARIYLA TAMAMLANDI!
echo ========================================
echo.
echo ✓ Proje derlendi
echo ✓ Publish yapıldı
echo ✓ Program kuruldu: %PROGRAM_PATH%
echo ✓ Başlatıcı oluşturuldu
echo ✓ Masaüstü kısayolu oluşturuldu
echo.
echo KULLANIM:
echo Masaüstündeki "DonatPlus" kısayoluna çift tıklayın!
echo.
echo Test etmek ister misiniz? (E/H)
set /p choice=
if /i "%choice%"=="E" (
    echo Program başlatılıyor...
    start "" "%PROGRAM_PATH%\DonatPlus_Start.bat"
    echo Program başlatıldı!
)

echo.
echo Kurulum tamamlandı!
pause
