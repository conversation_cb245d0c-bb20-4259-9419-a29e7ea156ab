# 🚀 DonatPlus Başlangıç Rehberi

## 📋 Hızlı Başlangıç (5 Dakika)

### 1. İlk Test (Programın Çalıştığını Doğrulama)
```
1. HIZLI_TEST.bat dosyasını çift tıklayın
2. Tüm testlerin geçtiğini kontrol edin
3. Ana program açılacak - bu DonatPlus'ın çalıştığını gösterir
```

### 2. AutoCAD ile Test (Eğer AutoCAD varsa)
```
1. AutoCAD'i açın
2. Komut satırına: NETLOAD
3. Do<PERSON>a seç: DonatPlus.AutoCAD\bin\Release\net48\DonatPlus.AutoCAD.dll
4. Komut satırına: DONATPLUS
5. Plugin menüsü açılırsa başarılı!
```

## 🎯 Gerçek Proje ile Test

### Adım 1: Test Çizimi <PERSON>
```
AutoCAD'de yeni çizim açın ve şu metinleri ekleyin:

Katman: KIRIŞ
- "5xø12 L=250"
- "3xø16 L=300"

Katman: KOLON  
- "4xø20 L=350"
- "ø8/15 L=30"

Katman: DÖŞEME
- "ø12/20 L=400"
- "ø10/25 L=350"
```

### Adım 2: Analiz Etme
```
1. DPANALIZ komutunu çalıştırın
2. Tüm donatı metinlerini seçin
3. Enter'a basın
4. Sonuçları kontrol edin
```

### Adım 3: Rapor Alma
```
1. DPRAPOR komutunu çalıştırın
2. Excel raporu oluşturun
3. Metraj listesini kontrol edin
```

## 📊 Beklenen Sonuçlar

### Analiz Sonuçları
```
✓ 5xø12 L=250 → 12.5m, ~11.1kg
✓ 3xø16 L=300 → 9.0m, ~14.2kg  
✓ 4xø20 L=350 → 14.0m, ~34.6kg
✓ Toplam ağırlık: ~60kg
```

### Excel Raporu
```
✓ Çap bazlı gruplandırma
✓ Toplam uzunluk ve ağırlık
✓ Katman bilgileri
✓ Kesim optimizasyonu
```

## 🔧 Sorun Giderme

### Program Açılmıyor
```
Çözüm 1: .NET 6.0 Runtime yükleyin
Çözüm 2: Windows Defender'ı kontrol edin
Çözüm 3: Yönetici olarak çalıştırın
```

### AutoCAD Plugin Yüklenmiyor
```
Çözüm 1: AutoCAD versiyonunu kontrol edin (2010-2025)
Çözüm 2: .NET Framework 4.8 yükleyin
Çözüm 3: Dosya yolunu kontrol edin
```

### Analiz Sonuç Vermiyor
```
Çözüm 1: Metin formatını kontrol edin: "5xø12 L=250"
Çözüm 2: Katman isimlerini kontrol edin: KIRIŞ, KOLON, DÖŞEME
Çözüm 3: Metinlerin seçildiğinden emin olun
```

## 📞 Destek ve İletişim

### Log Dosyaları
Hata durumunda şu dosyaları kontrol edin:
```
- %TEMP%\DonatPlus\logs\
- AutoCAD komut geçmişi
- Windows Event Viewer
```

### Test Dosyaları
```
- TEST_CIZIMI_ORNEGI.md → Detaylı test çizimi
- DonatPlus.TestConsole → Console test uygulaması
- Unit testler → 60 adet otomatik test
```

## 🎓 İleri Seviye Kullanım

### Özel Formatlar
```
Program şu formatları destekler:
- "5xø12 L=250" (standart)
- "ø16 L=350 3 adet" (alternatif)
- "Φ20 L=120.5" (Phi sembolü)
- "8×ø14 L=400" (çarpı sembolü)
```

### Katman Kuralları
```
Otomatik tanıma için katman isimleri:
- KIRIŞ, BEAM → Kiriş
- KOLON, COLUMN → Kolon
- DÖŞEME, SLAB → Döşeme
- PERDE, WALL → Perde
- HASIR, MESH → Hasır
```

### Rapor Seçenekleri
```
- Excel metraj listesi
- PDF raporu (gelecek versiyon)
- CSV export
- Kesim optimizasyonu
```

## 🚀 Başarı İpuçları

### En İyi Pratikler
```
1. Standart metin formatları kullanın
2. Katman isimlerini tutarlı yapın
3. Düzenli backup alın
4. Test çizimleri ile pratik yapın
```

### Performans İpuçları
```
1. Büyük projelerde katman bazlı analiz yapın
2. Gereksiz metinleri temizleyin
3. Düzenli olarak çizimi kaydedin
```

## 🎯 Sonuç

DonatPlus artık kullanıma hazır! Bu rehberi takip ederek:
- ✅ Programın çalıştığını doğruladınız
- ✅ AutoCAD entegrasyonunu test ettiniz  
- ✅ Gerçek proje ile deneme yaptınız
- ✅ Sorun giderme yöntemlerini öğrendiniz

**Başarılı projeler dileriz!** 🎉
