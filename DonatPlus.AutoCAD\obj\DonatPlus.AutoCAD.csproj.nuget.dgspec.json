{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD\\DonatPlus.AutoCAD.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD\\DonatPlus.AutoCAD.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD\\DonatPlus.AutoCAD.csproj", "projectName": "DonatPlus.AutoCAD", "projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD\\DonatPlus.AutoCAD.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net48"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj", "projectName": "DonatPlus.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\DonatPlus.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.Core\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net48", "net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}, "net48": {"targetAlias": "net48", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.4, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}