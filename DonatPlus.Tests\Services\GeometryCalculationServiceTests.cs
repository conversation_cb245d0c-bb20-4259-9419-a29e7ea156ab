using DonatPlus.Core.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace DonatPlus.Tests.Services
{
    public class GeometryCalculationServiceTests
    {
        private readonly GeometryCalculationService _service;
        private readonly Mock<ILogger<GeometryCalculationService>> _mockLogger;

        public GeometryCalculationServiceTests()
        {
            _mockLogger = new Mock<ILogger<GeometryCalculationService>>();
            _service = new GeometryCalculationService(_mockLogger.Object);
        }

        [Theory]
        [InlineData(8, 1.0, 0.395)] // ø8mm, 1m
        [InlineData(12, 1.0, 0.888)] // ø12mm, 1m
        [InlineData(16, 1.0, 1.58)] // ø16mm, 1m
        [InlineData(20, 1.0, 2.47)] // ø20mm, 1m
        public void CalculateRebarWeight_StandardDiameters_ReturnsCorrectWeight(double diameter, double length, double expectedWeight)
        {
            // Act
            var result = _service.CalculateRebarWeight(diameter, length);

            // Assert
            Assert.Equal(expectedWeight, result, 2); // 2 decimal places precision
        }

        [Theory]
        [InlineData(0, 0, 5, 5, 7.071)] // 5 units diagonal
        [InlineData(0, 0, 3, 4, 5.0)] // 3-4-5 triangle
        [InlineData(0, 0, 0, 10, 10.0)] // Vertical line
        [InlineData(0, 0, 10, 0, 10.0)] // Horizontal line
        public void CalculateDistance_VariousPoints_ReturnsCorrectDistance(double x1, double y1, double x2, double y2, double expectedDistance)
        {
            // Act
            var result = _service.CalculateDistance(x1, y1, x2, y2);

            // Assert
            Assert.Equal(expectedDistance, result, 3);
        }

        [Theory]
        [InlineData(90, 50, 12, 0.0654)] // 90 degree bend
        [InlineData(45, 50, 12, 0.0327)] // 45 degree bend
        [InlineData(180, 50, 12, 0.1309)] // 180 degree bend
        public void CalculateBendLength_VariousAngles_ReturnsCorrectLength(double angle, double radius, double diameter, double expectedLength)
        {
            // Act
            var result = _service.CalculateBendLength(angle, radius, diameter);

            // Assert
            Assert.Equal(expectedLength, result, 4);
        }

        [Theory]
        [InlineData(12, "90", 0.144)] // 12d for 90 degree hook
        [InlineData(16, "135", 0.096)] // 6d for 135 degree hook
        [InlineData(20, "180", 0.080)] // 4d for 180 degree hook
        public void CalculateHookLength_VariousHookTypes_ReturnsCorrectLength(double diameter, string hookType, double expectedLength)
        {
            // Act
            var result = _service.CalculateHookLength(diameter, hookType);

            // Assert
            Assert.Equal(expectedLength, result, 3);
        }

        [Theory]
        [InlineData(12, "C25", "S420", 0.48)] // Standard case
        [InlineData(16, "C20", "S420", 0.768)] // Lower concrete class
        [InlineData(12, "C30", "S420", 0.432)] // Higher concrete class
        [InlineData(12, "C25", "S500", 0.576)] // Higher steel grade
        public void CalculateLapLength_VariousParameters_ReturnsCorrectLength(double diameter, string concreteClass, string steelGrade, double expectedLength)
        {
            // Act
            var result = _service.CalculateLapLength(diameter, concreteClass, steelGrade);

            // Assert
            Assert.Equal(expectedLength, result, 3);
        }

        [Fact]
        public void CalculatePolylineLength_ValidPoints_ReturnsCorrectLength()
        {
            // Arrange
            var points = new List<Point2D>
            {
                new Point2D(0, 0),
                new Point2D(1000, 0), // 1m horizontal
                new Point2D(1000, 1000), // 1m vertical
                new Point2D(0, 1000), // 1m horizontal
                new Point2D(0, 0) // 1m vertical (closed)
            };

            // Act
            var result = _service.CalculatePolylineLength(points);

            // Assert
            Assert.Equal(4.0, result, 1); // 4 meters total
        }

        [Fact]
        public void CalculatePolylineLength_EmptyPoints_ReturnsZero()
        {
            // Arrange
            var points = new List<Point2D>();

            // Act
            var result = _service.CalculatePolylineLength(points);

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public void CalculatePolylineLength_SinglePoint_ReturnsZero()
        {
            // Arrange
            var points = new List<Point2D> { new Point2D(0, 0) };

            // Act
            var result = _service.CalculatePolylineLength(points);

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public void CalculatePolygonArea_Rectangle_ReturnsCorrectArea()
        {
            // Arrange - 10x5 rectangle
            var points = new List<Point2D>
            {
                new Point2D(0, 0),
                new Point2D(10, 0),
                new Point2D(10, 5),
                new Point2D(0, 5)
            };

            // Act
            var result = _service.CalculatePolygonArea(points);

            // Assert
            Assert.Equal(50, result);
        }

        [Fact]
        public void CalculatePolygonArea_Triangle_ReturnsCorrectArea()
        {
            // Arrange - Right triangle with base 6 and height 4
            var points = new List<Point2D>
            {
                new Point2D(0, 0),
                new Point2D(6, 0),
                new Point2D(0, 4)
            };

            // Act
            var result = _service.CalculatePolygonArea(points);

            // Assert
            Assert.Equal(12, result); // Area = 0.5 * base * height = 0.5 * 6 * 4 = 12
        }

        [Fact]
        public void OptimizeCutting_ValidRebars_ReturnsOptimizedPlan()
        {
            // Arrange
            var rebars = new List<DonatPlus.Core.Models.RebarElement>
            {
                new() { Diameter = 12, Length = 3.0, Quantity = 5 },
                new() { Diameter = 12, Length = 4.0, Quantity = 3 },
                new() { Diameter = 16, Length = 5.0, Quantity = 2 }
            };

            // Act
            var result = _service.OptimizeCutting(rebars, 12.0);

            // Assert
            Assert.NotEmpty(result);
            Assert.Contains(result, plan => plan.Diameter == 12);
            Assert.Contains(result, plan => plan.Diameter == 16);
            
            // Check that waste is minimized (should be less than 20% for this case)
            foreach (var plan in result)
            {
                Assert.True(plan.WastePercentage < 20, $"Waste percentage {plan.WastePercentage}% is too high");
            }
        }

        [Theory]
        [InlineData(0, 0)] // Zero diameter
        [InlineData(12, 0)] // Zero length
        [InlineData(-12, 5)] // Negative diameter
        [InlineData(12, -5)] // Negative length
        public void CalculateRebarWeight_InvalidInputs_ReturnsZero(double diameter, double length)
        {
            // Act
            var result = _service.CalculateRebarWeight(diameter, length);

            // Assert
            Assert.Equal(0, result);
        }
    }
}
