using DonatPlus.Core.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace DonatPlus.Tests.Services
{
    public class RebarDetectionServiceTests
    {
        private readonly RebarDetectionService _service;
        private readonly Mock<ILogger<RebarDetectionService>> _mockLogger;
        private readonly Mock<GeometryCalculationService> _mockGeometryService;

        public RebarDetectionServiceTests()
        {
            _mockLogger = new Mock<ILogger<RebarDetectionService>>();
            var mockGeometryLogger = new Mock<ILogger<GeometryCalculationService>>();
            _mockGeometryService = new Mock<GeometryCalculationService>(mockGeometryLogger.Object);
            _service = new RebarDetectionService(_mockLogger.Object, _mockGeometryService.Object);
        }

        [Theory]
        [InlineData("ø12", 12)]
        [InlineData("Φ16", 16)]
        [InlineData("φ20", 20)]
        [InlineData("ø 14", 14)]
        [InlineData("Ø25", 25)]
        public void ExtractRebarFromText_ValidDiameterSymbols_ExtractsDiameter(string text, double expectedDiameter)
        {
            // Arrange
            _mockGeometryService.Setup(x => x.CalculateRebarWeight(It.IsAny<double>(), It.IsAny<double>()))
                .Returns(1.0);

            // Act
            var result = _service.ExtractRebarFromText(text, 0, 0, "TEST_LAYER");

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedDiameter, result.Diameter);
        }

        [Theory]
        [InlineData("5xø12", 5, 12)]
        [InlineData("3×Φ16", 3, 16)]
        [InlineData("10 x ø20", 10, 20)]
        [InlineData("2Xφ14", 2, 14)]
        public void ExtractRebarFromText_ValidQuantityAndDiameter_ExtractsBoth(string text, int expectedQuantity, double expectedDiameter)
        {
            // Arrange
            _mockGeometryService.Setup(x => x.CalculateRebarWeight(It.IsAny<double>(), It.IsAny<double>()))
                .Returns(1.0);

            // Act
            var result = _service.ExtractRebarFromText(text, 0, 0, "TEST_LAYER");

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedQuantity, result.Quantity);
            Assert.Equal(expectedDiameter, result.Diameter);
        }

        [Theory]
        [InlineData("L=250", 2.5)]
        [InlineData("L = 350", 3.5)]
        [InlineData("L=120.5", 1.205)]
        public void ExtractRebarFromText_ValidLength_ExtractsLength(string text, double expectedLength)
        {
            // Arrange
            var fullText = $"ø12 {text}";
            _mockGeometryService.Setup(x => x.CalculateRebarWeight(It.IsAny<double>(), It.IsAny<double>()))
                .Returns(1.0);

            // Act
            var result = _service.ExtractRebarFromText(fullText, 0, 0, "TEST_LAYER");

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedLength, result.Length, 3);
        }

        [Theory]
        [InlineData("KIRIŞ", "Kiriş")]
        [InlineData("BEAM", "Kiriş")]
        [InlineData("KOLON", "Kolon")]
        [InlineData("COLUMN", "Kolon")]
        [InlineData("DÖŞEME", "Döşeme")]
        [InlineData("SLAB", "Döşeme")]
        [InlineData("PERDE", "Perde")]
        [InlineData("WALL", "Perde")]
        [InlineData("HASIR", "Hasır")]
        [InlineData("MESH", "Hasır")]
        [InlineData("OTHER", "Diğer")]
        public void ExtractRebarFromText_LayerNames_DeterminesElementType(string layerName, string expectedElementType)
        {
            // Arrange
            _mockGeometryService.Setup(x => x.CalculateRebarWeight(It.IsAny<double>(), It.IsAny<double>()))
                .Returns(1.0);

            // Act
            var result = _service.ExtractRebarFromText("ø12", 0, 0, layerName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedElementType, result.ElementType);
        }

        [Fact]
        public void ExtractRebarFromText_NoValidDiameter_ReturnsNull()
        {
            // Act
            var result = _service.ExtractRebarFromText("Some text without diameter", 0, 0, "TEST_LAYER");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void ExtractRebarFromText_NoValidLength_ReturnsNull()
        {
            // Arrange
            _mockGeometryService.Setup(x => x.CalculateRebarWeight(It.IsAny<double>(), It.IsAny<double>()))
                .Returns(1.0);

            // Act
            var result = _service.ExtractRebarFromText("ø12", 0, 0, "TEST_LAYER");

            // Assert
            Assert.Null(result); // Should return null because length is 0
        }

        [Fact]
        public void EstimateQuantityFromParallelLines_EmptyList_ReturnsOne()
        {
            // Arrange
            var lines = new List<LineSegment>();

            // Act
            var result = _service.EstimateQuantityFromParallelLines(lines);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public void EstimateQuantityFromParallelLines_ParallelLines_ReturnsCorrectCount()
        {
            // Arrange
            var lines = new List<LineSegment>
            {
                new() { StartX = 0, StartY = 0, EndX = 10, EndY = 0 }, // Horizontal line 1
                new() { StartX = 0, StartY = 1, EndX = 10, EndY = 1 }, // Horizontal line 2
                new() { StartX = 0, StartY = 2, EndX = 10, EndY = 2 }, // Horizontal line 3
            };

            // Act
            var result = _service.EstimateQuantityFromParallelLines(lines);

            // Assert
            Assert.Equal(3, result);
        }

        [Fact]
        public void DetectWireMesh_ValidGrid_ReturnsWireMesh()
        {
            // Arrange
            var horizontalLines = new List<LineSegment>
            {
                new() { StartX = 0, StartY = 0, EndX = 1000, EndY = 0, LayerName = "HASIR" },
                new() { StartX = 0, StartY = 150, EndX = 1000, EndY = 150, LayerName = "HASIR" },
                new() { StartX = 0, StartY = 300, EndX = 1000, EndY = 300, LayerName = "HASIR" }
            };

            var verticalLines = new List<LineSegment>
            {
                new() { StartX = 0, StartY = 0, EndX = 0, EndY = 300, LayerName = "HASIR" },
                new() { StartX = 150, StartY = 0, EndX = 150, EndY = 300, LayerName = "HASIR" },
                new() { StartX = 300, StartY = 0, EndX = 300, EndY = 300, LayerName = "HASIR" }
            };

            // Act
            var result = _service.DetectWireMesh(horizontalLines, verticalLines, "HASIR");

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsWireMesh);
            Assert.Equal("Hasır", result.ElementType);
            Assert.True(result.MeshWidth > 0);
            Assert.True(result.MeshHeight > 0);
        }

        [Fact]
        public void DetectWireMesh_InsufficientLines_ReturnsNull()
        {
            // Arrange
            var horizontalLines = new List<LineSegment>
            {
                new() { StartX = 0, StartY = 0, EndX = 1000, EndY = 0, LayerName = "HASIR" }
            };

            var verticalLines = new List<LineSegment>
            {
                new() { StartX = 0, StartY = 0, EndX = 0, EndY = 300, LayerName = "HASIR" }
            };

            // Act
            var result = _service.DetectWireMesh(horizontalLines, verticalLines, "HASIR");

            // Assert
            Assert.Null(result);
        }

        [Theory]
        [InlineData("ø12 L=250 5 adet", 12, 2.5, 5)]
        [InlineData("3xΦ16 L=350", 16, 3.5, 3)]
        [InlineData("ø20", 20, 0, 1)] // No length, should return null
        public void ExtractRebarFromText_ComplexText_ExtractsAllInformation(string text, double expectedDiameter, double expectedLength, int expectedQuantity)
        {
            // Arrange
            _mockGeometryService.Setup(x => x.CalculateRebarWeight(It.IsAny<double>(), It.IsAny<double>()))
                .Returns(1.0);

            // Act
            var result = _service.ExtractRebarFromText(text, 100, 200, "KIRIŞ");

            // Assert
            if (expectedLength > 0)
            {
                Assert.NotNull(result);
                Assert.Equal(expectedDiameter, result.Diameter);
                Assert.Equal(expectedLength, result.Length, 3);
                Assert.Equal(expectedQuantity, result.Quantity);
                Assert.Equal("Kiriş", result.ElementType);
                Assert.Equal(100, result.StartX);
                Assert.Equal(200, result.StartY);
            }
            else
            {
                Assert.Null(result);
            }
        }
    }
}
