# DonatPlus Build Script (PowerShell)
Write-Host "DonatPlus Build Script" -ForegroundColor Green
Write-Host "=====================" -ForegroundColor Green

# Değişkenler
$SolutionFile = "DonatPlus.sln"
$BuildConfig = "Release"
$OutputDir = "bin\Release"
$PublishDir = "publish"

try {
    # Temizlik
    Write-Host "Temizlik yapılıyor..." -ForegroundColor Yellow
    if (Test-Path $OutputDir) { Remove-Item $OutputDir -Recurse -Force }
    if (Test-Path $PublishDir) { Remove-Item $PublishDir -Recurse -Force }

    # Restore
    Write-Host "NuGet paketleri geri yükleniyor..." -ForegroundColor Yellow
    dotnet restore $SolutionFile
    if ($LASTEXITCODE -ne 0) { throw "NuGet restore başarısız!" }

    # Build
    Write-Host "Proje derleniyor..." -ForegroundColor Yellow
    dotnet build $SolutionFile --configuration $BuildConfig --no-restore
    if ($LASTEXITCODE -ne 0) { throw "Build başarısız!" }

    # Test
    Write-Host "Testler çalıştırılıyor..." -ForegroundColor Yellow
    dotnet test $SolutionFile --configuration $BuildConfig --no-build --verbosity normal
    if ($LASTEXITCODE -ne 0) { Write-Warning "Bazı testler başarısız!" }

    # Publish UI
    Write-Host "UI projesi publish ediliyor..." -ForegroundColor Yellow
    dotnet publish "DonatPlus.UI\DonatPlus.UI.csproj" --configuration $BuildConfig --output "$PublishDir\UI" --self-contained false --runtime win-x64
    if ($LASTEXITCODE -ne 0) { throw "UI publish başarısız!" }

    # Publish AutoCAD Plugin
    Write-Host "AutoCAD plugin publish ediliyor..." -ForegroundColor Yellow
    dotnet publish "DonatPlus.AutoCAD\DonatPlus.AutoCAD.csproj" --configuration $BuildConfig --output "$PublishDir\AutoCAD" --framework net48 --no-restore
    if ($LASTEXITCODE -ne 0) { throw "AutoCAD plugin publish başarısız!" }

    # Dosyaları kopyala
    Write-Host "Ek dosyalar kopyalanıyor..." -ForegroundColor Yellow
    Copy-Item "README.md" $PublishDir -ErrorAction SilentlyContinue
    Copy-Item "LICENSE" $PublishDir -ErrorAction SilentlyContinue

    Write-Host ""
    Write-Host "=====================" -ForegroundColor Green
    Write-Host "Build başarıyla tamamlandı!" -ForegroundColor Green
    Write-Host "=====================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Çıktı klasörü: $PublishDir" -ForegroundColor Cyan
}
catch {
    Write-Host "HATA: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Devam etmek için bir tuşa basın..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
