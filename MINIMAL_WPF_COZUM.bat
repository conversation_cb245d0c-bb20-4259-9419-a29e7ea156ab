@echo off
echo ========================================
echo DonatPlus Minimal WPF Çözümü
echo ========================================
echo.
echo BAML hatası için minimal WPF versiyonu oluşturuluyor...
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

echo.
echo 1. Minimal WPF uygulaması oluşturuluyor...

REM Minimal WPF klasörü oluştur
if exist "DonatPlus.Minimal" rmdir /s /q "DonatPlus.Minimal" >nul 2>&1
mkdir "DonatPlus.Minimal"

echo.
echo 2. Minimal proje dosyası oluşturuluyor...

REM Minimal csproj oluştur
echo ^<Project Sdk="Microsoft.NET.Sdk"^> > "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo   ^<PropertyGroup^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo     ^<OutputType^>WinExe^</OutputType^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo     ^<TargetFramework^>net48^</TargetFramework^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo     ^<UseWPF^>true^</UseWPF^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo     ^<UseWindowsForms^>true^</UseWindowsForms^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo   ^</PropertyGroup^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo   ^<ItemGroup^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo     ^<ProjectReference Include="..\DonatPlus.Core\DonatPlus.Core.csproj" /^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo   ^</ItemGroup^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"
echo ^</Project^> >> "DonatPlus.Minimal\DonatPlus.Minimal.csproj"

echo.
echo 3. Minimal Program.cs oluşturuluyor...

REM Minimal Program.cs oluştur
echo using System; > "DonatPlus.Minimal\Program.cs"
echo using System.Windows; >> "DonatPlus.Minimal\Program.cs"
echo using System.Windows.Forms; >> "DonatPlus.Minimal\Program.cs"
echo using DonatPlus.Core.Services; >> "DonatPlus.Minimal\Program.cs"
echo using Microsoft.Extensions.DependencyInjection; >> "DonatPlus.Minimal\Program.cs"
echo using Microsoft.Extensions.Logging; >> "DonatPlus.Minimal\Program.cs"
echo. >> "DonatPlus.Minimal\Program.cs"
echo namespace DonatPlus.Minimal >> "DonatPlus.Minimal\Program.cs"
echo { >> "DonatPlus.Minimal\Program.cs"
echo     class Program >> "DonatPlus.Minimal\Program.cs"
echo     { >> "DonatPlus.Minimal\Program.cs"
echo         [STAThread] >> "DonatPlus.Minimal\Program.cs"
echo         static void Main(string[] args) >> "DonatPlus.Minimal\Program.cs"
echo         { >> "DonatPlus.Minimal\Program.cs"
echo             try >> "DonatPlus.Minimal\Program.cs"
echo             { >> "DonatPlus.Minimal\Program.cs"
echo                 // WinForms kullanarak basit arayüz >> "DonatPlus.Minimal\Program.cs"
echo                 System.Windows.Forms.Application.EnableVisualStyles(); >> "DonatPlus.Minimal\Program.cs"
echo                 System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(false); >> "DonatPlus.Minimal\Program.cs"
echo. >> "DonatPlus.Minimal\Program.cs"
echo                 // Services >> "DonatPlus.Minimal\Program.cs"
echo                 var services = new ServiceCollection(); >> "DonatPlus.Minimal\Program.cs"
echo                 services.AddLogging(builder =^> builder.AddConsole()); >> "DonatPlus.Minimal\Program.cs"
echo                 services.AddTransient^<GeometryCalculationService^>(); >> "DonatPlus.Minimal\Program.cs"
echo                 services.AddTransient^<RebarDetectionService^>(); >> "DonatPlus.Minimal\Program.cs"
echo                 var serviceProvider = services.BuildServiceProvider(); >> "DonatPlus.Minimal\Program.cs"
echo. >> "DonatPlus.Minimal\Program.cs"
echo                 // Ana form >> "DonatPlus.Minimal\Program.cs"
echo                 var mainForm = new MainForm(serviceProvider); >> "DonatPlus.Minimal\Program.cs"
echo                 System.Windows.Forms.Application.Run(mainForm); >> "DonatPlus.Minimal\Program.cs"
echo             } >> "DonatPlus.Minimal\Program.cs"
echo             catch (Exception ex) >> "DonatPlus.Minimal\Program.cs"
echo             { >> "DonatPlus.Minimal\Program.cs"
echo                 MessageBox.Show($"Program hatası: {ex.Message}", "DonatPlus Hata", MessageBoxButtons.OK, MessageBoxIcon.Error); >> "DonatPlus.Minimal\Program.cs"
echo             } >> "DonatPlus.Minimal\Program.cs"
echo         } >> "DonatPlus.Minimal\Program.cs"
echo     } >> "DonatPlus.Minimal\Program.cs"
echo } >> "DonatPlus.Minimal\Program.cs"

echo.
echo 4. Minimal MainForm.cs oluşturuluyor...

REM Minimal MainForm.cs oluştur
echo using System; > "DonatPlus.Minimal\MainForm.cs"
echo using System.Collections.Generic; >> "DonatPlus.Minimal\MainForm.cs"
echo using System.Windows.Forms; >> "DonatPlus.Minimal\MainForm.cs"
echo using DonatPlus.Core.Models; >> "DonatPlus.Minimal\MainForm.cs"
echo using DonatPlus.Core.Services; >> "DonatPlus.Minimal\MainForm.cs"
echo using Microsoft.Extensions.DependencyInjection; >> "DonatPlus.Minimal\MainForm.cs"
echo. >> "DonatPlus.Minimal\MainForm.cs"
echo namespace DonatPlus.Minimal >> "DonatPlus.Minimal\MainForm.cs"
echo { >> "DonatPlus.Minimal\MainForm.cs"
echo     public partial class MainForm : Form >> "DonatPlus.Minimal\MainForm.cs"
echo     { >> "DonatPlus.Minimal\MainForm.cs"
echo         private readonly GeometryCalculationService _geometryService; >> "DonatPlus.Minimal\MainForm.cs"
echo         private readonly RebarDetectionService _detectionService; >> "DonatPlus.Minimal\MainForm.cs"
echo. >> "DonatPlus.Minimal\MainForm.cs"
echo         public MainForm(IServiceProvider serviceProvider) >> "DonatPlus.Minimal\MainForm.cs"
echo         { >> "DonatPlus.Minimal\MainForm.cs"
echo             _geometryService = serviceProvider.GetRequiredService^<GeometryCalculationService^>(); >> "DonatPlus.Minimal\MainForm.cs"
echo             _detectionService = serviceProvider.GetRequiredService^<RebarDetectionService^>(); >> "DonatPlus.Minimal\MainForm.cs"
echo             InitializeComponent(); >> "DonatPlus.Minimal\MainForm.cs"
echo         } >> "DonatPlus.Minimal\MainForm.cs"
echo. >> "DonatPlus.Minimal\MainForm.cs"
echo         private void InitializeComponent() >> "DonatPlus.Minimal\MainForm.cs"
echo         { >> "DonatPlus.Minimal\MainForm.cs"
echo             this.Text = "DonatPlus - Minimal Version"; >> "DonatPlus.Minimal\MainForm.cs"
echo             this.Size = new System.Drawing.Size(800, 600); >> "DonatPlus.Minimal\MainForm.cs"
echo             this.StartPosition = FormStartPosition.CenterScreen; >> "DonatPlus.Minimal\MainForm.cs"
echo. >> "DonatPlus.Minimal\MainForm.cs"
echo             var label = new Label(); >> "DonatPlus.Minimal\MainForm.cs"
echo             label.Text = "DonatPlus Minimal - BAML Hatası Çözülmüş!"; >> "DonatPlus.Minimal\MainForm.cs"
echo             label.Location = new System.Drawing.Point(50, 50); >> "DonatPlus.Minimal\MainForm.cs"
echo             label.Size = new System.Drawing.Size(400, 30); >> "DonatPlus.Minimal\MainForm.cs"
echo             this.Controls.Add(label); >> "DonatPlus.Minimal\MainForm.cs"
echo. >> "DonatPlus.Minimal\MainForm.cs"
echo             var testButton = new Button(); >> "DonatPlus.Minimal\MainForm.cs"
echo             testButton.Text = "Core Fonksiyonları Test Et"; >> "DonatPlus.Minimal\MainForm.cs"
echo             testButton.Location = new System.Drawing.Point(50, 100); >> "DonatPlus.Minimal\MainForm.cs"
echo             testButton.Size = new System.Drawing.Size(200, 40); >> "DonatPlus.Minimal\MainForm.cs"
echo             testButton.Click += TestButton_Click; >> "DonatPlus.Minimal\MainForm.cs"
echo             this.Controls.Add(testButton); >> "DonatPlus.Minimal\MainForm.cs"
echo         } >> "DonatPlus.Minimal\MainForm.cs"
echo. >> "DonatPlus.Minimal\MainForm.cs"
echo         private void TestButton_Click(object sender, EventArgs e) >> "DonatPlus.Minimal\MainForm.cs"
echo         { >> "DonatPlus.Minimal\MainForm.cs"
echo             try >> "DonatPlus.Minimal\MainForm.cs"
echo             { >> "DonatPlus.Minimal\MainForm.cs"
echo                 var weight = _geometryService.CalculateRebarWeight(12, 5.0); >> "DonatPlus.Minimal\MainForm.cs"
echo                 var rebar = _detectionService.ExtractRebarFromText("5xø12 L=250", 0, 0, "TEST"); >> "DonatPlus.Minimal\MainForm.cs"
echo                 MessageBox.Show($"Test başarılı!\\nø12mm 5m ağırlık: {weight:F2}kg\\nMetin analizi: {rebar?.Description}", "Test Sonucu"); >> "DonatPlus.Minimal\MainForm.cs"
echo             } >> "DonatPlus.Minimal\MainForm.cs"
echo             catch (Exception ex) >> "DonatPlus.Minimal\MainForm.cs"
echo             { >> "DonatPlus.Minimal\MainForm.cs"
echo                 MessageBox.Show($"Test hatası: {ex.Message}", "Hata"); >> "DonatPlus.Minimal\MainForm.cs"
echo             } >> "DonatPlus.Minimal\MainForm.cs"
echo         } >> "DonatPlus.Minimal\MainForm.cs"
echo     } >> "DonatPlus.Minimal\MainForm.cs"
echo } >> "DonatPlus.Minimal\MainForm.cs"

echo.
echo 5. Minimal versiyon derleniyor...
dotnet build DonatPlus.Minimal\DonatPlus.Minimal.csproj --configuration Release

if %errorLevel% neq 0 (
    echo HATA: Minimal versiyon derlenemedi!
    pause
    exit /b 1
)

echo ✓ Minimal versiyon derlendi

echo.
echo 6. Minimal versiyon kurulumu...
set PROGRAM_PATH=C:\Program Files\DonatPlus_Minimal

if exist "%PROGRAM_PATH%" rmdir /s /q "%PROGRAM_PATH%" >nul 2>&1
mkdir "%PROGRAM_PATH%" >nul 2>&1

xcopy "DonatPlus.Minimal\bin\Release\net48\*" "%PROGRAM_PATH%\" /Y /S /Q

echo.
echo 7. Masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop

echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus_Minimal.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus.Minimal.exe" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "DonatPlus Minimal - BAML Hatası Yok" >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"
cscript "%TEMP%\shortcut.vbs" >nul 2>&1
del "%TEMP%\shortcut.vbs" >nul 2>&1

echo.
echo ========================================
echo MINIMAL WPF ÇÖZÜMÜ TAMAMLANDI!
echo ========================================
echo.
echo ✓ Minimal WPF versiyonu oluşturuldu
echo ✓ BAML hatası tamamen ortadan kaldırıldı
echo ✓ Program kuruldu: %PROGRAM_PATH%
echo ✓ Masaüstü kısayolu: DonatPlus_Minimal
echo.
echo Bu versiyon WinForms kullanır, BAML hatası olmaz!
echo.
echo Test etmek ister misiniz? (E/H)
set /p choice=
if /i "%choice%"=="E" (
    echo Program başlatılıyor...
    start "" "%PROGRAM_PATH%\DonatPlus.Minimal.exe"
)

pause
