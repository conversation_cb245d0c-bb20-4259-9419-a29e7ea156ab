{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD.Working\\DonatPlus.AutoCAD.Working.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD.Working\\DonatPlus.AutoCAD.Working.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD.Working\\DonatPlus.AutoCAD.Working.csproj", "projectName": "DonatPlus.AutoCAD.Working", "projectPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD.Working\\DonatPlus.AutoCAD.Working.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\donatplus\\DonatPlus.AutoCAD.Working\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net48"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}