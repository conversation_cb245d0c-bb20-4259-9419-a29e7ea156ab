---
description:
globs:
alwaysApply: true
---
# SWE-agent overview

SWE-agent implements an AI software engineering agent that uses language models to
fix github issues.
Here's what you need to know about the project structure:

- The main entry points to running are in the `sweagent/run`, in particular `run_single.py` and `run_batch.py`, where the latter is used for benchmarking.
- The main class that governs agent behavior is `sweagent/agent/agents.py`
- The AI agent proposes actions that are executed in sandboxed docker containers
- At the beginning of an agent class, we initialize a `SWEEnv` class from `sweagent/environment/swe_env.py`. This class interfaces with the SWE-ReX project to interface with the sandboxed docker containers
- Part of the SWE-agent project are tools in `tools/`. They are organized in bundles. These bundles are copied to the sandboxed container and made available in the $PATH variable.
- In addition we provide two "inspectors" that allow to inspect trajectories (the output files of agents), `inspector_cli.py` provides a command line interface and `sweagent/inspector/server.py` a web interface.