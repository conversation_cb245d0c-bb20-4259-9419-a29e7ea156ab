using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using DonatPlus.Core.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DonatPlus.AutoCAD.Services
{
    /// <summary>
    /// AutoCAD DWG dosyalarından veri okuma servisi
    /// </summary>
    public class DwgReaderService
    {
        private readonly ILogger<DwgReaderService> _logger;
        
        public DwgReaderService(ILogger<DwgReaderService> logger)
        {
            _logger = logger;
        }
        
        /// <summary>
        /// Seçilen entity'leri okur
        /// </summary>
        public List<EntityData> ReadSelectedEntities(SelectionSet selectionSet)
        {
            var entities = new List<EntityData>();
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;
            
            try
            {
                using (var trans = db.TransactionManager.StartTransaction())
                {
                    foreach (SelectedObject selObj in selectionSet)
                    {
                        if (selObj != null)
                        {
                            var entity = trans.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                            if (entity != null)
                            {
                                var entityData = ExtractEntityData(entity);
                                if (entityData != null)
                                {
                                    entities.Add(entityData);
                                }
                            }
                        }
                    }
                    trans.Commit();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Seçilen entity'ler okunurken hata");
            }
            
            return entities;
        }
        
        /// <summary>
        /// Belirtilen sınır içindeki tüm entity'leri okur
        /// </summary>
        public List<EntityData> ReadEntitiesInBoundary(ObjectId boundaryId)
        {
            var entities = new List<EntityData>();
            var doc = Application.DocumentManager.MdiActiveDocument;
            var db = doc.Database;
            
            try
            {
                using (var trans = db.TransactionManager.StartTransaction())
                {
                    var boundary = trans.GetObject(boundaryId, OpenMode.ForRead) as Polyline;
                    if (boundary == null)
                    {
                        _logger.LogWarning("Sınır polyline'ı bulunamadı");
                        return entities;
                    }
                    
                    // Sınır içindeki tüm objeleri bul
                    var boundaryPoints = GetPolylinePoints(boundary);
                    
                    // Model space'deki tüm entity'leri kontrol et
                    var modelSpace = trans.GetObject(db.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
                    if (modelSpace != null)
                    {
                        foreach (ObjectId objId in modelSpace)
                        {
                            var entity = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                            if (entity != null && IsEntityInBoundary(entity, boundaryPoints))
                            {
                                var entityData = ExtractEntityData(entity);
                                if (entityData != null)
                                {
                                    entities.Add(entityData);
                                }
                            }
                        }
                    }
                    
                    trans.Commit();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Sınır içindeki entity'ler okunurken hata");
            }
            
            return entities;
        }
        
        /// <summary>
        /// Entity'den veri çıkarır
        /// </summary>
        private EntityData? ExtractEntityData(Entity entity)
        {
            try
            {
                var entityData = new EntityData
                {
                    ObjectId = entity.ObjectId,
                    EntityType = entity.GetType().Name,
                    LayerName = entity.Layer,
                    ColorIndex = entity.ColorIndex
                };
                
                switch (entity)
                {
                    case DBText text:
                        entityData.TextContent = text.TextString;
                        entityData.Position = new Point3d(text.Position.X, text.Position.Y, text.Position.Z);
                        entityData.Height = text.Height;
                        break;
                        
                    case MText mtext:
                        entityData.TextContent = mtext.Contents;
                        entityData.Position = new Point3d(mtext.Location.X, mtext.Location.Y, mtext.Location.Z);
                        entityData.Height = mtext.TextHeight;
                        break;
                        
                    case Line line:
                        entityData.StartPoint = line.StartPoint;
                        entityData.EndPoint = line.EndPoint;
                        entityData.Length = line.Length;
                        break;
                        
                    case Polyline polyline:
                        entityData.Points = GetPolylinePoints(polyline);
                        entityData.Length = GetPolylineLength(polyline);
                        entityData.IsClosed = polyline.Closed;
                        break;
                        
                    case Circle circle:
                        entityData.Center = circle.Center;
                        entityData.Radius = circle.Radius;
                        break;
                        
                    case Arc arc:
                        entityData.Center = arc.Center;
                        entityData.Radius = arc.Radius;
                        entityData.StartAngle = arc.StartAngle;
                        entityData.EndAngle = arc.EndAngle;
                        break;
                        
                    case BlockReference blockRef:
                        entityData.BlockName = blockRef.Name;
                        entityData.Position = blockRef.Position;
                        entityData.Scale = blockRef.ScaleFactors;
                        entityData.Rotation = blockRef.Rotation;
                        
                        // Block içindeki attribute'ları oku
                        entityData.Attributes = ReadBlockAttributes(blockRef);
                        break;
                        
                    default:
                        // Diğer entity tipleri için temel bilgileri al
                        if (entity.GeometricExtents.HasValue)
                        {
                            var extents = entity.GeometricExtents.Value;
                            entityData.BoundingBox = new BoundingBox
                            {
                                MinPoint = extents.MinPoint,
                                MaxPoint = extents.MaxPoint
                            };
                        }
                        break;
                }
                
                return entityData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Entity veri çıkarma hatası: {EntityType}", entity.GetType().Name);
                return null;
            }
        }
        
        /// <summary>
        /// Polyline noktalarını alır
        /// </summary>
        private List<Point3d> GetPolylinePoints(Polyline polyline)
        {
            var points = new List<Point3d>();
            
            for (int i = 0; i < polyline.NumberOfVertices; i++)
            {
                var point2d = polyline.GetPoint2dAt(i);
                points.Add(new Point3d(point2d.X, point2d.Y, 0));
            }
            
            return points;
        }
        
        /// <summary>
        /// Polyline uzunluğunu hesaplar
        /// </summary>
        private double GetPolylineLength(Polyline polyline)
        {
            double totalLength = 0;
            
            for (int i = 0; i < polyline.NumberOfVertices - 1; i++)
            {
                var point1 = polyline.GetPoint2dAt(i);
                var point2 = polyline.GetPoint2dAt(i + 1);
                totalLength += point1.GetDistanceTo(point2);
            }
            
            if (polyline.Closed && polyline.NumberOfVertices > 2)
            {
                var firstPoint = polyline.GetPoint2dAt(0);
                var lastPoint = polyline.GetPoint2dAt(polyline.NumberOfVertices - 1);
                totalLength += firstPoint.GetDistanceTo(lastPoint);
            }
            
            return totalLength;
        }
        
        /// <summary>
        /// Block attribute'larını okur
        /// </summary>
        private Dictionary<string, string> ReadBlockAttributes(BlockReference blockRef)
        {
            var attributes = new Dictionary<string, string>();
            
            try
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                var db = doc.Database;
                
                using (var trans = db.TransactionManager.StartTransaction())
                {
                    foreach (ObjectId attId in blockRef.AttributeCollection)
                    {
                        var attRef = trans.GetObject(attId, OpenMode.ForRead) as AttributeReference;
                        if (attRef != null)
                        {
                            attributes[attRef.Tag] = attRef.TextString;
                        }
                    }
                    trans.Commit();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Block attribute okuma hatası");
            }
            
            return attributes;
        }
        
        /// <summary>
        /// Entity'nin sınır içinde olup olmadığını kontrol eder
        /// </summary>
        private bool IsEntityInBoundary(Entity entity, List<Point3d> boundaryPoints)
        {
            try
            {
                // Entity'nin merkez noktasını al
                Point3d centerPoint;
                
                if (entity.GeometricExtents.HasValue)
                {
                    var extents = entity.GeometricExtents.Value;
                    centerPoint = new Point3d(
                        (extents.MinPoint.X + extents.MaxPoint.X) / 2,
                        (extents.MinPoint.Y + extents.MaxPoint.Y) / 2,
                        0
                    );
                }
                else
                {
                    return false;
                }
                
                // Point-in-polygon algoritması
                return IsPointInPolygon(centerPoint, boundaryPoints);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Sınır kontrolü hatası");
                return false;
            }
        }
        
        /// <summary>
        /// Nokta çokgen içinde mi kontrolü (Ray casting algoritması)
        /// </summary>
        private bool IsPointInPolygon(Point3d point, List<Point3d> polygon)
        {
            int intersections = 0;
            int n = polygon.Count;
            
            for (int i = 0; i < n; i++)
            {
                int j = (i + 1) % n;
                
                if (((polygon[i].Y > point.Y) != (polygon[j].Y > point.Y)) &&
                    (point.X < (polygon[j].X - polygon[i].X) * (point.Y - polygon[i].Y) / 
                     (polygon[j].Y - polygon[i].Y) + polygon[i].X))
                {
                    intersections++;
                }
            }
            
            return (intersections % 2) == 1;
        }
    }
    
    /// <summary>
    /// Entity verilerini tutan sınıf
    /// </summary>
    public class EntityData
    {
        public ObjectId ObjectId { get; set; }
        public string EntityType { get; set; } = string.Empty;
        public string LayerName { get; set; } = string.Empty;
        public int ColorIndex { get; set; }
        
        // Metin entity'leri için
        public string TextContent { get; set; } = string.Empty;
        public Point3d Position { get; set; }
        public double Height { get; set; }
        
        // Çizgi entity'leri için
        public Point3d StartPoint { get; set; }
        public Point3d EndPoint { get; set; }
        public double Length { get; set; }
        
        // Polyline için
        public List<Point3d> Points { get; set; } = new List<Point3d>();
        public bool IsClosed { get; set; }
        
        // Daire/Arc için
        public Point3d Center { get; set; }
        public double Radius { get; set; }
        public double StartAngle { get; set; }
        public double EndAngle { get; set; }
        
        // Block için
        public string BlockName { get; set; } = string.Empty;
        public Scale3d Scale { get; set; }
        public double Rotation { get; set; }
        public Dictionary<string, string> Attributes { get; set; } = new Dictionary<string, string>();
        
        // Genel
        public BoundingBox? BoundingBox { get; set; }
    }
    
    /// <summary>
    /// Sınırlayıcı kutu
    /// </summary>
    public class BoundingBox
    {
        public Point3d MinPoint { get; set; }
        public Point3d MaxPoint { get; set; }
        
        public double Width => Math.Abs(MaxPoint.X - MinPoint.X);
        public double Height => Math.Abs(MaxPoint.Y - MinPoint.Y);
        public Point3d Center => new Point3d(
            (MinPoint.X + MaxPoint.X) / 2,
            (MinPoint.Y + MaxPoint.Y) / 2,
            (MinPoint.Z + MaxPoint.Z) / 2
        );
    }
}
