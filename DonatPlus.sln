﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DonatPlus.Core", "DonatPlus.Core\DonatPlus.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DonatPlus.AutoCAD", "DonatPlus.AutoCAD\DonatPlus.AutoCAD.csproj", "{E6A5FB56-5A47-FCB3-2703-1A3CB01C8D4E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DonatPlus.UI", "DonatPlus.UI\DonatPlus.UI.csproj", "{4D15C282-AF1B-C8FF-2D54-9AD167598E10}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DonatPlus.Database", "DonatPlus.Database\DonatPlus.Database.csproj", "{FD06439B-EDF3-0802-8FAA-654A6871E9E8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DonatPlus.Reports", "DonatPlus.Reports\DonatPlus.Reports.csproj", "{61D8FD43-ED30-8DEC-1E03-23FE3DAA76DE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DonatPlus.Tests", "DonatPlus.Tests\DonatPlus.Tests.csproj", "{5741875D-4340-0DE2-802F-AB3AB762A4DC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DonatPlus.Launcher", "DonatPlus.Launcher\DonatPlus.Launcher.csproj", "{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.Build.0 = Release|Any CPU
		{E6A5FB56-5A47-FCB3-2703-1A3CB01C8D4E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E6A5FB56-5A47-FCB3-2703-1A3CB01C8D4E}.Debug|x64.Build.0 = Debug|Any CPU
		{E6A5FB56-5A47-FCB3-2703-1A3CB01C8D4E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E6A5FB56-5A47-FCB3-2703-1A3CB01C8D4E}.Debug|x86.Build.0 = Debug|Any CPU
		{E6A5FB56-5A47-FCB3-2703-1A3CB01C8D4E}.Release|x64.ActiveCfg = Release|Any CPU
		{E6A5FB56-5A47-FCB3-2703-1A3CB01C8D4E}.Release|x64.Build.0 = Release|Any CPU
		{E6A5FB56-5A47-FCB3-2703-1A3CB01C8D4E}.Release|x86.ActiveCfg = Release|Any CPU
		{E6A5FB56-5A47-FCB3-2703-1A3CB01C8D4E}.Release|x86.Build.0 = Release|Any CPU
		{4D15C282-AF1B-C8FF-2D54-9AD167598E10}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4D15C282-AF1B-C8FF-2D54-9AD167598E10}.Debug|x64.Build.0 = Debug|Any CPU
		{4D15C282-AF1B-C8FF-2D54-9AD167598E10}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4D15C282-AF1B-C8FF-2D54-9AD167598E10}.Debug|x86.Build.0 = Debug|Any CPU
		{4D15C282-AF1B-C8FF-2D54-9AD167598E10}.Release|x64.ActiveCfg = Release|Any CPU
		{4D15C282-AF1B-C8FF-2D54-9AD167598E10}.Release|x64.Build.0 = Release|Any CPU
		{4D15C282-AF1B-C8FF-2D54-9AD167598E10}.Release|x86.ActiveCfg = Release|Any CPU
		{4D15C282-AF1B-C8FF-2D54-9AD167598E10}.Release|x86.Build.0 = Release|Any CPU
		{FD06439B-EDF3-0802-8FAA-654A6871E9E8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FD06439B-EDF3-0802-8FAA-654A6871E9E8}.Debug|x64.Build.0 = Debug|Any CPU
		{FD06439B-EDF3-0802-8FAA-654A6871E9E8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FD06439B-EDF3-0802-8FAA-654A6871E9E8}.Debug|x86.Build.0 = Debug|Any CPU
		{FD06439B-EDF3-0802-8FAA-654A6871E9E8}.Release|x64.ActiveCfg = Release|Any CPU
		{FD06439B-EDF3-0802-8FAA-654A6871E9E8}.Release|x64.Build.0 = Release|Any CPU
		{FD06439B-EDF3-0802-8FAA-654A6871E9E8}.Release|x86.ActiveCfg = Release|Any CPU
		{FD06439B-EDF3-0802-8FAA-654A6871E9E8}.Release|x86.Build.0 = Release|Any CPU
		{61D8FD43-ED30-8DEC-1E03-23FE3DAA76DE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{61D8FD43-ED30-8DEC-1E03-23FE3DAA76DE}.Debug|x64.Build.0 = Debug|Any CPU
		{61D8FD43-ED30-8DEC-1E03-23FE3DAA76DE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{61D8FD43-ED30-8DEC-1E03-23FE3DAA76DE}.Debug|x86.Build.0 = Debug|Any CPU
		{61D8FD43-ED30-8DEC-1E03-23FE3DAA76DE}.Release|x64.ActiveCfg = Release|Any CPU
		{61D8FD43-ED30-8DEC-1E03-23FE3DAA76DE}.Release|x64.Build.0 = Release|Any CPU
		{61D8FD43-ED30-8DEC-1E03-23FE3DAA76DE}.Release|x86.ActiveCfg = Release|Any CPU
		{61D8FD43-ED30-8DEC-1E03-23FE3DAA76DE}.Release|x86.Build.0 = Release|Any CPU
		{5741875D-4340-0DE2-802F-AB3AB762A4DC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5741875D-4340-0DE2-802F-AB3AB762A4DC}.Debug|x64.Build.0 = Debug|Any CPU
		{5741875D-4340-0DE2-802F-AB3AB762A4DC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5741875D-4340-0DE2-802F-AB3AB762A4DC}.Debug|x86.Build.0 = Debug|Any CPU
		{5741875D-4340-0DE2-802F-AB3AB762A4DC}.Release|x64.ActiveCfg = Release|Any CPU
		{5741875D-4340-0DE2-802F-AB3AB762A4DC}.Release|x64.Build.0 = Release|Any CPU
		{5741875D-4340-0DE2-802F-AB3AB762A4DC}.Release|x86.ActiveCfg = Release|Any CPU
		{5741875D-4340-0DE2-802F-AB3AB762A4DC}.Release|x86.Build.0 = Release|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Debug|x64.Build.0 = Debug|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Debug|x86.Build.0 = Debug|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Release|x64.ActiveCfg = Release|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Release|x64.Build.0 = Release|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Release|x86.ActiveCfg = Release|Any CPU
		{DC639E8C-5B8E-4307-9366-7D0F9BB526F7}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
