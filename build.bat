@echo off
echo DonatPlus Build Script
echo =====================

:: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
set SOLUTION_FILE=DonatPlus.sln
set BUILD_CONFIG=Release
set OUTPUT_DIR=bin\Release
set PUBLISH_DIR=publish

:: Temizlik
echo Temizlik yapılıyor...
if exist %OUTPUT_DIR% rmdir /s /q %OUTPUT_DIR%
if exist %PUBLISH_DIR% rmdir /s /q %PUBLISH_DIR%

:: Restore
echo NuGet paketleri geri yükleniyor...
dotnet restore %SOLUTION_FILE%
if errorlevel 1 (
    echo HATA: NuGet restore başarısız!
    pause
    exit /b 1
)

:: Build
echo Proje derleniyor...
dotnet build %SOLUTION_FILE% --configuration %BUILD_CONFIG% --no-restore
if errorlevel 1 (
    echo HATA: Build başarısız!
    pause
    exit /b 1
)

:: Test (eğer test projesi varsa)
echo Testler çalıştırılıyor...
dotnet test %SOLUTION_FILE% --configuration %BUILD_CONFIG% --no-build --verbosity normal
if errorlevel 1 (
    echo UYARI: Bazı testler başarısız!
)

:: Publish UI
echo UI projesi publish ediliyor...
dotnet publish DonatPlus.UI\DonatPlus.UI.csproj --configuration %BUILD_CONFIG% --output %PUBLISH_DIR%\UI --self-contained false --runtime win-x64
if errorlevel 1 (
    echo HATA: UI publish başarısız!
    pause
    exit /b 1
)

:: Publish AutoCAD Plugin
echo AutoCAD plugin publish ediliyor...
dotnet publish DonatPlus.AutoCAD\DonatPlus.AutoCAD.csproj --configuration %BUILD_CONFIG% --output %PUBLISH_DIR%\AutoCAD --framework net48 --no-restore
if errorlevel 1 (
    echo HATA: AutoCAD plugin publish başarısız!
    pause
    exit /b 1
)

:: Dosyaları kopyala
echo Ek dosyalar kopyalanıyor...
copy README.md %PUBLISH_DIR%\
copy LICENSE %PUBLISH_DIR%\ 2>nul
mkdir %PUBLISH_DIR%\Documentation 2>nul
copy docs\*.* %PUBLISH_DIR%\Documentation\ 2>nul

:: Kurulum scripti oluştur
echo Kurulum scripti oluşturuluyor...
(
echo @echo off
echo echo DonatPlus Kurulum
echo echo ================
echo.
echo :: .NET 6.0 Runtime kontrolü
echo dotnet --version ^>nul 2^>^&1
echo if errorlevel 1 ^(
echo     echo HATA: .NET 6.0 Runtime bulunamadı!
echo     echo Lütfen https://dotnet.microsoft.com/download adresinden .NET 6.0 Runtime'ı indirin.
echo     pause
echo     exit /b 1
echo ^)
echo.
echo :: Uygulama klasörü oluştur
echo set INSTALL_DIR=%%ProgramFiles%%\DonatPlus
echo if not exist "%%INSTALL_DIR%%" mkdir "%%INSTALL_DIR%%"
echo.
echo :: Dosyaları kopyala
echo echo Dosyalar kopyalanıyor...
echo xcopy /E /Y UI\*.* "%%INSTALL_DIR%%\"
echo xcopy /E /Y AutoCAD\*.* "%%INSTALL_DIR%%\AutoCAD\"
echo.
echo :: Başlat menüsü kısayolu
echo echo Kısayollar oluşturuluyor...
echo set SHORTCUT_DIR=%%ProgramData%%\Microsoft\Windows\Start Menu\Programs
echo powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%%SHORTCUT_DIR%%\DonatPlus.lnk'^); $Shortcut.TargetPath = '%%INSTALL_DIR%%\DonatPlus.UI.exe'; $Shortcut.Save(^)"
echo.
echo :: Masaüstü kısayolu
echo set DESKTOP_DIR=%%USERPROFILE%%\Desktop
echo powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%%DESKTOP_DIR%%\DonatPlus.lnk'^); $Shortcut.TargetPath = '%%INSTALL_DIR%%\DonatPlus.UI.exe'; $Shortcut.Save(^)"
echo.
echo echo Kurulum tamamlandı!
echo echo DonatPlus başlat menüsünden veya masaüstü kısayolundan çalıştırılabilir.
echo pause
) > %PUBLISH_DIR%\install.bat

:: Kaldırma scripti oluştur
echo Kaldırma scripti oluşturuluyor...
(
echo @echo off
echo echo DonatPlus Kaldırma
echo echo ==================
echo.
echo set INSTALL_DIR=%%ProgramFiles%%\DonatPlus
echo set SHORTCUT_DIR=%%ProgramData%%\Microsoft\Windows\Start Menu\Programs
echo set DESKTOP_DIR=%%USERPROFILE%%\Desktop
echo.
echo echo Dosyalar kaldırılıyor...
echo if exist "%%INSTALL_DIR%%" rmdir /s /q "%%INSTALL_DIR%%"
echo if exist "%%SHORTCUT_DIR%%\DonatPlus.lnk" del "%%SHORTCUT_DIR%%\DonatPlus.lnk"
echo if exist "%%DESKTOP_DIR%%\DonatPlus.lnk" del "%%DESKTOP_DIR%%\DonatPlus.lnk"
echo.
echo echo Kaldırma tamamlandı!
echo pause
) > %PUBLISH_DIR%\uninstall.bat

:: AutoCAD kurulum talimatları
echo AutoCAD kurulum talimatları oluşturuluyor...
(
echo DonatPlus AutoCAD Plugin Kurulum Talimatları
echo ============================================
echo.
echo 1. AutoCAD'i yönetici olarak çalıştırın
echo 2. NETLOAD komutunu yazın
echo 3. DonatPlus.AutoCAD.dll dosyasını seçin
echo 4. Plugin yüklendikten sonra aşağıdaki komutları kullanabilirsiniz:
echo.
echo    DONATANALIZ  - Seçilen alandaki donatıları analiz eder
echo    DONATPAFTA   - Pafta sınırları içindeki donatıları analiz eder
echo    HASIRANALIZ  - Çelik hasır analizi yapar
echo    DONATBILGI   - Yardım bilgilerini gösterir
echo    DONATAYAR    - Ayarları düzenler
echo.
echo Otomatik Yükleme:
echo ================
echo Plugin'in her AutoCAD başlatıldığında otomatik yüklenmesi için:
echo 1. AutoCAD'de APPLOAD komutunu çalıştırın
echo 2. "Startup Suite" sekmesine geçin
echo 3. "Add" butonuna tıklayın
echo 4. DonatPlus.AutoCAD.dll dosyasını seçin
echo 5. "Close" butonuna tıklayın
echo.
echo Sorun Giderme:
echo ==============
echo - Plugin yüklenmiyorsa .NET Framework 4.8 yüklü olduğundan emin olun
echo - AutoCAD versiyonunuzun desteklendiğinden emin olun ^(2010-2025^)
echo - Antivirus yazılımının DLL dosyasını engellemediğinden emin olun
) > %PUBLISH_DIR%\AutoCAD\KURULUM_TALIMATLARI.txt

echo.
echo =====================
echo Build başarıyla tamamlandı!
echo =====================
echo.
echo Çıktı klasörü: %PUBLISH_DIR%
echo.
echo Kurulum için:
echo 1. %PUBLISH_DIR%\install.bat dosyasını yönetici olarak çalıştırın
echo 2. AutoCAD plugin kurulumu için %PUBLISH_DIR%\AutoCAD\KURULUM_TALIMATLARI.txt dosyasını okuyun
echo.
pause
