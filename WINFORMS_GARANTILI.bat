@echo off
echo ========================================
echo DonatPlus WinForms Garantili Versiyon
echo ========================================
echo.
echo WinForms versiyonu - BAML hatası imkansız!
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

echo.
echo 1. WinForms projesi oluşturuluyor...

if exist "DonatPlus.WinForms" rmdir /s /q "DonatPlus.WinForms" >nul 2>&1
mkdir "DonatPlus.WinForms"

echo.
echo 2. WinForms proje dosyası oluşturuluyor...

REM WinForms csproj oluştur
echo ^<Project Sdk="Microsoft.NET.Sdk"^> > "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo   ^<PropertyGroup^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo     ^<OutputType^>WinExe^</OutputType^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo     ^<TargetFramework^>net48^</TargetFramework^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo     ^<UseWindowsForms^>true^</UseWindowsForms^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo     ^<LangVersion^>latest^</LangVersion^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo   ^</PropertyGroup^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo   ^<ItemGroup^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo     ^<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="3.1.32" /^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo     ^<PackageReference Include="Microsoft.Extensions.Logging" Version="3.1.32" /^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo     ^<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="3.1.32" /^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo   ^</ItemGroup^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"
echo ^</Project^> >> "DonatPlus.WinForms\DonatPlus.WinForms.csproj"

echo.
echo 3. Core sınıfları kopyalanıyor...
xcopy "DonatPlus.Core\Models\*.cs" "DonatPlus.WinForms\Models\" /Y /S /I >nul 2>&1
xcopy "DonatPlus.Core\Services\*.cs" "DonatPlus.WinForms\Services\" /Y /S /I >nul 2>&1

echo.
echo 4. Program.cs oluşturuluyor...

REM Program.cs oluştur
echo using System; > "DonatPlus.WinForms\Program.cs"
echo using System.Windows.Forms; >> "DonatPlus.WinForms\Program.cs"
echo. >> "DonatPlus.WinForms\Program.cs"
echo namespace DonatPlus.WinForms >> "DonatPlus.WinForms\Program.cs"
echo { >> "DonatPlus.WinForms\Program.cs"
echo     static class Program >> "DonatPlus.WinForms\Program.cs"
echo     { >> "DonatPlus.WinForms\Program.cs"
echo         [STAThread] >> "DonatPlus.WinForms\Program.cs"
echo         static void Main() >> "DonatPlus.WinForms\Program.cs"
echo         { >> "DonatPlus.WinForms\Program.cs"
echo             Application.EnableVisualStyles(); >> "DonatPlus.WinForms\Program.cs"
echo             Application.SetCompatibleTextRenderingDefault(false); >> "DonatPlus.WinForms\Program.cs"
echo             Application.Run(new MainForm()); >> "DonatPlus.WinForms\Program.cs"
echo         } >> "DonatPlus.WinForms\Program.cs"
echo     } >> "DonatPlus.WinForms\Program.cs"
echo } >> "DonatPlus.WinForms\Program.cs"

echo.
echo 5. MainForm.cs oluşturuluyor...

REM MainForm.cs oluştur
echo using System; > "DonatPlus.WinForms\MainForm.cs"
echo using System.Drawing; >> "DonatPlus.WinForms\MainForm.cs"
echo using System.Windows.Forms; >> "DonatPlus.WinForms\MainForm.cs"
echo using DonatPlus.WinForms.Services; >> "DonatPlus.WinForms\MainForm.cs"
echo using Microsoft.Extensions.DependencyInjection; >> "DonatPlus.WinForms\MainForm.cs"
echo using Microsoft.Extensions.Logging; >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo namespace DonatPlus.WinForms >> "DonatPlus.WinForms\MainForm.cs"
echo { >> "DonatPlus.WinForms\MainForm.cs"
echo     public partial class MainForm : Form >> "DonatPlus.WinForms\MainForm.cs"
echo     { >> "DonatPlus.WinForms\MainForm.cs"
echo         private readonly GeometryCalculationService _geometryService; >> "DonatPlus.WinForms\MainForm.cs"
echo         private readonly RebarDetectionService _detectionService; >> "DonatPlus.WinForms\MainForm.cs"
echo         private TextBox txtDonatıMetni; >> "DonatPlus.WinForms\MainForm.cs"
echo         private TextBox txtSonuçlar; >> "DonatPlus.WinForms\MainForm.cs"
echo         private Button btnHesapla; >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo         public MainForm() >> "DonatPlus.WinForms\MainForm.cs"
echo         { >> "DonatPlus.WinForms\MainForm.cs"
echo             // Services >> "DonatPlus.WinForms\MainForm.cs"
echo             var services = new ServiceCollection(); >> "DonatPlus.WinForms\MainForm.cs"
echo             services.AddLogging(builder =^> builder.AddConsole()); >> "DonatPlus.WinForms\MainForm.cs"
echo             services.AddTransient^<GeometryCalculationService^>(); >> "DonatPlus.WinForms\MainForm.cs"
echo             services.AddTransient^<RebarDetectionService^>(); >> "DonatPlus.WinForms\MainForm.cs"
echo             var serviceProvider = services.BuildServiceProvider(); >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo             _geometryService = serviceProvider.GetRequiredService^<GeometryCalculationService^>(); >> "DonatPlus.WinForms\MainForm.cs"
echo             _detectionService = serviceProvider.GetRequiredService^<RebarDetectionService^>(); >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo             InitializeComponent(); >> "DonatPlus.WinForms\MainForm.cs"
echo         } >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo         private void InitializeComponent() >> "DonatPlus.WinForms\MainForm.cs"
echo         { >> "DonatPlus.WinForms\MainForm.cs"
echo             this.Text = "DonatPlus WinForms - BAML Hatası Yok!"; >> "DonatPlus.WinForms\MainForm.cs"
echo             this.Size = new Size(800, 600); >> "DonatPlus.WinForms\MainForm.cs"
echo             this.StartPosition = FormStartPosition.CenterScreen; >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo             var lblBaslık = new Label(); >> "DonatPlus.WinForms\MainForm.cs"
echo             lblBaslık.Text = "DonatPlus - WinForms Versiyonu"; >> "DonatPlus.WinForms\MainForm.cs"
echo             lblBaslık.Font = new Font("Arial", 16, FontStyle.Bold); >> "DonatPlus.WinForms\MainForm.cs"
echo             lblBaslık.Location = new Point(50, 20); >> "DonatPlus.WinForms\MainForm.cs"
echo             lblBaslık.Size = new Size(400, 30); >> "DonatPlus.WinForms\MainForm.cs"
echo             this.Controls.Add(lblBaslık); >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo             var lblMetin = new Label(); >> "DonatPlus.WinForms\MainForm.cs"
echo             lblMetin.Text = "Donatı Metni:"; >> "DonatPlus.WinForms\MainForm.cs"
echo             lblMetin.Location = new Point(50, 70); >> "DonatPlus.WinForms\MainForm.cs"
echo             lblMetin.Size = new Size(100, 20); >> "DonatPlus.WinForms\MainForm.cs"
echo             this.Controls.Add(lblMetin); >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo             txtDonatıMetni = new TextBox(); >> "DonatPlus.WinForms\MainForm.cs"
echo             txtDonatıMetni.Text = "5xø12 L=250"; >> "DonatPlus.WinForms\MainForm.cs"
echo             txtDonatıMetni.Location = new Point(50, 95); >> "DonatPlus.WinForms\MainForm.cs"
echo             txtDonatıMetni.Size = new Size(300, 25); >> "DonatPlus.WinForms\MainForm.cs"
echo             this.Controls.Add(txtDonatıMetni); >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo             btnHesapla = new Button(); >> "DonatPlus.WinForms\MainForm.cs"
echo             btnHesapla.Text = "Hesapla"; >> "DonatPlus.WinForms\MainForm.cs"
echo             btnHesapla.Location = new Point(50, 130); >> "DonatPlus.WinForms\MainForm.cs"
echo             btnHesapla.Size = new Size(100, 30); >> "DonatPlus.WinForms\MainForm.cs"
echo             btnHesapla.Click += BtnHesapla_Click; >> "DonatPlus.WinForms\MainForm.cs"
echo             this.Controls.Add(btnHesapla); >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo             var lblSonuç = new Label(); >> "DonatPlus.WinForms\MainForm.cs"
echo             lblSonuç.Text = "Sonuçlar:"; >> "DonatPlus.WinForms\MainForm.cs"
echo             lblSonuç.Location = new Point(50, 180); >> "DonatPlus.WinForms\MainForm.cs"
echo             lblSonuç.Size = new Size(100, 20); >> "DonatPlus.WinForms\MainForm.cs"
echo             this.Controls.Add(lblSonuç); >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo             txtSonuçlar = new TextBox(); >> "DonatPlus.WinForms\MainForm.cs"
echo             txtSonuçlar.Multiline = true; >> "DonatPlus.WinForms\MainForm.cs"
echo             txtSonuçlar.ReadOnly = true; >> "DonatPlus.WinForms\MainForm.cs"
echo             txtSonuçlar.ScrollBars = ScrollBars.Vertical; >> "DonatPlus.WinForms\MainForm.cs"
echo             txtSonuçlar.Location = new Point(50, 205); >> "DonatPlus.WinForms\MainForm.cs"
echo             txtSonuçlar.Size = new Size(500, 200); >> "DonatPlus.WinForms\MainForm.cs"
echo             this.Controls.Add(txtSonuçlar); >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo             var lblDurum = new Label(); >> "DonatPlus.WinForms\MainForm.cs"
echo             lblDurum.Text = "✓ BAML Hatası Çözülmüş - WinForms Versiyonu"; >> "DonatPlus.WinForms\MainForm.cs"
echo             lblDurum.ForeColor = Color.Green; >> "DonatPlus.WinForms\MainForm.cs"
echo             lblDurum.Location = new Point(50, 420); >> "DonatPlus.WinForms\MainForm.cs"
echo             lblDurum.Size = new Size(400, 20); >> "DonatPlus.WinForms\MainForm.cs"
echo             this.Controls.Add(lblDurum); >> "DonatPlus.WinForms\MainForm.cs"
echo         } >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo         private void BtnHesapla_Click(object sender, EventArgs e) >> "DonatPlus.WinForms\MainForm.cs"
echo         { >> "DonatPlus.WinForms\MainForm.cs"
echo             try >> "DonatPlus.WinForms\MainForm.cs"
echo             { >> "DonatPlus.WinForms\MainForm.cs"
echo                 var metin = txtDonatıMetni.Text; >> "DonatPlus.WinForms\MainForm.cs"
echo                 var rebar = _detectionService.ExtractRebarFromText(metin, 0, 0, "TEST"); >> "DonatPlus.WinForms\MainForm.cs"
echo. >> "DonatPlus.WinForms\MainForm.cs"
echo                 if (rebar != null) >> "DonatPlus.WinForms\MainForm.cs"
echo                 { >> "DonatPlus.WinForms\MainForm.cs"
echo                     var sonuç = $"Analiz Sonucu:\\r\\n"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     sonuç += $"Çap: {rebar.Diameter}mm\\r\\n"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     sonuç += $"Uzunluk: {rebar.Length}m\\r\\n"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     sonuç += $"Adet: {rebar.Quantity}\\r\\n"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     sonuç += $"Ağırlık: {rebar.Weight:F2}kg\\r\\n"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     sonuç += $"Toplam Uzunluk: {rebar.Length * rebar.Quantity:F2}m\\r\\n"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     sonuç += $"Toplam Ağırlık: {rebar.Weight * rebar.Quantity:F2}kg\\r\\n\\r\\n"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     sonuç += "Test Hesaplamaları:\\r\\n"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     sonuç += $"ø12mm 5m ağırlık: {_geometryService.CalculateRebarWeight(12, 5):F2}kg\\r\\n"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     sonuç += $"90° büküm uzunluğu: {_geometryService.CalculateBendLength(90, 50, 12):F4}m"; >> "DonatPlus.WinForms\MainForm.cs"
echo                     txtSonuçlar.Text = sonuç; >> "DonatPlus.WinForms\MainForm.cs"
echo                 } >> "DonatPlus.WinForms\MainForm.cs"
echo                 else >> "DonatPlus.WinForms\MainForm.cs"
echo                 { >> "DonatPlus.WinForms\MainForm.cs"
echo                     txtSonuçlar.Text = "Metin analiz edilemedi!"; >> "DonatPlus.WinForms\MainForm.cs"
echo                 } >> "DonatPlus.WinForms\MainForm.cs"
echo             } >> "DonatPlus.WinForms\MainForm.cs"
echo             catch (Exception ex) >> "DonatPlus.WinForms\MainForm.cs"
echo             { >> "DonatPlus.WinForms\MainForm.cs"
echo                 txtSonuçlar.Text = $"Hata: {ex.Message}"; >> "DonatPlus.WinForms\MainForm.cs"
echo             } >> "DonatPlus.WinForms\MainForm.cs"
echo         } >> "DonatPlus.WinForms\MainForm.cs"
echo     } >> "DonatPlus.WinForms\MainForm.cs"
echo } >> "DonatPlus.WinForms\MainForm.cs"

echo.
echo 6. WinForms versiyonu derleniyor...
dotnet build DonatPlus.WinForms\DonatPlus.WinForms.csproj --configuration Release

if %errorLevel% neq 0 (
    echo HATA: WinForms versiyonu derlenemedi!
    pause
    exit /b 1
)

echo ✓ WinForms versiyonu derlendi

echo.
echo 7. Program kurulumu yapılıyor...
set PROGRAM_PATH=C:\Program Files\DonatPlus_WinForms

if exist "%PROGRAM_PATH%" rmdir /s /q "%PROGRAM_PATH%" >nul 2>&1
mkdir "%PROGRAM_PATH%" >nul 2>&1

xcopy "DonatPlus.WinForms\bin\Release\net48\*" "%PROGRAM_PATH%\" /Y /S /Q

echo.
echo 8. Masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop

echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus_WinForms.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus.WinForms.exe" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "DonatPlus WinForms - BAML Hatası İmkansız" >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"
cscript "%TEMP%\shortcut.vbs" >nul 2>&1
del "%TEMP%\shortcut.vbs" >nul 2>&1

echo.
echo ========================================
echo WINFORMS GARANTİLİ VERSİYON HAZIR!
echo ========================================
echo.
echo ✓ WinForms versiyonu oluşturuldu
echo ✓ BAML hatası imkansız (WPF yok)
echo ✓ Core fonksiyonlar dahil
echo ✓ Program kuruldu: %PROGRAM_PATH%
echo ✓ Masaüstü kısayolu: DonatPlus_WinForms
echo.
echo Bu versiyon %100 çalışacak!
echo.
echo Test etmek ister misiniz? (E/H)
set /p choice=
if /i "%choice%"=="E" (
    echo Program başlatılıyor...
    start "" "%PROGRAM_PATH%\DonatPlus.WinForms.exe"
    echo.
    echo WinForms versiyonu başlatıldı!
)

pause
