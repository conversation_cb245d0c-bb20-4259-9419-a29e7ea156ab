@echo off
echo ========================================
echo DonatPlus Kurulum Scripti
echo ========================================
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    echo Sağ tık yapıp "Yönetici olarak çalıştır" seçin.
    pause
    exit /b 1
)

echo.
echo 1. Proje derleniyor...
dotnet build DonatPlus.sln --configuration Release
if %errorLevel% neq 0 (
    echo HATA: Proje derlene<PERSON>i!
    pause
    exit /b 1
)

echo.
echo 2. AutoCAD plugin klasörü oluşturuluyor...

REM AutoCAD 2024 kontrolü
if exist "C:\Program Files\Autodesk\AutoCAD 2024\" (
    set AUTOCAD_PATH=C:\Program Files\Autodesk\AutoCAD 2024
    set AUTOCAD_VERSION=2024
    goto :found_autocad
)

REM AutoCAD 2023 kontrolü
if exist "C:\Program Files\Autodesk\AutoCAD 2023\" (
    set AUTOCAD_PATH=C:\Program Files\Autodesk\AutoCAD 2023
    set AUTOCAD_VERSION=2023
    goto :found_autocad
)

REM AutoCAD 2022 kontrolü
if exist "C:\Program Files\Autodesk\AutoCAD 2022\" (
    set AUTOCAD_PATH=C:\Program Files\Autodesk\AutoCAD 2022
    set AUTOCAD_VERSION=2022
    goto :found_autocad
)

echo UYARI: AutoCAD bulunamadı. Manuel kurulum gerekli.
echo Plugin dosyaları DonatPlus.AutoCAD\bin\Release\net48\ klasöründe.
goto :skip_autocad

:found_autocad
echo AutoCAD %AUTOCAD_VERSION% tespit edildi: %AUTOCAD_PATH%

REM Plugin klasörü oluştur
set PLUGIN_PATH=%AUTOCAD_PATH%\Plug-ins\DonatPlus
if not exist "%PLUGIN_PATH%" (
    mkdir "%PLUGIN_PATH%"
)

echo.
echo 3. Plugin dosyaları kopyalanıyor...
xcopy "DonatPlus.AutoCAD\bin\Release\net48\*" "%PLUGIN_PATH%\" /Y /S
xcopy "DonatPlus.AutoCAD\PackageContents.xml" "%PLUGIN_PATH%\" /Y

echo Plugin kurulumu tamamlandı: %PLUGIN_PATH%

:skip_autocad

echo.
echo 4. Ana program kurulumu...
set PROGRAM_PATH=C:\Program Files\DonatPlus
if not exist "%PROGRAM_PATH%" (
    mkdir "%PROGRAM_PATH%"
)

xcopy "DonatPlus.UI\bin\Release\net6.0-windows\*" "%PROGRAM_PATH%\" /Y /S

echo.
echo 5. Masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus.UI.exe" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "DonatPlus - Betonarme Donatı Metraj Hesaplama" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"
cscript "%TEMP%\CreateShortcut.vbs" >nul
del "%TEMP%\CreateShortcut.vbs"

echo.
echo ========================================
echo KURULUM TAMAMLANDI!
echo ========================================
echo.
echo Ana Program: %PROGRAM_PATH%\DonatPlus.UI.exe
if defined AUTOCAD_VERSION (
    echo AutoCAD Plugin: %PLUGIN_PATH%
    echo.
    echo AutoCAD Komutları:
    echo - DONATPLUS: Ana menü
    echo - DPANALIZ: Analiz et
    echo - DPMETRAJ: Metraj listesi
    echo - DPRAPOR: Rapor oluştur
)
echo.
echo Masaüstünde kısayol oluşturuldu.
echo.
echo Test için TEST_CIZIMI_ORNEGI.md dosyasını inceleyin.
echo.
pause
