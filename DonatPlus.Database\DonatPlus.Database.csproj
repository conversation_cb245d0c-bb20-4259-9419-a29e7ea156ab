<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net6.0;net48</TargetFrameworks>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <AssemblyTitle>DonatPlus Database Layer</AssemblyTitle>
    <AssemblyDescription>Veritabanı katmanı</AssemblyDescription>
    <AssemblyCompany>DonatPlus</AssemblyCompany>
    <AssemblyProduct>DonatPlus</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TargetFramework)' == 'net6.0'">
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TargetFramework)' == 'net48'">
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net6.0'">
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="7.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.11" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net48'">
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.25" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
  </ItemGroup>

  <!-- .NET Framework 4.8 için ek referanslar -->
  <ItemGroup Condition="'$(TargetFramework)' == 'net48'">
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DonatPlus.Core\DonatPlus.Core.csproj" />
  </ItemGroup>

</Project>
