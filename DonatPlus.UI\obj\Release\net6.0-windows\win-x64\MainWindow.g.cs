﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CF13FE5ED318023924947FF2C2D1EAFC3387CE1B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DonatPlus.UI {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnMinimize;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnMaximize;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClose;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnNewProject;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnOpenProject;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAnalyzeDrawing;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCalculations;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReports;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSettings;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnHelp;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtProjectName;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtProjectInfo;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnQuickAnalyze;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSaveProject;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl MainTabControl;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem TabWelcome;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStatus;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse DbStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCurrentTime;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DonatPlus.UI;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnMinimize = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\..\MainWindow.xaml"
            this.BtnMinimize.Click += new System.Windows.RoutedEventHandler(this.BtnMinimize_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnMaximize = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\MainWindow.xaml"
            this.BtnMaximize.Click += new System.Windows.RoutedEventHandler(this.BtnMaximize_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnClose = ((System.Windows.Controls.Button)(target));
            
            #line 54 "..\..\..\..\MainWindow.xaml"
            this.BtnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnNewProject = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\..\MainWindow.xaml"
            this.BtnNewProject.Click += new System.Windows.RoutedEventHandler(this.BtnNewProject_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnOpenProject = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\MainWindow.xaml"
            this.BtnOpenProject.Click += new System.Windows.RoutedEventHandler(this.BtnOpenProject_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnAnalyzeDrawing = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\MainWindow.xaml"
            this.BtnAnalyzeDrawing.Click += new System.Windows.RoutedEventHandler(this.BtnAnalyzeDrawing_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnCalculations = ((System.Windows.Controls.Button)(target));
            
            #line 98 "..\..\..\..\MainWindow.xaml"
            this.BtnCalculations.Click += new System.Windows.RoutedEventHandler(this.BtnCalculations_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnReports = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\MainWindow.xaml"
            this.BtnReports.Click += new System.Windows.RoutedEventHandler(this.BtnReports_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnSettings = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\..\MainWindow.xaml"
            this.BtnSettings.Click += new System.Windows.RoutedEventHandler(this.BtnSettings_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnHelp = ((System.Windows.Controls.Button)(target));
            
            #line 124 "..\..\..\..\MainWindow.xaml"
            this.BtnHelp.Click += new System.Windows.RoutedEventHandler(this.BtnHelp_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.TxtProjectName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtProjectInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.BtnQuickAnalyze = ((System.Windows.Controls.Button)(target));
            
            #line 157 "..\..\..\..\MainWindow.xaml"
            this.BtnQuickAnalyze.Click += new System.Windows.RoutedEventHandler(this.BtnQuickAnalyze_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BtnSaveProject = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\..\..\MainWindow.xaml"
            this.BtnSaveProject.Click += new System.Windows.RoutedEventHandler(this.BtnSaveProject_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.MainTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 16:
            this.TabWelcome = ((System.Windows.Controls.TabItem)(target));
            return;
            case 17:
            this.TxtStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.DbStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 19:
            this.TxtCurrentTime = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

