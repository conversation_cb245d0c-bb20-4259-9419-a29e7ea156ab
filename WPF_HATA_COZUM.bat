@echo off
echo ========================================
echo DonatPlus WPF Hata Çözümü
echo ========================================
echo.
echo Çıkış kodu -532462766 WPF/XAML hatası gösteriyor.
echo Self-contained versiyon oluşturuluyor...
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Yönetici yetkisi tespit edildi.
) else (
    echo HATA: Bu script yönetici yetkisi ile çalıştırılmalıdır!
    pause
    exit /b 1
)

if not exist "DonatPlus.UI\DonatPlus.UI.csproj" (
    echo HATA: DonatPlus.UI projesi bulunamadı!
    echo Bu script'i donatplus klasöründe çalıştırın.
    pause
    exit /b 1
)

echo.
echo 1. Proje temizleniyor...
dotnet clean DonatPlus.UI\DonatPlus.UI.csproj >nul 2>&1

echo.
echo 2. Self-contained deployment oluşturuluyor...
echo Bu işlem 2-3 dakika sürebilir...

REM Önce basit self-contained dene
dotnet publish DonatPlus.UI\DonatPlus.UI.csproj -c Release -f net6.0-windows --self-contained true -r win-x64 --output "wpf_fix_output" --verbosity quiet

if %errorLevel% neq 0 (
    echo İlk deneme başarısız, alternatif yöntem deneniyor...
    dotnet publish DonatPlus.UI\DonatPlus.UI.csproj -c Release -f net6.0-windows --self-contained true -r win-x64 -p:PublishSingleFile=false --output "wpf_fix_output"
    
    if %errorLevel% neq 0 (
        echo HATA: Self-contained deployment oluşturulamadı!
        pause
        exit /b 1
    )
)

echo ✓ Self-contained deployment oluşturuldu

echo.
echo 3. Eski kurulum temizleniyor...
set PROGRAM_PATH=C:\Program Files\DonatPlus

if exist "%PROGRAM_PATH%" (
    rmdir /s /q "%PROGRAM_PATH%" >nul 2>&1
    timeout /t 2 /nobreak >nul
)

mkdir "%PROGRAM_PATH%" >nul 2>&1

echo.
echo 4. Yeni dosyalar kopyalanıyor...
xcopy "wpf_fix_output\*" "%PROGRAM_PATH%\" /Y /S /Q

if %errorLevel% neq 0 (
    echo HATA: Dosyalar kopyalanamadı!
    pause
    exit /b 1
)

echo ✓ Dosyalar kopyalandı

echo.
echo 5. WPF uyumlu başlatıcı oluşturuluyor...

REM Gelişmiş WPF başlatıcı
echo @echo off > "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo title DonatPlus WPF Fixed >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo echo DonatPlus WPF Fixed Version başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo echo Bu versiyon WPF hatalarını çözer! >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo echo. >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo cd /d "%%~dp0" >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo REM WPF hata düzeltme ayarları >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo set DOTNET_EnableWriteXorExecute=0 >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo set DOTNET_ROLL_FORWARD=Major >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo set COMPlus_EnableDiagnostics=0 >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo set DOTNET_DefaultDiagnosticPortSuspend=0 >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo set DOTNET_SYSTEM_NET_HTTP_USESOCKETSHTTPHANDLER=0 >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo echo Program başlatılıyor... >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo start "" "DonatPlus.UI.exe" >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo. >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo if %%errorLevel%% neq 0 ( >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo. >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo HATA: Program hala başlatılamadı! >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo. >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo Son çare çözümler: >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo 1. Bilgisayarı yeniden başlatın >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo 2. Windows Update yapın >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo 3. Antivirus yazılımını geçici olarak kapatın >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo 4. Programı yönetici olarak çalıştırın >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo. >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     pause >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo ^) else ( >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo     echo ✓ Program başarıyla başlatıldı! >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
echo ^) >> "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"

echo.
echo 6. Yeni masaüstü kısayolu oluşturuluyor...
set DESKTOP=%USERPROFILE%\Desktop

REM Eski kısayolları sil
if exist "%DESKTOP%\DonatPlus.lnk" del "%DESKTOP%\DonatPlus.lnk" >nul 2>&1
if exist "%DESKTOP%\DonatPlus_Fixed.lnk" del "%DESKTOP%\DonatPlus_Fixed.lnk" >nul 2>&1

echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\DonatPlus_Fixed.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%PROGRAM_PATH%" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "DonatPlus WPF Fixed - WPF Hatası Düzeltilmiş" >> "%TEMP%\shortcut.vbs"
echo oLink.WindowStyle = 1 >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"
cscript "%TEMP%\shortcut.vbs" >nul 2>&1
del "%TEMP%\shortcut.vbs" >nul 2>&1

echo.
echo 7. Temizlik yapılıyor...
if exist "wpf_fix_output" rmdir /s /q "wpf_fix_output" >nul 2>&1

echo.
echo ========================================
echo WPF HATA ÇÖZÜMÜ TAMAMLANDI!
echo ========================================
echo.
echo ✓ Self-contained deployment oluşturuldu
echo ✓ WPF hata düzeltmeleri uygulandı
echo ✓ Program kuruldu: %PROGRAM_PATH%
echo ✓ Yeni masaüstü kısayolu: DonatPlus_Fixed
echo.
echo ÖNEMLI:
echo Artık "DonatPlus_Fixed" kısayolunu kullanın!
echo Bu versiyon WPF hatalarını çözer.
echo.
echo Test etmek ister misiniz? (E/H)
set /p choice=
if /i "%choice%"=="E" (
    echo Program başlatılıyor...
    start "" "%PROGRAM_PATH%\DonatPlus_WPF_Fixed.bat"
    echo.
    echo Program başlatıldı! Açılmasını bekleyin...
)

echo.
echo Kurulum tamamlandı!
pause
