using DonatPlus.Database.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;

namespace DonatPlus.Database.DbContext
{
    /// <summary>
    /// DonatPlus Entity Framework DbContext
    /// </summary>
    public class DonatPlusContext : Microsoft.EntityFrameworkCore.DbContext
    {
        private readonly ILogger<DonatPlusContext>? _logger;
        
        public DonatPlusContext(DbContextOptions<DonatPlusContext> options, ILogger<DonatPlusContext>? logger = null) 
            : base(options)
        {
            _logger = logger;
        }
        
        // DbSet tanımlamaları
        public DbSet<Project> Projects { get; set; } = null!;
        public DbSet<ProjectCalculation> ProjectCalculations { get; set; } = null!;
        public DbSet<CalculationRebar> CalculationRebars { get; set; } = null!;
        public DbSet<ProjectDrawing> ProjectDrawings { get; set; } = null!;
        public DbSet<CalculationReport> CalculationReports { get; set; } = null!;
        public DbSet<WireMeshTemplate> WireMeshTemplates { get; set; } = null!;
        public DbSet<SystemSetting> SystemSettings { get; set; } = null!;
        
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // Varsayılan SQLite bağlantısı
                var dbPath = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "DonatPlus",
                    "donatplus.db"
                );
                
                // Klasör yoksa oluştur
                var directory = System.IO.Path.GetDirectoryName(dbPath);
                if (!string.IsNullOrEmpty(directory) && !System.IO.Directory.Exists(directory))
                {
                    System.IO.Directory.CreateDirectory(directory);
                }
                
                optionsBuilder.UseSqlite($"Data Source={dbPath}");
                
                if (_logger != null)
                {
                    optionsBuilder.LogTo(message => _logger.LogDebug(message));
                }
            }
        }
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // İlişkileri yapılandır
            ConfigureRelationships(modelBuilder);
            
            // İndeksleri yapılandır
            ConfigureIndexes(modelBuilder);
            
            // Varsayılan verileri ekle
            SeedData(modelBuilder);
        }
        
        private void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // Project -> ProjectCalculation (One-to-Many)
            modelBuilder.Entity<ProjectCalculation>()
                .HasOne(pc => pc.Project)
                .WithMany(p => p.Calculations)
                .HasForeignKey(pc => pc.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);
            
            // Project -> ProjectDrawing (One-to-Many)
            modelBuilder.Entity<ProjectDrawing>()
                .HasOne(pd => pd.Project)
                .WithMany(p => p.Drawings)
                .HasForeignKey(pd => pd.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);
            
            // ProjectCalculation -> CalculationRebar (One-to-Many)
            modelBuilder.Entity<CalculationRebar>()
                .HasOne(cr => cr.Calculation)
                .WithMany(pc => pc.RebarElements)
                .HasForeignKey(cr => cr.CalculationId)
                .OnDelete(DeleteBehavior.Cascade);
            
            // ProjectCalculation -> CalculationReport (One-to-Many)
            modelBuilder.Entity<CalculationReport>()
                .HasOne(cr => cr.Calculation)
                .WithMany(pc => pc.Reports)
                .HasForeignKey(cr => cr.CalculationId)
                .OnDelete(DeleteBehavior.Cascade);
        }
        
        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // Project indeksleri
            modelBuilder.Entity<Project>()
                .HasIndex(p => p.ProjectNumber)
                .IsUnique();
            
            modelBuilder.Entity<Project>()
                .HasIndex(p => p.Name);
            
            // ProjectCalculation indeksleri
            modelBuilder.Entity<ProjectCalculation>()
                .HasIndex(pc => pc.CalculationDate);
            
            modelBuilder.Entity<ProjectCalculation>()
                .HasIndex(pc => pc.ProjectId);
            
            // CalculationRebar indeksleri
            modelBuilder.Entity<CalculationRebar>()
                .HasIndex(cr => cr.CalculationId);
            
            modelBuilder.Entity<CalculationRebar>()
                .HasIndex(cr => new { cr.Diameter, cr.ElementType });
            
            // ProjectDrawing indeksleri
            modelBuilder.Entity<ProjectDrawing>()
                .HasIndex(pd => pd.ProjectId);
            
            modelBuilder.Entity<ProjectDrawing>()
                .HasIndex(pd => pd.FileName);
            
            // SystemSetting indeksleri
            modelBuilder.Entity<SystemSetting>()
                .HasIndex(ss => ss.Key)
                .IsUnique();
            
            modelBuilder.Entity<SystemSetting>()
                .HasIndex(ss => ss.Category);
        }
        
        private void SeedData(ModelBuilder modelBuilder)
        {
            // Varsayılan çelik hasır şablonları
            modelBuilder.Entity<WireMeshTemplate>().HasData(
                new WireMeshTemplate
                {
                    Id = 1,
                    Name = "Q131",
                    DiameterX = 5,
                    DiameterY = 5,
                    SpacingX = 15,
                    SpacingY = 15,
                    WeightPerSquareMeter = 2.2m,
                    Standard = "TS 648",
                    Description = "Standart hasır Q131 - 5/5mm - 15x15cm",
                    IsActive = true
                },
                new WireMeshTemplate
                {
                    Id = 2,
                    Name = "Q188",
                    DiameterX = 6,
                    DiameterY = 6,
                    SpacingX = 15,
                    SpacingY = 15,
                    WeightPerSquareMeter = 3.8m,
                    Standard = "TS 648",
                    Description = "Standart hasır Q188 - 6/6mm - 15x15cm",
                    IsActive = true
                },
                new WireMeshTemplate
                {
                    Id = 3,
                    Name = "Q257",
                    DiameterX = 7,
                    DiameterY = 7,
                    SpacingX = 15,
                    SpacingY = 15,
                    WeightPerSquareMeter = 5.1m,
                    Standard = "TS 648",
                    Description = "Standart hasır Q257 - 7/7mm - 15x15cm",
                    IsActive = true
                },
                new WireMeshTemplate
                {
                    Id = 4,
                    Name = "Q335",
                    DiameterX = 8,
                    DiameterY = 8,
                    SpacingX = 15,
                    SpacingY = 15,
                    WeightPerSquareMeter = 6.7m,
                    Standard = "TS 648",
                    Description = "Standart hasır Q335 - 8/8mm - 15x15cm",
                    IsActive = true
                }
            );
            
            // Varsayılan sistem ayarları
            modelBuilder.Entity<SystemSetting>().HasData(
                new SystemSetting
                {
                    Id = 1,
                    Key = "DefaultConcreteClass",
                    Value = "C25",
                    Description = "Varsayılan beton sınıfı",
                    Category = "Material",
                    ModifiedBy = "System"
                },
                new SystemSetting
                {
                    Id = 2,
                    Key = "DefaultSteelGrade",
                    Value = "S420",
                    Description = "Varsayılan çelik sınıfı",
                    Category = "Material",
                    ModifiedBy = "System"
                },
                new SystemSetting
                {
                    Id = 3,
                    Key = "StandardBarLength",
                    Value = "12.0",
                    Description = "Standart çubuk boyu (metre)",
                    Category = "Calculation",
                    ModifiedBy = "System"
                },
                new SystemSetting
                {
                    Id = 4,
                    Key = "TolerancePercentage",
                    Value = "5.0",
                    Description = "Tolerans yüzdesi",
                    Category = "Calculation",
                    ModifiedBy = "System"
                },
                new SystemSetting
                {
                    Id = 5,
                    Key = "AutoSaveInterval",
                    Value = "300",
                    Description = "Otomatik kaydetme aralığı (saniye)",
                    Category = "Application",
                    ModifiedBy = "System"
                },
                new SystemSetting
                {
                    Id = 6,
                    Key = "DefaultReportFormat",
                    Value = "Excel",
                    Description = "Varsayılan rapor formatı",
                    Category = "Report",
                    ModifiedBy = "System"
                },
                new SystemSetting
                {
                    Id = 7,
                    Key = "DatabaseBackupInterval",
                    Value = "7",
                    Description = "Veritabanı yedekleme aralığı (gün)",
                    Category = "Backup",
                    ModifiedBy = "System"
                }
            );
        }
        
        /// <summary>
        /// Veritabanını oluştur ve gerekli tabloları ekle
        /// </summary>
        public void EnsureCreated()
        {
            try
            {
                Database.EnsureCreated();
                _logger?.LogInformation("Veritabanı başarıyla oluşturuldu veya zaten mevcut");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Veritabanı oluşturma hatası");
                throw;
            }
        }
        
        /// <summary>
        /// Veritabanı migrasyonlarını uygula
        /// </summary>
        public void Migrate()
        {
            try
            {
                Database.Migrate();
                _logger?.LogInformation("Veritabanı migrasyonları başarıyla uygulandı");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Veritabanı migrasyon hatası");
                throw;
            }
        }
    }
}
