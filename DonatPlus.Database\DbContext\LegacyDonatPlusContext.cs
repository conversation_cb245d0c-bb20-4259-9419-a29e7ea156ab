#if NET48
using System;
using System.Data.SQLite;
using System.IO;
using DonatPlus.Database.Models;
using Microsoft.Extensions.Logging;

namespace DonatPlus.Database.DbContext
{
    /// <summary>
    /// .NET Framework 4.8 için basit SQLite veritabanı bağlantısı
    /// </summary>
    public class LegacyDonatPlusContext : IDisposable
    {
        private readonly SQLiteConnection _connection;
        private readonly ILogger<LegacyDonatPlusContext> _logger;
        
        public LegacyDonatPlusContext(ILogger<LegacyDonatPlusContext> logger = null)
        {
            _logger = logger;
            
            var dbPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "DonatPlus",
                "donatplus_legacy.db"
            );
            
            // Klasör yoksa oluştur
            var directory = Path.GetDirectoryName(dbPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            _connection = new SQLiteConnection($"Data Source={dbPath};Version=3;");
            _connection.Open();
            
            InitializeDatabase();
        }
        
        private void InitializeDatabase()
        {
            try
            {
                // Basit tablo oluşturma
                var createProjectsTable = @"
                    CREATE TABLE IF NOT EXISTS Projects (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        ProjectNumber TEXT,
                        ClientName TEXT,
                        Location TEXT,
                        Engineer TEXT,
                        Description TEXT,
                        ConcreteClass TEXT DEFAULT 'C25',
                        SteelGrade TEXT DEFAULT 'S420',
                        CreatedDate TEXT,
                        ModifiedDate TEXT,
                        IsActive INTEGER DEFAULT 1
                    )";
                
                var createCalculationsTable = @"
                    CREATE TABLE IF NOT EXISTS ProjectCalculations (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ProjectId INTEGER,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        CalculationDate TEXT,
                        CalculatedBy TEXT,
                        TotalWeight REAL,
                        TotalLength REAL,
                        TotalCost REAL,
                        SelectedArea TEXT,
                        CalculationData TEXT,
                        IsApproved INTEGER DEFAULT 0,
                        ApprovedBy TEXT,
                        ApprovalDate TEXT,
                        FOREIGN KEY (ProjectId) REFERENCES Projects(Id)
                    )";
                
                var createRebarsTable = @"
                    CREATE TABLE IF NOT EXISTS CalculationRebars (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        CalculationId INTEGER,
                        ElementType TEXT,
                        Diameter REAL,
                        Length REAL,
                        Quantity INTEGER,
                        Weight REAL,
                        Grade TEXT DEFAULT 'S420',
                        Position TEXT,
                        Description TEXT,
                        LayerName TEXT,
                        StartX REAL,
                        StartY REAL,
                        EndX REAL,
                        EndY REAL,
                        IsWireMesh INTEGER DEFAULT 0,
                        MeshSpacingX REAL,
                        MeshSpacingY REAL,
                        MeshWidth REAL,
                        MeshHeight REAL,
                        CreatedDate TEXT,
                        FOREIGN KEY (CalculationId) REFERENCES ProjectCalculations(Id)
                    )";
                
                ExecuteNonQuery(createProjectsTable);
                ExecuteNonQuery(createCalculationsTable);
                ExecuteNonQuery(createRebarsTable);
                
                _logger?.LogInformation("Legacy veritabanı başarıyla başlatıldı");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Legacy veritabanı başlatma hatası");
                throw;
            }
        }
        
        public void ExecuteNonQuery(string sql)
        {
            using (var command = new SQLiteCommand(sql, _connection))
            {
                command.ExecuteNonQuery();
            }
        }
        
        public SQLiteDataReader ExecuteReader(string sql)
        {
            var command = new SQLiteCommand(sql, _connection);
            return command.ExecuteReader();
        }
        
        public object ExecuteScalar(string sql)
        {
            using (var command = new SQLiteCommand(sql, _connection))
            {
                return command.ExecuteScalar();
            }
        }
        
        public void Dispose()
        {
            _connection?.Close();
            _connection?.Dispose();
        }
    }
}
#endif
