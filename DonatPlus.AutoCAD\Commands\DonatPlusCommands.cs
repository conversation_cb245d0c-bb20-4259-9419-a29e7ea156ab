using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using DonatPlus.AutoCAD.Services;
using DonatPlus.Core.Models;
using DonatPlus.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DonatPlus.AutoCAD.Commands
{
    /// <summary>
    /// DonatPlus AutoCAD komutları
    /// </summary>
    public class DonatPlusCommands
    {
        private static IServiceProvider? _serviceProvider;
        private static ILogger<DonatPlusCommands>? _logger;
        
        static DonatPlusCommands()
        {
            InitializeServices();
        }
        
        private static void InitializeServices()
        {
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole());
            services.AddTransient<RebarDetectionService>();
            services.AddTransient<GeometryCalculationService>();
            services.AddTransient<DwgReaderService>();
            services.AddTransient<EntityAnalyzerService>();
            
            _serviceProvider = services.BuildServiceProvider();
            _logger = _serviceProvider.GetService<ILogger<DonatPlusCommands>>();
        }
        
        /// <summary>
        /// Ana donatı analiz komutu
        /// </summary>
        [CommandMethod("DONATANALIZ")]
        public void AnalyzeRebar()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            
            try
            {
                ed.WriteMessage("\nDonatPlus - Donatı Analizi Başlatılıyor...\n");
                
                // Kullanıcıdan alan seçimini iste
                var selectionResult = ed.GetSelection();
                if (selectionResult.Status != PromptStatus.OK)
                {
                    ed.WriteMessage("\nSeçim iptal edildi.\n");
                    return;
                }
                
                var dwgReader = _serviceProvider?.GetService<DwgReaderService>();
                var entityAnalyzer = _serviceProvider?.GetService<EntityAnalyzerService>();
                
                if (dwgReader == null || entityAnalyzer == null)
                {
                    ed.WriteMessage("\nServis başlatma hatası!\n");
                    return;
                }
                
                // Seçilen objeleri analiz et
                var entities = dwgReader.ReadSelectedEntities(selectionResult.Value);
                var rebarElements = entityAnalyzer.AnalyzeEntities(entities);
                
                if (rebarElements.Any())
                {
                    ShowResults(rebarElements, ed);
                    
                    // Sonuçları dosyaya kaydet
                    var saveResult = ed.GetString("\nSonuçları kaydetmek istiyor musunuz? (E/H): ");
                    if (saveResult.Status == PromptStatus.OK && 
                        saveResult.StringResult.ToUpperInvariant() == "E")
                    {
                        SaveResults(rebarElements, ed);
                    }
                }
                else
                {
                    ed.WriteMessage("\nSeçilen alanda donatı bulunamadı.\n");
                }
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\nHata: {ex.Message}\n");
                _logger?.LogError(ex, "Donatı analizi sırasında hata");
            }
        }
        
        /// <summary>
        /// Pafta seçimi ile analiz
        /// </summary>
        [CommandMethod("DONATPAFTA")]
        public void AnalyzeRebarBySheet()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            
            try
            {
                ed.WriteMessage("\nPafta sınırlarını seçin...\n");
                
                // Pafta sınırı için polyline seç
                var plineOpts = new PromptEntityOptions("\nPafta sınırı polyline'ını seçin: ");
                plineOpts.SetRejectMessage("\nSadece polyline seçebilirsiniz!");
                plineOpts.AddAllowedClass(typeof(Polyline), true);
                
                var plineResult = ed.GetEntity(plineOpts);
                if (plineResult.Status != PromptStatus.OK)
                {
                    ed.WriteMessage("\nSeçim iptal edildi.\n");
                    return;
                }
                
                var dwgReader = _serviceProvider?.GetService<DwgReaderService>();
                var entityAnalyzer = _serviceProvider?.GetService<EntityAnalyzerService>();
                
                if (dwgReader == null || entityAnalyzer == null)
                {
                    ed.WriteMessage("\nServis başlatma hatası!\n");
                    return;
                }
                
                // Pafta içindeki tüm objeleri al
                var entities = dwgReader.ReadEntitiesInBoundary(plineResult.ObjectId);
                var rebarElements = entityAnalyzer.AnalyzeEntities(entities);
                
                if (rebarElements.Any())
                {
                    ShowResults(rebarElements, ed);
                    SaveResults(rebarElements, ed);
                }
                else
                {
                    ed.WriteMessage("\nSeçilen paftada donatı bulunamadı.\n");
                }
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\nHata: {ex.Message}\n");
                _logger?.LogError(ex, "Pafta analizi sırasında hata");
            }
        }
        
        /// <summary>
        /// Hasır analizi komutu
        /// </summary>
        [CommandMethod("HASIRANALIZ")]
        public void AnalyzeWireMesh()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            
            try
            {
                ed.WriteMessage("\nHasır analizi başlatılıyor...\n");
                
                var selectionResult = ed.GetSelection();
                if (selectionResult.Status != PromptStatus.OK)
                {
                    ed.WriteMessage("\nSeçim iptal edildi.\n");
                    return;
                }
                
                var dwgReader = _serviceProvider?.GetService<DwgReaderService>();
                var entityAnalyzer = _serviceProvider?.GetService<EntityAnalyzerService>();
                
                if (dwgReader == null || entityAnalyzer == null)
                {
                    ed.WriteMessage("\nServis başlatma hatası!\n");
                    return;
                }
                
                var entities = dwgReader.ReadSelectedEntities(selectionResult.Value);
                var wireMeshElements = entityAnalyzer.AnalyzeWireMesh(entities);
                
                if (wireMeshElements.Any())
                {
                    ShowWireMeshResults(wireMeshElements, ed);
                }
                else
                {
                    ed.WriteMessage("\nSeçilen alanda hasır bulunamadı.\n");
                }
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\nHata: {ex.Message}\n");
                _logger?.LogError(ex, "Hasır analizi sırasında hata");
            }
        }
        
        /// <summary>
        /// Donatı bilgilerini göster
        /// </summary>
        [CommandMethod("DONATBILGI")]
        public void ShowRebarInfo()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            
            try
            {
                ed.WriteMessage("\nDonatPlus v1.0 - Betonarme Donatı Metraj Hesaplama\n");
                ed.WriteMessage("Komutlar:\n");
                ed.WriteMessage("DONATANALIZ - Seçilen alandaki donatıları analiz eder\n");
                ed.WriteMessage("DONATPAFTA - Pafta sınırları içindeki donatıları analiz eder\n");
                ed.WriteMessage("HASIRANALIZ - Çelik hasır analizi yapar\n");
                ed.WriteMessage("DONATBILGI - Bu yardım mesajını gösterir\n");
                ed.WriteMessage("DONATAYAR - Ayarları düzenler\n");
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\nHata: {ex.Message}\n");
                _logger?.LogError(ex, "Bilgi gösterimi sırasında hata");
            }
        }
        
        /// <summary>
        /// Ayarlar komutu
        /// </summary>
        [CommandMethod("DONATAYAR")]
        public void ShowSettings()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            
            try
            {
                ed.WriteMessage("\nDonatPlus Ayarları:\n");
                ed.WriteMessage("1. Varsayılan çelik sınıfı: S420\n");
                ed.WriteMessage("2. Varsayılan beton sınıfı: C25\n");
                ed.WriteMessage("3. Standart çubuk boyu: 12m\n");
                ed.WriteMessage("4. Tolerans değeri: ±5%\n");
                
                // Gelecekte ayar değiştirme özelliği eklenebilir
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\nHata: {ex.Message}\n");
                _logger?.LogError(ex, "Ayarlar gösterimi sırasında hata");
            }
        }
        
        private void ShowResults(List<RebarElement> rebarElements, Editor ed)
        {
            ed.WriteMessage("\n=== DONAT ANALİZ SONUÇLARI ===\n");
            ed.WriteMessage($"Toplam donatı sayısı: {rebarElements.Count}\n");
            
            var totalWeight = rebarElements.Sum(r => r.TotalWeight);
            var totalLength = rebarElements.Sum(r => r.TotalLength);
            
            ed.WriteMessage($"Toplam ağırlık: {totalWeight:F2} kg\n");
            ed.WriteMessage($"Toplam uzunluk: {totalLength:F2} m\n");
            
            // Çaplara göre grupla
            var groupedByDiameter = rebarElements.GroupBy(r => r.Diameter);
            ed.WriteMessage("\nÇaplara göre dağılım:\n");
            
            foreach (var group in groupedByDiameter.OrderBy(g => g.Key))
            {
                var diameterWeight = group.Sum(r => r.TotalWeight);
                var diameterLength = group.Sum(r => r.TotalLength);
                var count = group.Sum(r => r.Quantity);
                
                ed.WriteMessage($"ø{group.Key}mm: {count} adet, {diameterLength:F2}m, {diameterWeight:F2}kg\n");
            }
            
            // Element tiplerine göre grupla
            var groupedByType = rebarElements.GroupBy(r => r.ElementType);
            ed.WriteMessage("\nElement tiplerine göre dağılım:\n");
            
            foreach (var group in groupedByType)
            {
                var typeWeight = group.Sum(r => r.TotalWeight);
                ed.WriteMessage($"{group.Key}: {typeWeight:F2} kg\n");
            }
        }
        
        private void ShowWireMeshResults(List<RebarElement> wireMeshElements, Editor ed)
        {
            ed.WriteMessage("\n=== HASIR ANALİZ SONUÇLARI ===\n");
            ed.WriteMessage($"Toplam hasır sayısı: {wireMeshElements.Count}\n");
            
            var totalWeight = wireMeshElements.Sum(r => r.TotalWeight);
            var totalArea = wireMeshElements.Sum(r => r.MeshWidth * r.MeshHeight);
            
            ed.WriteMessage($"Toplam ağırlık: {totalWeight:F2} kg\n");
            ed.WriteMessage($"Toplam alan: {totalArea:F2} m²\n");
            
            foreach (var mesh in wireMeshElements)
            {
                ed.WriteMessage($"\nHasır: {mesh.MeshWidth:F2}x{mesh.MeshHeight:F2}m\n");
                ed.WriteMessage($"Aralık: {mesh.MeshSpacingX:F0}x{mesh.MeshSpacingY:F0}cm\n");
                ed.WriteMessage($"Ağırlık: {mesh.TotalWeight:F2} kg\n");
            }
        }
        
        private void SaveResults(List<RebarElement> rebarElements, Editor ed)
        {
            try
            {
                var fileName = $"DonatAnaliz_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                var filePath = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                    fileName
                );
                
                using (var writer = new System.IO.StreamWriter(filePath))
                {
                    writer.WriteLine("DONATPLUS - DONAT ANALİZ RAPORU");
                    writer.WriteLine($"Tarih: {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
                    writer.WriteLine($"Toplam donatı sayısı: {rebarElements.Count}");
                    writer.WriteLine($"Toplam ağırlık: {rebarElements.Sum(r => r.TotalWeight):F2} kg");
                    writer.WriteLine($"Toplam uzunluk: {rebarElements.Sum(r => r.TotalLength):F2} m");
                    writer.WriteLine();
                    
                    writer.WriteLine("DETAY LİSTESİ:");
                    foreach (var rebar in rebarElements)
                    {
                        writer.WriteLine($"ø{rebar.Diameter}mm - {rebar.Quantity} adet - " +
                                       $"{rebar.Length:F2}m - {rebar.TotalWeight:F2}kg - {rebar.ElementType}");
                    }
                }
                
                ed.WriteMessage($"\nSonuçlar kaydedildi: {filePath}\n");
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\nDosya kaydetme hatası: {ex.Message}\n");
                _logger?.LogError(ex, "Sonuç kaydetme hatası");
            }
        }
    }
}
