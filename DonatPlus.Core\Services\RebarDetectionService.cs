#if NET48
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
#endif
using DonatPlus.Core.Models;
using Microsoft.Extensions.Logging;

namespace DonatPlus.Core.Services
{
    /// <summary>
    /// AutoCAD çizimlerinden donatı bilgilerini algılayan servis
    /// </summary>
    public class RebarDetectionService
    {
        private readonly ILogger<RebarDetectionService> _logger;
        private readonly GeometryCalculationService _geometryService;
        
        // Donatı çapı algılama için regex pattern'ları
        private readonly Regex _diameterPattern = new Regex(@"[øΦφ]\s*(\d+)", RegexOptions.IgnoreCase);
        private readonly Regex _quantityPattern = new Regex(@"(\d+)\s*[xX×]\s*[øΦφ]", RegexOptions.IgnoreCase);
        private readonly Regex _lengthPattern = new Regex(@"L\s*=\s*(\d+(?:\.\d+)?)", RegexOptions.IgnoreCase);
        
        // Standart donatı çapları (mm)
        private readonly double[] _standardDiameters = { 6, 8, 10, 12, 14, 16, 18, 20, 22, 25, 28, 32 };
        
        public RebarDetectionService(ILogger<RebarDetectionService> logger, GeometryCalculationService geometryService)
        {
            _logger = logger;
            _geometryService = geometryService;
        }
        
        /// <summary>
        /// Metin içeriğinden donatı bilgilerini çıkarır
        /// </summary>
        public RebarElement? ExtractRebarFromText(string text, double x, double y, string layerName)
        {
            try
            {
                var rebar = new RebarElement
                {
                    StartX = x,
                    StartY = y,
                    LayerName = layerName,
                    Description = text
                };
                
                // Çap bilgisini çıkar
                var diameterMatch = _diameterPattern.Match(text);
                if (diameterMatch.Success)
                {
                    if (double.TryParse(diameterMatch.Groups[1].Value, out double diameter))
                    {
                        rebar.Diameter = diameter;
                    }
                }
                
                // Adet bilgisini çıkar
                var quantityMatch = _quantityPattern.Match(text);
                if (quantityMatch.Success)
                {
                    if (int.TryParse(quantityMatch.Groups[1].Value, out int quantity))
                    {
                        rebar.Quantity = quantity;
                    }
                }
                else
                {
                    rebar.Quantity = 1; // Varsayılan adet
                }
                
                // Uzunluk bilgisini çıkar
                var lengthMatch = _lengthPattern.Match(text);
                if (lengthMatch.Success)
                {
                    if (double.TryParse(lengthMatch.Groups[1].Value, out double length))
                    {
                        rebar.Length = length / 100; // cm'den m'ye çevir
                    }
                }
                
                // Element tipini layer adından tahmin et
                rebar.ElementType = DetermineElementType(layerName, text);
                
                // Ağırlığı hesapla
                if (rebar.Diameter > 0 && rebar.Length > 0)
                {
                    rebar.Weight = _geometryService.CalculateRebarWeight(rebar.Diameter, rebar.Length);
                    return rebar;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Metin analizi sırasında hata: {Text}", text);
                return null;
            }
        }
        
        /// <summary>
        /// Paralel çizgilerden donatı adedini tahmin eder
        /// </summary>
        public int EstimateQuantityFromParallelLines(List<LineSegment> lines, double tolerance = 0.1)
        {
            if (lines == null || lines.Count == 0)
                return 1;
                
            try
            {
                // Paralel çizgileri grupla
                var parallelGroups = GroupParallelLines(lines, tolerance);
                
                // En büyük grubu al (ana donatı grubu olabilir)
                var mainGroup = parallelGroups.OrderByDescending(g => g.Count).FirstOrDefault();
                
                return mainGroup?.Count ?? 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Paralel çizgi analizi sırasında hata");
                return 1;
            }
        }
        
        /// <summary>
        /// Çelik hasır algılama
        /// </summary>
        public RebarElement? DetectWireMesh(List<LineSegment> horizontalLines, List<LineSegment> verticalLines, string layerName)
        {
            try
            {
                if (horizontalLines.Count < 2 || verticalLines.Count < 2)
                    return null;
                
                // Hasır boyutlarını hesapla
                var minX = horizontalLines.SelectMany(l => new[] { l.StartX, l.EndX }).Min();
                var maxX = horizontalLines.SelectMany(l => new[] { l.StartX, l.EndX }).Max();
                var minY = verticalLines.SelectMany(l => new[] { l.StartY, l.EndY }).Min();
                var maxY = verticalLines.SelectMany(l => new[] { l.StartY, l.EndY }).Max();
                
                var width = Math.Abs(maxX - minX) / 100; // cm'den m'ye
                var height = Math.Abs(maxY - minY) / 100;
                
                // Aralıkları hesapla
                var spacingX = CalculateAverageSpacing(horizontalLines, true) / 10; // mm'den cm'ye
                var spacingY = CalculateAverageSpacing(verticalLines, false) / 10;
                
                var wireMesh = new RebarElement
                {
                    IsWireMesh = true,
                    MeshWidth = width,
                    MeshHeight = height,
                    MeshSpacingX = spacingX,
                    MeshSpacingY = spacingY,
                    LayerName = layerName,
                    ElementType = "Hasır",
                    Quantity = 1,
                    StartX = minX,
                    StartY = minY,
                    EndX = maxX,
                    EndY = maxY
                };
                
                // Standart hasır şablonundan çap ve ağırlık bilgisini al
                var template = GetWireMeshTemplate(spacingX, spacingY);
                if (template != null)
                {
                    wireMesh.Diameter = template.DiameterX; // Ana çap
                    wireMesh.Weight = template.WeightPerSquareMeter * width * height;
                    wireMesh.Description = $"Hasır {template.Name} - {width:F2}x{height:F2}m";
                }
                
                return wireMesh;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hasır algılama sırasında hata");
                return null;
            }
        }
        
        private string DetermineElementType(string layerName, string text)
        {
            var layer = layerName.ToLowerInvariant();
            var content = text.ToLowerInvariant();
            
            if (layer.Contains("kiriş") || layer.Contains("beam") || content.Contains("kiriş"))
                return "Kiriş";
            if (layer.Contains("kolon") || layer.Contains("column") || content.Contains("kolon"))
                return "Kolon";
            if (layer.Contains("döşeme") || layer.Contains("slab") || content.Contains("döşeme"))
                return "Döşeme";
            if (layer.Contains("perde") || layer.Contains("wall") || content.Contains("perde"))
                return "Perde";
            if (layer.Contains("hasır") || layer.Contains("mesh") || content.Contains("hasır"))
                return "Hasır";
                
            return "Diğer";
        }
        
        private List<List<LineSegment>> GroupParallelLines(List<LineSegment> lines, double tolerance)
        {
            var groups = new List<List<LineSegment>>();
            
            foreach (var line in lines)
            {
                bool addedToGroup = false;
                
                foreach (var group in groups)
                {
                    if (group.Any(l => AreParallel(line, l, tolerance)))
                    {
                        group.Add(line);
                        addedToGroup = true;
                        break;
                    }
                }
                
                if (!addedToGroup)
                {
                    groups.Add(new List<LineSegment> { line });
                }
            }
            
            return groups;
        }
        
        private bool AreParallel(LineSegment line1, LineSegment line2, double tolerance)
        {
            var angle1 = Math.Atan2(line1.EndY - line1.StartY, line1.EndX - line1.StartX);
            var angle2 = Math.Atan2(line2.EndY - line2.StartY, line2.EndX - line2.StartX);
            
            var angleDiff = Math.Abs(angle1 - angle2);
            return angleDiff < tolerance || Math.Abs(angleDiff - Math.PI) < tolerance;
        }
        
        private double CalculateAverageSpacing(List<LineSegment> lines, bool horizontal)
        {
            if (lines.Count < 2) return 0;
            
            var positions = horizontal 
                ? lines.Select(l => (l.StartY + l.EndY) / 2).OrderBy(y => y).ToList()
                : lines.Select(l => (l.StartX + l.EndX) / 2).OrderBy(x => x).ToList();
            
            var spacings = new List<double>();
            for (int i = 1; i < positions.Count; i++)
            {
                spacings.Add(Math.Abs(positions[i] - positions[i - 1]));
            }
            
            return spacings.Count > 0 ? spacings.Average() : 0;
        }
        
        private WireMeshTemplate? GetWireMeshTemplate(double spacingX, double spacingY)
        {
            // Standart hasır şablonları
            var templates = new List<WireMeshTemplate>
            {
                new WireMeshTemplate { Name = "Q131", DiameterX = 5, DiameterY = 5, SpacingX = 15, SpacingY = 15, WeightPerSquareMeter = 2.2 },
                new WireMeshTemplate { Name = "Q188", DiameterX = 6, DiameterY = 6, SpacingX = 15, SpacingY = 15, WeightPerSquareMeter = 3.8 },
                new WireMeshTemplate { Name = "Q257", DiameterX = 7, DiameterY = 7, SpacingX = 15, SpacingY = 15, WeightPerSquareMeter = 5.1 },
                new WireMeshTemplate { Name = "Q335", DiameterX = 8, DiameterY = 8, SpacingX = 15, SpacingY = 15, WeightPerSquareMeter = 6.7 }
            };
            
            // En yakın şablonu bul
            return templates.OrderBy(t => 
                Math.Abs(t.SpacingX - spacingX) + Math.Abs(t.SpacingY - spacingY)
            ).FirstOrDefault();
        }
    }
    
    /// <summary>
    /// Çizgi segmenti
    /// </summary>
    public class LineSegment
    {
        public double StartX { get; set; }
        public double StartY { get; set; }
        public double EndX { get; set; }
        public double EndY { get; set; }
        public string LayerName { get; set; } = string.Empty;
        
        public double Length => Math.Sqrt(Math.Pow(EndX - StartX, 2) + Math.Pow(EndY - StartY, 2));
    }
}
