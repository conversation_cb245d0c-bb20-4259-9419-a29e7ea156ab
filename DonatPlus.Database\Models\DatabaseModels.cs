#if NET6_0_OR_GREATER
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
#endif

namespace DonatPlus.Database.Models
{
#if NET6_0_OR_GREATER
    /// <summary>
    /// Proje bilgilerini tutan tablo
    /// </summary>
    [Table("Projects")]
    public class Project
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string ProjectNumber { get; set; } = string.Empty;
        
        [MaxLength(200)]
        public string ClientName { get; set; } = string.Empty;
        
        [MaxLength(300)]
        public string Location { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Engineer { get; set; } = string.Empty;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? ModifiedDate { get; set; }
        
        [MaxLength(50)]
        public string ConcreteClass { get; set; } = "C25";
        
        [MaxLength(50)]
        public string SteelGrade { get; set; } = "S420";
        
        public bool IsActive { get; set; } = true;
        
        // Navigation properties
        public virtual ICollection<ProjectCalculation> Calculations { get; set; } = new List<ProjectCalculation>();
        public virtual ICollection<ProjectDrawing> Drawings { get; set; } = new List<ProjectDrawing>();
    }
    
    /// <summary>
    /// Proje hesaplamalarını tutan tablo
    /// </summary>
    [Table("ProjectCalculations")]
    public class ProjectCalculation
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime CalculationDate { get; set; } = DateTime.Now;
        
        [MaxLength(100)]
        public string CalculatedBy { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalWeight { get; set; }
        
        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalLength { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }
        
        [MaxLength(100)]
        public string SelectedArea { get; set; } = string.Empty;
        
        public string? CalculationData { get; set; } // JSON formatında hesaplama detayları
        
        public bool IsApproved { get; set; } = false;
        
        [MaxLength(100)]
        public string? ApprovedBy { get; set; }
        
        public DateTime? ApprovalDate { get; set; }
        
        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;
        
        public virtual ICollection<CalculationRebar> RebarElements { get; set; } = new List<CalculationRebar>();
        public virtual ICollection<CalculationReport> Reports { get; set; } = new List<CalculationReport>();
    }
    
    /// <summary>
    /// Hesaplama içindeki donatı elemanlarını tutan tablo
    /// </summary>
    [Table("CalculationRebars")]
    public class CalculationRebar
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int CalculationId { get; set; }
        
        [MaxLength(100)]
        public string ElementType { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(5,1)")]
        public decimal Diameter { get; set; }
        
        [Column(TypeName = "decimal(10,3)")]
        public decimal Length { get; set; }
        
        public int Quantity { get; set; }
        
        [Column(TypeName = "decimal(10,3)")]
        public decimal Weight { get; set; }
        
        [MaxLength(50)]
        public string Grade { get; set; } = "S420";
        
        [MaxLength(50)]
        public string Position { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string LayerName { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(15,3)")]
        public decimal StartX { get; set; }
        
        [Column(TypeName = "decimal(15,3)")]
        public decimal StartY { get; set; }
        
        [Column(TypeName = "decimal(15,3)")]
        public decimal EndX { get; set; }
        
        [Column(TypeName = "decimal(15,3)")]
        public decimal EndY { get; set; }
        
        public bool IsWireMesh { get; set; } = false;
        
        [Column(TypeName = "decimal(5,1)")]
        public decimal MeshSpacingX { get; set; }
        
        [Column(TypeName = "decimal(5,1)")]
        public decimal MeshSpacingY { get; set; }
        
        [Column(TypeName = "decimal(10,3)")]
        public decimal MeshWidth { get; set; }
        
        [Column(TypeName = "decimal(10,3)")]
        public decimal MeshHeight { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // Navigation properties
        [ForeignKey("CalculationId")]
        public virtual ProjectCalculation Calculation { get; set; } = null!;
    }
    
    /// <summary>
    /// Proje çizimlerini tutan tablo
    /// </summary>
    [Table("ProjectDrawings")]
    public class ProjectDrawing
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string FileName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string DrawingNumber { get; set; } = string.Empty;
        
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string Scale { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string Revision { get; set; } = string.Empty;
        
        public DateTime UploadDate { get; set; } = DateTime.Now;
        
        public long FileSize { get; set; }
        
        [MaxLength(50)]
        public string FileHash { get; set; } = string.Empty;
        
        public DateTime? LastAnalyzed { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;
    }
    
    /// <summary>
    /// Hesaplama raporlarını tutan tablo
    /// </summary>
    [Table("CalculationReports")]
    public class CalculationReport
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int CalculationId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string ReportType { get; set; } = string.Empty; // YeşilDefter, KeşifÖzeti, KesimListesi, SiparişListesi
        
        [Required]
        [MaxLength(200)]
        public string FileName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;
        
        public DateTime GeneratedDate { get; set; } = DateTime.Now;
        
        [MaxLength(100)]
        public string GeneratedBy { get; set; } = string.Empty;
        
        public long FileSize { get; set; }
        
        [MaxLength(50)]
        public string Format { get; set; } = string.Empty; // Excel, PDF, CSV
        
        public bool IsActive { get; set; } = true;
        
        // Navigation properties
        [ForeignKey("CalculationId")]
        public virtual ProjectCalculation Calculation { get; set; } = null!;
    }
    
    /// <summary>
    /// Çelik hasır şablonlarını tutan tablo
    /// </summary>
    [Table("WireMeshTemplates")]
    public class WireMeshTemplate
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(5,1)")]
        public decimal DiameterX { get; set; }
        
        [Column(TypeName = "decimal(5,1)")]
        public decimal DiameterY { get; set; }
        
        [Column(TypeName = "decimal(5,1)")]
        public decimal SpacingX { get; set; }
        
        [Column(TypeName = "decimal(5,1)")]
        public decimal SpacingY { get; set; }
        
        [Column(TypeName = "decimal(8,3)")]
        public decimal WeightPerSquareMeter { get; set; }
        
        [MaxLength(50)]
        public string Standard { get; set; } = "TS 648";
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }
    
    /// <summary>
    /// Sistem ayarlarını tutan tablo
    /// </summary>
    [Table("SystemSettings")]
    public class SystemSetting
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Key { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        public string Value { get; set; } = string.Empty;
        
        [MaxLength(200)]
        public string Description { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string Category { get; set; } = string.Empty;
        
        public DateTime ModifiedDate { get; set; } = DateTime.Now;
        
        [MaxLength(100)]
        public string ModifiedBy { get; set; } = string.Empty;
    }
#endif
}
