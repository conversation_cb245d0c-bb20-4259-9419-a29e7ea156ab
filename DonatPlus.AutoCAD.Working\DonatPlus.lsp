; DonatPlus AutoCAD LISP Commands 
(defun c:DONATPLUS () 
  (command "NETLOAD" "DonatPlus.AutoCAD.Working.dll") 
  (princ "\\nDonatPlus Plugin yüklendi!") 
  (princ "\\nKomutlar: DONATPLUS, DPANALIZ, DPTEST, DPHELP") 
  (princ) 
) 
 
(defun c:DPANALIZ () 
  (princ "\\nDonatı analizi başlatılıyor...") 
  (princ "\\nMetinleri seçin ve Enter'a basın.") 
  (princ) 
) 
 
(defun c:DPTEST () 
  (princ "\\nDonatPlus test hesaplamaları çalıştırılıyor...") 
  (princ) 
) 
 
(defun c:DPHELP () 
  (princ "\\nDonatPlus Komutları:") 
  (princ "\\nDONATPLUS - Ana menü") 
  (princ "\\nDPANALIZ - Donatı analizi") 
  (princ "\\nDPTEST - Test hesaplama") 
  (princ "\\nDPHELP - Yardım") 
  (princ) 
) 
 
(princ "\\nDonatPlus LISP komutları yüklendi!") 
(princ "\\nKomutlar: DONATPLUS, DPANALIZ, DPTEST, DPHELP") 
