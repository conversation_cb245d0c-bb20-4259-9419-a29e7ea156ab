using DonatPlus.AutoCAD.Services;
using DonatPlus.Core.Models;
using DonatPlus.Core.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DonatPlus.AutoCAD.Services
{
    /// <summary>
    /// AutoCAD entity'lerini analiz eden servis
    /// </summary>
    public class EntityAnalyzerService
    {
        private readonly ILogger<EntityAnalyzerService> _logger;
        private readonly RebarDetectionService _rebarDetection;
        private readonly GeometryCalculationService _geometryCalculation;
        
        public EntityAnalyzerService(
            ILogger<EntityAnalyzerService> logger,
            RebarDetectionService rebarDetection,
            GeometryCalculationService geometryCalculation)
        {
            _logger = logger;
            _rebarDetection = rebarDetection;
            _geometryCalculation = geometryCalculation;
        }
        
        /// <summary>
        /// Entity listesini analiz ederek donatı elemanlarını çıkarır
        /// </summary>
        public List<RebarElement> AnalyzeEntities(List<EntityData> entities)
        {
            var rebarElements = new List<RebarElement>();
            
            try
            {
                _logger.LogInformation("Entity analizi başlatılıyor. Toplam entity sayısı: {Count}", entities.Count);
                
                // Metin entity'lerini analiz et
                var textEntities = entities.Where(e => 
                    e.EntityType == "DBText" || e.EntityType == "MText").ToList();
                
                foreach (var textEntity in textEntities)
                {
                    var rebar = _rebarDetection.ExtractRebarFromText(
                        textEntity.TextContent, 
                        textEntity.Position.X, 
                        textEntity.Position.Y, 
                        textEntity.LayerName);
                    
                    if (rebar != null)
                    {
                        // Yakındaki çizgilerden uzunluk bilgisini tamamla
                        if (rebar.Length == 0)
                        {
                            rebar.Length = FindNearbyLineLength(textEntity, entities);
                        }
                        
                        // Ağırlığı yeniden hesapla
                        if (rebar.Diameter > 0 && rebar.Length > 0)
                        {
                            rebar.Weight = _geometryCalculation.CalculateRebarWeight(rebar.Diameter, rebar.Length);
                            rebarElements.Add(rebar);
                        }
                    }
                }
                
                // Çizgi entity'lerini analiz et (paralel donatılar için)
                var lineEntities = entities.Where(e => 
                    e.EntityType == "Line" || e.EntityType == "Polyline").ToList();
                
                var parallelGroups = GroupParallelLines(lineEntities);
                foreach (var group in parallelGroups)
                {
                    var rebar = AnalyzeParallelLines(group);
                    if (rebar != null)
                    {
                        rebarElements.Add(rebar);
                    }
                }
                
                // Block entity'lerini analiz et
                var blockEntities = entities.Where(e => e.EntityType == "BlockReference").ToList();
                foreach (var blockEntity in blockEntities)
                {
                    var rebar = AnalyzeBlock(blockEntity);
                    if (rebar != null)
                    {
                        rebarElements.Add(rebar);
                    }
                }
                
                _logger.LogInformation("Entity analizi tamamlandı. Bulunan donatı sayısı: {Count}", rebarElements.Count);
                
                return rebarElements;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Entity analizi sırasında hata");
                return rebarElements;
            }
        }
        
        /// <summary>
        /// Çelik hasır analizi
        /// </summary>
        public List<RebarElement> AnalyzeWireMesh(List<EntityData> entities)
        {
            var wireMeshElements = new List<RebarElement>();
            
            try
            {
                // Yatay ve dikey çizgileri ayır
                var horizontalLines = new List<LineSegment>();
                var verticalLines = new List<LineSegment>();
                
                var lineEntities = entities.Where(e => 
                    e.EntityType == "Line" || e.EntityType == "Polyline").ToList();
                
                foreach (var entity in lineEntities)
                {
                    if (entity.EntityType == "Line")
                    {
                        var line = new LineSegment
                        {
                            StartX = entity.StartPoint.X,
                            StartY = entity.StartPoint.Y,
                            EndX = entity.EndPoint.X,
                            EndY = entity.EndPoint.Y,
                            LayerName = entity.LayerName
                        };
                        
                        if (IsHorizontalLine(line))
                            horizontalLines.Add(line);
                        else if (IsVerticalLine(line))
                            verticalLines.Add(line);
                    }
                }
                
                // Hasır algıla
                if (horizontalLines.Count >= 2 && verticalLines.Count >= 2)
                {
                    var wireMesh = _rebarDetection.DetectWireMesh(horizontalLines, verticalLines, "HASIR");
                    if (wireMesh != null)
                    {
                        wireMeshElements.Add(wireMesh);
                    }
                }
                
                return wireMeshElements;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hasır analizi sırasında hata");
                return wireMeshElements;
            }
        }
        
        /// <summary>
        /// Yakındaki çizgi uzunluğunu bulur
        /// </summary>
        private double FindNearbyLineLength(EntityData textEntity, List<EntityData> entities)
        {
            const double searchRadius = 1000; // mm
            
            var nearbyLines = entities.Where(e => 
                (e.EntityType == "Line" || e.EntityType == "Polyline") &&
                GetDistance(textEntity.Position, e.StartPoint) < searchRadius)
                .ToList();
            
            if (nearbyLines.Any())
            {
                var closestLine = nearbyLines.OrderBy(l => 
                    GetDistance(textEntity.Position, l.StartPoint)).First();
                return closestLine.Length / 1000; // mm'den m'ye çevir
            }
            
            return 0;
        }
        
        /// <summary>
        /// Paralel çizgileri gruplar
        /// </summary>
        private List<List<EntityData>> GroupParallelLines(List<EntityData> lineEntities)
        {
            var groups = new List<List<EntityData>>();
            const double angleTolerance = 0.1; // radyan
            
            foreach (var line in lineEntities)
            {
                bool addedToGroup = false;
                var lineAngle = CalculateLineAngle(line);
                
                foreach (var group in groups)
                {
                    var groupAngle = CalculateLineAngle(group.First());
                    if (Math.Abs(lineAngle - groupAngle) < angleTolerance)
                    {
                        group.Add(line);
                        addedToGroup = true;
                        break;
                    }
                }
                
                if (!addedToGroup)
                {
                    groups.Add(new List<EntityData> { line });
                }
            }
            
            return groups.Where(g => g.Count > 1).ToList(); // Sadece paralel grupları döndür
        }
        
        /// <summary>
        /// Paralel çizgi grubunu analiz eder
        /// </summary>
        private RebarElement? AnalyzeParallelLines(List<EntityData> parallelLines)
        {
            try
            {
                if (parallelLines.Count < 2)
                    return null;
                
                var firstLine = parallelLines.First();
                var averageLength = parallelLines.Average(l => l.Length) / 1000; // m'ye çevir
                
                // Çap bilgisini layer adından veya yakındaki metinlerden tahmin et
                var estimatedDiameter = EstimateDiameterFromLayer(firstLine.LayerName);
                
                var rebar = new RebarElement
                {
                    Diameter = estimatedDiameter,
                    Length = averageLength,
                    Quantity = parallelLines.Count,
                    ElementType = DetermineElementTypeFromLayer(firstLine.LayerName),
                    LayerName = firstLine.LayerName,
                    StartX = firstLine.StartPoint.X,
                    StartY = firstLine.StartPoint.Y,
                    EndX = firstLine.EndPoint.X,
                    EndY = firstLine.EndPoint.Y,
                    Description = $"Paralel donatı grubu - {parallelLines.Count} adet"
                };
                
                if (rebar.Diameter > 0 && rebar.Length > 0)
                {
                    rebar.Weight = _geometryCalculation.CalculateRebarWeight(rebar.Diameter, rebar.Length);
                    return rebar;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Paralel çizgi analizi hatası");
                return null;
            }
        }
        
        /// <summary>
        /// Block entity'sini analiz eder
        /// </summary>
        private RebarElement? AnalyzeBlock(EntityData blockEntity)
        {
            try
            {
                // Block adından donatı bilgisi çıkarmaya çalış
                var blockName = blockEntity.BlockName.ToLowerInvariant();
                
                if (blockName.Contains("donat") || blockName.Contains("rebar") || 
                    blockName.Contains("ø") || blockName.Contains("phi"))
                {
                    var rebar = new RebarElement
                    {
                        StartX = blockEntity.Position.X,
                        StartY = blockEntity.Position.Y,
                        LayerName = blockEntity.LayerName,
                        ElementType = DetermineElementTypeFromLayer(blockEntity.LayerName),
                        Description = $"Block: {blockEntity.BlockName}",
                        Quantity = 1
                    };
                    
                    // Attribute'lardan bilgi çıkar
                    foreach (var attr in blockEntity.Attributes)
                    {
                        if (attr.Key.ToLowerInvariant().Contains("diameter") || 
                            attr.Key.ToLowerInvariant().Contains("çap"))
                        {
                            if (double.TryParse(attr.Value, out double diameter))
                            {
                                rebar.Diameter = diameter;
                            }
                        }
                        else if (attr.Key.ToLowerInvariant().Contains("length") || 
                                attr.Key.ToLowerInvariant().Contains("uzunluk"))
                        {
                            if (double.TryParse(attr.Value, out double length))
                            {
                                rebar.Length = length / 100; // cm'den m'ye
                            }
                        }
                        else if (attr.Key.ToLowerInvariant().Contains("quantity") || 
                                attr.Key.ToLowerInvariant().Contains("adet"))
                        {
                            if (int.TryParse(attr.Value, out int quantity))
                            {
                                rebar.Quantity = quantity;
                            }
                        }
                    }
                    
                    // Eksik bilgileri tahmin et
                    if (rebar.Diameter == 0)
                    {
                        rebar.Diameter = EstimateDiameterFromLayer(rebar.LayerName);
                    }
                    
                    if (rebar.Diameter > 0 && rebar.Length > 0)
                    {
                        rebar.Weight = _geometryCalculation.CalculateRebarWeight(rebar.Diameter, rebar.Length);
                        return rebar;
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Block analizi hatası");
                return null;
            }
        }
        
        private double GetDistance(Autodesk.AutoCAD.Geometry.Point3d p1, Autodesk.AutoCAD.Geometry.Point3d p2)
        {
            return Math.Sqrt(Math.Pow(p2.X - p1.X, 2) + Math.Pow(p2.Y - p1.Y, 2));
        }
        
        private double CalculateLineAngle(EntityData line)
        {
            return Math.Atan2(line.EndPoint.Y - line.StartPoint.Y, line.EndPoint.X - line.StartPoint.X);
        }
        
        private bool IsHorizontalLine(LineSegment line, double tolerance = 0.1)
        {
            var angle = Math.Atan2(line.EndY - line.StartY, line.EndX - line.StartX);
            return Math.Abs(angle) < tolerance || Math.Abs(Math.Abs(angle) - Math.PI) < tolerance;
        }
        
        private bool IsVerticalLine(LineSegment line, double tolerance = 0.1)
        {
            var angle = Math.Atan2(line.EndY - line.StartY, line.EndX - line.StartX);
            return Math.Abs(Math.Abs(angle) - Math.PI / 2) < tolerance;
        }
        
        private double EstimateDiameterFromLayer(string layerName)
        {
            var layer = layerName.ToLowerInvariant();
            
            // Layer adından çap tahmin et
            if (layer.Contains("8")) return 8;
            if (layer.Contains("10")) return 10;
            if (layer.Contains("12")) return 12;
            if (layer.Contains("14")) return 14;
            if (layer.Contains("16")) return 16;
            if (layer.Contains("18")) return 18;
            if (layer.Contains("20")) return 20;
            if (layer.Contains("22")) return 22;
            if (layer.Contains("25")) return 25;
            
            // Element tipine göre varsayılan çap
            if (layer.Contains("kiriş") || layer.Contains("beam")) return 16;
            if (layer.Contains("kolon") || layer.Contains("column")) return 20;
            if (layer.Contains("döşeme") || layer.Contains("slab")) return 12;
            if (layer.Contains("perde") || layer.Contains("wall")) return 14;
            
            return 12; // Varsayılan çap
        }
        
        private string DetermineElementTypeFromLayer(string layerName)
        {
            var layer = layerName.ToLowerInvariant();
            
            if (layer.Contains("kiriş") || layer.Contains("beam")) return "Kiriş";
            if (layer.Contains("kolon") || layer.Contains("column")) return "Kolon";
            if (layer.Contains("döşeme") || layer.Contains("slab")) return "Döşeme";
            if (layer.Contains("perde") || layer.Contains("wall")) return "Perde";
            if (layer.Contains("hasır") || layer.Contains("mesh")) return "Hasır";
            
            return "Diğer";
        }
    }
}
